# 📊 Руководство по сбору данных в Package Analyzer

## 🎯 Обзор функционала

Визуальная панель управления теперь включает полноценный функционал для сбора информации о npm пакетах в базу данных. Все операции выполняются через удобный веб-интерфейс с отображением прогресса в реальном времени.

## 🎛️ Интерфейс сбора данных

### Секция "Сбор данных"
Расположена в главной панели управления и содержит:

#### 🔄 Основные операции
- **Добавить пакеты** - автоматический сбор популярных пакетов
- **Обновить существующие** - обновление информации о пакетах в БД
- **Популярные пакеты** - сбор топ-1000 популярных пакетов
- **По категориям** - сбор пакетов по заданным категориям

#### ➕ Ручное добавление
- **Конкретный пакет** - добавление одного пакета по названию
- **Список пакетов** - массовое добавление через текстовое поле

## 🚀 Способы сбора данных

### 1. Автоматический сбор популярных пакетов
```
Кнопка: "Добавить пакеты"
API: POST /api/collect/auto
Описание: Собирает 100 самых популярных npm пакетов
```

### 2. Сбор топ-популярных пакетов
```
Кнопка: "Популярные пакеты"
API: POST /api/collect/popular
Параметры: { limit: 1000 }
Описание: Собирает указанное количество популярных пакетов
```

### 3. Сбор по категориям
```
Кнопка: "По категориям"
API: POST /api/collect/categories
Категории: framework, library, utility, build-tool, testing, react, vue, angular, node, webpack, babel
Описание: Собирает пакеты по предустановленным категориям
```

### 4. Добавление конкретного пакета
```
Поле ввода + кнопка "Добавить"
API: POST /api/collect/package
Параметры: { packageName: "react" }
Описание: Добавляет один конкретный пакет
```

### 5. Массовое добавление пакетов
```
Текстовое поле + кнопка "Добавить список"
API: POST /api/collect/packages
Формат: Один пакет на строку
Описание: Добавляет несколько пакетов одновременно
```

### 6. Обновление существующих пакетов
```
Кнопка: "Обновить существующие"
API: POST /api/collect/update
Описание: Обновляет информацию о всех пакетах в БД
```

## 📈 Мониторинг процесса

### Прогресс в реальном времени
- **Прогресс-бар** с процентами выполнения
- **Детали задачи** - текущий обрабатываемый пакет
- **Логи системы** - подробная информация о процессе

### WebSocket обновления
- `progress` - обновление прогресса задачи
- `task` - изменение статуса задачи
- `task_complete` - завершение задачи
- `log` - новые записи в логах

### Статусы задач
- **pending** - ожидание выполнения
- **running** - выполняется
- **completed** - завершено успешно
- **failed** - завершено с ошибкой
- **stopped** - остановлено пользователем

## 🗄️ Собираемые данные

### Основная информация о пакете
```json
{
  "name": "react",
  "description": "A JavaScript library for building user interfaces",
  "repository": "https://github.com/facebook/react",
  "homepage": "https://reactjs.org/",
  "license": "MIT",
  "latest_version": "18.2.0"
}
```

### Версии пакета
```json
{
  "versions": ["18.2.0", "18.1.0", "18.0.0", "17.0.2"],
  "dependencies": {
    "loose-envify": "^1.1.0"
  }
}
```

## 🔧 API Endpoints

### Сбор данных
- `POST /api/collect/auto` - автоматический сбор
- `POST /api/collect/package` - один пакет
- `POST /api/collect/packages` - список пакетов
- `POST /api/collect/popular` - популярные пакеты
- `POST /api/collect/categories` - по категориям
- `POST /api/collect/update` - обновление существующих

### Управление задачами
- `GET /api/tasks` - список задач
- `POST /api/tasks/stop` - остановка всех задач

### Просмотр данных
- `GET /api/packages` - список пакетов с пагинацией
- `GET /api/packages/:name` - детали пакета
- `PUT /api/packages/:name` - обновление пакета

## ⚡ Производительность

### Оптимизация запросов
- **Таймаут**: 10 секунд на запрос к npm registry
- **Задержка**: 100мс между запросами (защита от rate limiting)
- **Retry**: Автоматический пропуск неудачных пакетов
- **Прогресс**: Обновление каждые 1% выполнения

### GPU ускорение
- **Анализ совместимости** версий
- **Поиск циклических зависимостей**
- **Матричные вычисления** для больших объемов данных

## 🛡️ Обработка ошибок

### Типы ошибок
- **Сетевые ошибки** - таймауты, недоступность npm registry
- **Ошибки парсинга** - некорректные данные пакета
- **Ошибки БД** - проблемы с записью в SQLite

### Стратегия восстановления
- **Пропуск проблемных пакетов** с логированием
- **Продолжение выполнения** несмотря на отдельные ошибки
- **Детальное логирование** для диагностики

## 📊 Примеры использования

### Быстрый старт
1. Откройте панель управления: `http://localhost:3000`
2. Нажмите "Популярные пакеты" для сбора топ-пакетов
3. Наблюдайте прогресс в секции "Очередь задач"
4. Просматривайте результаты в "База данных пакетов"

### Добавление конкретных пакетов
1. Введите название в поле "Название пакета"
2. Нажмите "Добавить" или Enter
3. Пакет будет добавлен в очередь на обработку

### Массовое добавление
1. Введите список пакетов в текстовое поле (по одному на строку)
2. Нажмите "Добавить список"
3. Все пакеты будут обработаны последовательно

## 🔍 Мониторинг и отладка

### Логи системы
- **Уровни**: INFO, SUCCESS, WARNING, ERROR
- **Временные метки** для каждого события
- **Автопрокрутка** к новым сообщениям
- **Фильтрация** и очистка логов

### Статистика
- **Общее количество пакетов** в базе данных
- **Скорость обработки** пакетов в секунду
- **Использование ресурсов** CPU и памяти
- **Статус GPU** и CUDA

## 🚀 Планы развития

### Ближайшие улучшения
- **Планировщик задач** - автоматический сбор по расписанию
- **Фильтры сбора** - исключение определенных пакетов
- **Приоритеты** - важные пакеты обрабатываются первыми
- **Экспорт данных** - выгрузка в JSON/CSV

### Интеграции
- **npm API** - получение статистики загрузок
- **GitHub API** - информация о репозиториях
- **Snyk API** - данные о безопасности
- **Bundlephobia** - размеры пакетов

Функционал сбора данных полностью интегрирован в визуальную панель управления и готов к использованию! 🎉
