// Скрипт для проверки структуры базы данных
const Database = require('./db/database');

async function checkDatabaseStructure() {
  console.log('🔍 Проверка структуры базы данных...');
  
  try {
    const db = new Database();
    await db.initialize();
    
    console.log('📊 Структура таблицы packages:');
    
    db.db.all("PRAGMA table_info(packages)", (err, columns) => {
      if (err) {
        console.error('❌ Ошибка проверки структуры:', err);
        return;
      }
      
      console.log(`📈 Всего колонок: ${columns.length}`);
      console.log('📋 Список колонок:');
      
      columns.forEach((col, index) => {
        const nullable = col.notnull ? 'NOT NULL' : 'NULL';
        const defaultVal = col.dflt_value ? ` DEFAULT ${col.dflt_value}` : '';
        console.log(`   ${index + 1}. ${col.name}: ${col.type} ${nullable}${defaultVal}`);
      });
      
      // Создаем правильный SQL запрос
      const columnNames = columns.map(col => col.name);
      const placeholders = columnNames.map(() => '?').join(', ');
      
      console.log('\n📝 Правильный SQL запрос:');
      console.log(`INSERT OR REPLACE INTO packages (${columnNames.join(', ')}) VALUES (${placeholders})`);
      
      console.log('\n🔧 Количество значений должно быть:', columns.length);
      
      // Закрываем соединение
      db.db.close((err) => {
        if (err) {
          console.error('Ошибка закрытия БД:', err);
        } else {
          console.log('✅ Проверка завершена');
        }
        process.exit(0);
      });
    });
    
  } catch (error) {
    console.error('❌ Ошибка проверки:', error.message);
    process.exit(1);
  }
}

// Запускаем проверку
if (require.main === module) {
  checkDatabaseStructure().catch(error => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
}

module.exports = { checkDatabaseStructure };
