#!/usr/bin/env node
const PackageAnalyzer = require('./index');
const path = require('path');
const fs = require('fs');

const args = process.argv.slice(2);
const command = args[0];
const packagePath = args[1] || './package.json';
const options = {
  useGPU: !args.includes('--no-gpu'),
  verbose: args.includes('--verbose')
};

if (!command) {
  console.error('Использование: package-analyzer <команда> [путь к package.json] [опции]');
  console.error('Команды:');
  console.error('  analyze - Анализ зависимостей пакета');
  console.error('  resolve - Разрешение конфликтов зависимостей');
  console.error('  scan-all - Сканирование всех пакетов npm');
  console.error('Опции:');
  console.error('  --no-gpu - Отключить использование GPU');
  console.error('  --verbose - Подробный вывод');
  process.exit(1);
}

const analyzer = new PackageAnalyzer();

async function run() {
  try {
    if (command === 'scan-all') {
      console.log('Запуск полного сканирования пакетов npm...');
      const scanner = require('./scripts/full-scan');
      await scanner.startFullScan();
      return;
    }

    if (!fs.existsSync(packagePath)) {
      throw new Error(`Файл не найден: ${packagePath}`);
    }

    if (command === 'analyze') {
      console.log(`Анализ пакета ${packagePath}${options.useGPU ? ' с использованием GPU' : ''}...`);
      const result = await analyzer.analyzePackage(packagePath);
      
      console.log('\nРезультаты анализа:');
      console.log('-------------------');
      console.log(`Имя пакета: ${result.name}`);
      console.log(`Версия: ${result.version}`);
      console.log(`Всего зависимостей: ${result.dependenciesCount}`);
      
      if (result.cyclicDependencies && result.cyclicDependencies.length > 0) {
        console.log('\nОбнаружены циклические зависимости:');
        result.cyclicDependencies.forEach(pkg => {
          console.log(`- ${pkg}`);
        });
      }
      
      if (result.potentialIssues && result.potentialIssues.length > 0) {
        console.log('\nПотенциальные проблемы:');
        result.potentialIssues.forEach(issue => {
          console.log(`- ${issue.package}@${issue.version}: ${issue.issue}`);
        });
      }
      
      if (options.verbose && result.compatibilityMatrix) {
        console.log('\nМатрица совместимости (GPU-анализ):');
        console.log(result.compatibilityMatrix);
      }
    } 
    else if (command === 'resolve') {
      const { resolved, message } = await analyzer.resolveConflicts(packagePath);
      console.log(message);
      if (resolved) {
        console.log('Обновлен package.json с совместимыми версиями');
      }
    }
    else {
      console.error(`Неизвестная команда: ${command}`);
      process.exit(1);
    }
  } catch (error) {
    console.error('Ошибка:', error.message);
    process.exit(1);
  }
}

run();
