const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class Database {
  constructor() {
    this.dbPath = path.join(__dirname, 'packages.db');
    this.db = new sqlite3.Database(this.dbPath);
  }

  async initialize() {
    return new Promise((resolve, reject) => {
      this.db.serialize(() => {
        // Основная таблица пакетов
        this.db.run(`CREATE TABLE IF NOT EXISTS packages (
          name TEXT PRIMARY KEY,
          description TEXT,
          repository TEXT,
          homepage TEXT,
          license TEXT,
          latest_version TEXT,
          author TEXT,
          keywords TEXT,
          download_count INTEGER DEFAULT 0,
          bundle_size INTEGER DEFAULT 0,
          gzip_size INTEGER DEFAULT 0,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`);

        // Таблица версий пакетов
        this.db.run(`CREATE TABLE IF NOT EXISTS versions (
          package_name TEXT,
          version TEXT,
          dependencies TEXT,
          dev_dependencies TEXT,
          peer_dependencies TEXT,
          optional_dependencies TEXT,
          published_at TIMESTAMP,
          deprecated BOOLEAN DEFAULT FALSE,
          PRIMARY KEY (package_name, version),
          FOREIGN KEY (package_name) REFERENCES packages(name)
        )`);

        // Таблица зависимостей (нормализованная)
        this.db.run(`CREATE TABLE IF NOT EXISTS dependencies (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          package_name TEXT,
          version TEXT,
          dependency_name TEXT,
          dependency_version TEXT,
          dependency_type TEXT, -- 'dependencies', 'devDependencies', 'peerDependencies', 'optionalDependencies'
          FOREIGN KEY (package_name, version) REFERENCES versions(package_name, version)
        )`);

        // Таблица метрик пакетов
        this.db.run(`CREATE TABLE IF NOT EXISTS package_metrics (
          package_name TEXT PRIMARY KEY,
          weekly_downloads INTEGER DEFAULT 0,
          monthly_downloads INTEGER DEFAULT 0,
          yearly_downloads INTEGER DEFAULT 0,
          github_stars INTEGER DEFAULT 0,
          github_forks INTEGER DEFAULT 0,
          github_issues INTEGER DEFAULT 0,
          last_commit_date TIMESTAMP,
          maintenance_score REAL DEFAULT 0,
          popularity_score REAL DEFAULT 0,
          quality_score REAL DEFAULT 0,
          FOREIGN KEY (package_name) REFERENCES packages(name)
        )`);

        // Таблица уязвимостей
        this.db.run(`CREATE TABLE IF NOT EXISTS vulnerabilities (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          package_name TEXT,
          version_range TEXT,
          severity TEXT, -- 'critical', 'high', 'medium', 'low'
          title TEXT,
          description TEXT,
          cve_id TEXT,
          fixed_in TEXT,
          reported_at TIMESTAMP,
          FOREIGN KEY (package_name) REFERENCES packages(name)
        )`);

        resolve();
      });
    });
  }

  async updatePackageInfo(packageName, packageInfo) {
    return new Promise((resolve, reject) => {
      this.db.serialize(() => {
        // Обновляем основную информацию о пакете
        this.db.run(
          `INSERT OR REPLACE INTO packages
          (name, description, repository, homepage, license, latest_version, author, keywords, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))`,
          [
            packageName,
            packageInfo.description,
            packageInfo.repository,
            packageInfo.homepage,
            packageInfo.license,
            packageInfo.latest_version,
            packageInfo.author,
            packageInfo.keywords ? JSON.stringify(packageInfo.keywords) : null
          ],
          (err) => {
            if (err) return reject(err);

            // Добавляем версии пакета (если они переданы)
            if (packageInfo.versions && packageInfo.versions.length > 0) {
              this.saveVersionsData(packageName, packageInfo.versions, resolve, reject);
            } else {
              resolve();
            }
          }
        );
      });
    });
  }

  // Сохранение данных о версиях
  async saveVersionsData(packageName, versionsData, resolve, reject) {
    try {
      const versionStmt = this.db.prepare(
        `INSERT OR REPLACE INTO versions
        (package_name, version, dependencies, dev_dependencies, peer_dependencies, optional_dependencies, published_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)`
      );

      const depStmt = this.db.prepare(
        `INSERT OR REPLACE INTO dependencies
        (package_name, version, dependency_name, dependency_version, dependency_type)
        VALUES (?, ?, ?, ?, ?)`
      );

      for (const versionData of versionsData) {
        const version = typeof versionData === 'string' ? versionData : versionData.version;

        // Сохраняем информацию о версии
        versionStmt.run([
          packageName,
          version,
          JSON.stringify(versionData.dependencies || {}),
          JSON.stringify(versionData.devDependencies || {}),
          JSON.stringify(versionData.peerDependencies || {}),
          JSON.stringify(versionData.optionalDependencies || {}),
          versionData.published_at || new Date().toISOString()
        ]);

        // Сохраняем нормализованные зависимости
        const depTypes = [
          { deps: versionData.dependencies, type: 'dependencies' },
          { deps: versionData.devDependencies, type: 'devDependencies' },
          { deps: versionData.peerDependencies, type: 'peerDependencies' },
          { deps: versionData.optionalDependencies, type: 'optionalDependencies' }
        ];

        for (const { deps, type } of depTypes) {
          if (deps && typeof deps === 'object') {
            for (const [depName, depVersion] of Object.entries(deps)) {
              depStmt.run([packageName, version, depName, depVersion, type]);
            }
          }
        }
      }

      versionStmt.finalize();
      depStmt.finalize();
      resolve();
    } catch (error) {
      reject(error);
    }
  }

  // Получение детальной информации о пакете
  async getPackageDetails(packageName) {
    return new Promise((resolve, reject) => {
      // Получаем основную информацию о пакете
      this.db.get(
        `SELECT * FROM packages WHERE name = ?`,
        [packageName],
        (err, packageData) => {
          if (err) return reject(err);
          if (!packageData) return resolve(null);

          // Получаем версии с зависимостями
          this.db.all(
            `SELECT v.*,
             GROUP_CONCAT(d.dependency_name || ':' || d.dependency_version || ':' || d.dependency_type) as deps
             FROM versions v
             LEFT JOIN dependencies d ON v.package_name = d.package_name AND v.version = d.version
             WHERE v.package_name = ?
             GROUP BY v.package_name, v.version
             ORDER BY v.published_at DESC`,
            [packageName],
            (err, versions) => {
              if (err) return reject(err);

              // Обрабатываем зависимости
              const processedVersions = versions.map(version => {
                const dependencies = {};
                const devDependencies = {};
                const peerDependencies = {};
                const optionalDependencies = {};

                if (version.deps) {
                  version.deps.split(',').forEach(dep => {
                    const [name, ver, type] = dep.split(':');
                    if (name && ver && type) {
                      switch (type) {
                        case 'dependencies':
                          dependencies[name] = ver;
                          break;
                        case 'devDependencies':
                          devDependencies[name] = ver;
                          break;
                        case 'peerDependencies':
                          peerDependencies[name] = ver;
                          break;
                        case 'optionalDependencies':
                          optionalDependencies[name] = ver;
                          break;
                      }
                    }
                  });
                }

                return {
                  version: version.version,
                  published_at: version.published_at,
                  deprecated: version.deprecated,
                  dependencies,
                  devDependencies,
                  peerDependencies,
                  optionalDependencies
                };
              });

              // Получаем метрики
              this.db.get(
                `SELECT * FROM package_metrics WHERE package_name = ?`,
                [packageName],
                (err, metrics) => {
                  if (err) return reject(err);

                  // Получаем уязвимости
                  this.db.all(
                    `SELECT * FROM vulnerabilities WHERE package_name = ? ORDER BY reported_at DESC`,
                    [packageName],
                    (err, vulnerabilities) => {
                      if (err) return reject(err);

                      resolve({
                        ...packageData,
                        keywords: packageData.keywords ? JSON.parse(packageData.keywords) : [],
                        versions: processedVersions,
                        metrics: metrics || {},
                        vulnerabilities: vulnerabilities || []
                      });
                    }
                  );
                }
              );
            }
          );
        }
      );
    });
  }

  // Получение списка пакетов с пагинацией
  async getPackagesList(search = '', limit = 20, offset = 0) {
    return new Promise((resolve, reject) => {
      const searchCondition = search ? `WHERE name LIKE '%${search}%' OR description LIKE '%${search}%'` : '';

      this.db.get(
        `SELECT COUNT(*) as total FROM packages ${searchCondition}`,
        (err, countResult) => {
          if (err) return reject(err);

          this.db.all(
            `SELECT p.*,
             COUNT(v.version) as version_count,
             COUNT(d.dependency_name) as dependency_count
             FROM packages p
             LEFT JOIN versions v ON p.name = v.package_name
             LEFT JOIN dependencies d ON p.name = d.package_name AND d.dependency_type = 'dependencies'
             ${searchCondition}
             GROUP BY p.name
             ORDER BY p.updated_at DESC
             LIMIT ? OFFSET ?`,
            [limit, offset],
            (err, packages) => {
              if (err) return reject(err);

              resolve({
                packages: packages.map(pkg => ({
                  ...pkg,
                  keywords: pkg.keywords ? JSON.parse(pkg.keywords) : []
                })),
                pagination: {
                  total: countResult.total,
                  limit,
                  offset,
                  page: Math.floor(offset / limit) + 1,
                  pages: Math.ceil(countResult.total / limit)
                }
              });
            }
          );
        }
      );
    });
  }

  // Анализ зависимостей для package.json
  async analyzeDependencies(dependencies, devDependencies = {}) {
    return new Promise((resolve, reject) => {
      const allDeps = { ...dependencies, ...devDependencies };
      const depNames = Object.keys(allDeps);

      if (depNames.length === 0) {
        return resolve({
          totalDependencies: 0,
          knownPackages: [],
          unknownPackages: [],
          potentialIssues: [],
          recommendations: []
        });
      }

      const placeholders = depNames.map(() => '?').join(',');

      this.db.all(
        `SELECT p.name, p.latest_version, p.license, p.description,
         v.vulnerabilities_count
         FROM packages p
         LEFT JOIN (
           SELECT package_name, COUNT(*) as vulnerabilities_count
           FROM vulnerabilities
           GROUP BY package_name
         ) v ON p.name = v.package_name
         WHERE p.name IN (${placeholders})`,
        depNames,
        (err, knownPackages) => {
          if (err) return reject(err);

          const knownNames = knownPackages.map(pkg => pkg.name);
          const unknownPackages = depNames.filter(name => !knownNames.includes(name));

          // Анализируем потенциальные проблемы
          const potentialIssues = [];
          const recommendations = [];

          knownPackages.forEach(pkg => {
            const requestedVersion = allDeps[pkg.name];

            // Проверяем уязвимости
            if (pkg.vulnerabilities_count > 0) {
              potentialIssues.push({
                package: pkg.name,
                type: 'security',
                severity: 'high',
                message: `Найдено ${pkg.vulnerabilities_count} уязвимостей`,
                recommendation: `Обновите до последней версии ${pkg.latest_version}`
              });
            }

            // Проверяем устаревшие версии
            if (requestedVersion && pkg.latest_version) {
              const isOutdated = this.isVersionOutdated(requestedVersion, pkg.latest_version);
              if (isOutdated) {
                recommendations.push({
                  package: pkg.name,
                  type: 'update',
                  current: requestedVersion,
                  latest: pkg.latest_version,
                  message: `Доступна новая версия ${pkg.latest_version}`
                });
              }
            }
          });

          resolve({
            totalDependencies: depNames.length,
            knownPackages,
            unknownPackages,
            potentialIssues,
            recommendations,
            analysis: {
              securityIssues: potentialIssues.filter(i => i.type === 'security').length,
              outdatedPackages: recommendations.filter(r => r.type === 'update').length,
              unknownPackages: unknownPackages.length
            }
          });
        }
      );
    });
  }

  // Простая проверка устаревших версий
  isVersionOutdated(current, latest) {
    // Упрощенная проверка - в реальности нужен semver
    const cleanCurrent = current.replace(/[^0-9.]/g, '');
    const cleanLatest = latest.replace(/[^0-9.]/g, '');
    return cleanCurrent !== cleanLatest;
  }

  close() {
    this.db.close();
  }
}

module.exports = Database;
