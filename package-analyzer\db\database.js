const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class Database {
  constructor() {
    this.dbPath = path.join(__dirname, 'packages.db');
    this.db = new sqlite3.Database(this.dbPath);
  }

  async initialize() {
    return new Promise((resolve, reject) => {
      this.db.serialize(() => {
        // Основная таблица пакетов (расширенная схема)
        this.db.run(`CREATE TABLE IF NOT EXISTS packages (
          name TEXT PRIMARY KEY,
          description TEXT,
          repository TEXT,
          homepage TEXT,
          license TEXT,
          latest_version TEXT,
          author TEXT,
          maintainers TEXT,
          contributors TEXT,
          keywords TEXT,
          readme TEXT,
          changelog TEXT,
          bugs_url TEXT,
          npm_url TEXT,
          download_count INTEGER DEFAULT 0,
          weekly_downloads INTEGER DEFAULT 0,
          monthly_downloads INTEGER DEFAULT 0,
          yearly_downloads INTEGER DEFAULT 0,
          bundle_size INTEGER DEFAULT 0,
          gzip_size INTEGER DEFAULT 0,
          unpacked_size INTEGER DEFAULT 0,
          file_count INTEGER DEFAULT 0,
          has_typescript BOOLEAN DEFAULT 0,
          has_tests BOOLEAN DEFAULT 0,
          test_coverage REAL DEFAULT 0,
          quality_score REAL DEFAULT 0,
          popularity_score REAL DEFAULT 0,
          maintenance_score REAL DEFAULT 0,
          github_stars INTEGER DEFAULT 0,
          github_forks INTEGER DEFAULT 0,
          github_issues INTEGER DEFAULT 0,
          github_watchers INTEGER DEFAULT 0,
          last_commit_date TIMESTAMP,
          first_release_date TIMESTAMP,
          last_release_date TIMESTAMP,
          release_frequency REAL DEFAULT 0,
          version_count INTEGER DEFAULT 0,
          dependency_count INTEGER DEFAULT 0,
          dev_dependency_count INTEGER DEFAULT 0,
          peer_dependency_count INTEGER DEFAULT 0,
          optional_dependency_count INTEGER DEFAULT 0,
          engines TEXT,
          os_compatibility TEXT,
          cpu_compatibility TEXT,
          funding TEXT,
          sponsors TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`);

        // Таблица версий пакетов
        this.db.run(`CREATE TABLE IF NOT EXISTS versions (
          package_name TEXT,
          version TEXT,
          dependencies TEXT,
          dev_dependencies TEXT,
          peer_dependencies TEXT,
          optional_dependencies TEXT,
          published_at TIMESTAMP,
          deprecated BOOLEAN DEFAULT FALSE,
          PRIMARY KEY (package_name, version),
          FOREIGN KEY (package_name) REFERENCES packages(name)
        )`);

        // Таблица зависимостей (нормализованная)
        this.db.run(`CREATE TABLE IF NOT EXISTS dependencies (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          package_name TEXT,
          version TEXT,
          dependency_name TEXT,
          dependency_version TEXT,
          dependency_type TEXT, -- 'dependencies', 'devDependencies', 'peerDependencies', 'optionalDependencies'
          FOREIGN KEY (package_name, version) REFERENCES versions(package_name, version)
        )`);

        // Таблица метрик пакетов
        this.db.run(`CREATE TABLE IF NOT EXISTS package_metrics (
          package_name TEXT PRIMARY KEY,
          weekly_downloads INTEGER DEFAULT 0,
          monthly_downloads INTEGER DEFAULT 0,
          yearly_downloads INTEGER DEFAULT 0,
          github_stars INTEGER DEFAULT 0,
          github_forks INTEGER DEFAULT 0,
          github_issues INTEGER DEFAULT 0,
          last_commit_date TIMESTAMP,
          maintenance_score REAL DEFAULT 0,
          popularity_score REAL DEFAULT 0,
          quality_score REAL DEFAULT 0,
          FOREIGN KEY (package_name) REFERENCES packages(name)
        )`);

        // Таблица уязвимостей
        this.db.run(`CREATE TABLE IF NOT EXISTS vulnerabilities (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          package_name TEXT,
          version_range TEXT,
          severity TEXT, -- 'critical', 'high', 'medium', 'low'
          title TEXT,
          description TEXT,
          cve_id TEXT,
          fixed_in TEXT,
          reported_at TIMESTAMP,
          FOREIGN KEY (package_name) REFERENCES packages(name)
        )`);

        // Таблица совместимости версий
        this.db.run(`CREATE TABLE IF NOT EXISTS version_compatibility (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          package_name TEXT,
          version1 TEXT,
          version2 TEXT,
          compatibility_status TEXT, -- 'compatible', 'incompatible', 'unknown'
          compatibility_score REAL,
          breaking_changes TEXT,
          migration_notes TEXT,
          tested_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (package_name) REFERENCES packages(name),
          UNIQUE(package_name, version1, version2)
        )`);

        // Таблица альтернативных пакетов
        this.db.run(`CREATE TABLE IF NOT EXISTS package_alternatives (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          package_name TEXT,
          alternative_name TEXT,
          similarity_score REAL,
          feature_compatibility TEXT,
          migration_difficulty TEXT, -- 'easy', 'medium', 'hard'
          recommendation_reason TEXT,
          FOREIGN KEY (package_name) REFERENCES packages(name),
          FOREIGN KEY (alternative_name) REFERENCES packages(name),
          UNIQUE(package_name, alternative_name)
        )`);

        // Таблица конфликтов зависимостей
        this.db.run(`CREATE TABLE IF NOT EXISTS dependency_conflicts (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          project_hash TEXT,
          package1_name TEXT,
          package1_version TEXT,
          package2_name TEXT,
          package2_version TEXT,
          conflict_type TEXT, -- 'version', 'peer', 'circular'
          conflict_severity TEXT, -- 'critical', 'high', 'medium', 'low'
          resolution_suggestion TEXT,
          detected_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(project_hash, package1_name, package2_name)
        )`);

        // Таблица дерева зависимостей
        this.db.run(`CREATE TABLE IF NOT EXISTS dependency_tree (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          root_package TEXT,
          root_version TEXT,
          dependency_path TEXT,
          dependency_name TEXT,
          dependency_version TEXT,
          depth_level INTEGER,
          is_circular BOOLEAN DEFAULT 0,
          FOREIGN KEY (root_package) REFERENCES packages(name),
          UNIQUE(root_package, root_version, dependency_path)
        )`);

        // Таблица анализа совместимости проектов
        this.db.run(`CREATE TABLE IF NOT EXISTS project_compatibility_analysis (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          project_hash TEXT,
          project_name TEXT,
          analysis_result TEXT, -- JSON с результатами анализа
          total_dependencies INTEGER,
          compatible_dependencies INTEGER,
          incompatible_dependencies INTEGER,
          unknown_dependencies INTEGER,
          risk_level TEXT, -- 'low', 'medium', 'high'
          analyzed_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          UNIQUE(project_hash)
        )`);

        // Выполняем миграцию для добавления недостающих колонок
        this.migrateDatabase();

        // Создаем таблицы для аудита
        this.createAuditTables().then(() => {
          resolve();
        }).catch(err => {
          console.error('Ошибка создания таблиц аудита:', err);
          resolve(); // Продолжаем работу даже при ошибке
        });
      });
    });
  }

  // Миграция базы данных для добавления недостающих колонок
  migrateDatabase() {
    const columnsToAdd = [
      'maintainers TEXT',
      'contributors TEXT',
      'readme TEXT',
      'changelog TEXT',
      'bugs_url TEXT',
      'npm_url TEXT',
      'weekly_downloads INTEGER DEFAULT 0',
      'monthly_downloads INTEGER DEFAULT 0',
      'yearly_downloads INTEGER DEFAULT 0',
      'unpacked_size INTEGER DEFAULT 0',
      'file_count INTEGER DEFAULT 0',
      'has_typescript BOOLEAN DEFAULT 0',
      'has_tests BOOLEAN DEFAULT 0',
      'test_coverage REAL DEFAULT 0',
      'quality_score REAL DEFAULT 0',
      'popularity_score REAL DEFAULT 0',
      'maintenance_score REAL DEFAULT 0',
      'github_stars INTEGER DEFAULT 0',
      'github_forks INTEGER DEFAULT 0',
      'github_issues INTEGER DEFAULT 0',
      'github_watchers INTEGER DEFAULT 0',
      'last_commit_date TIMESTAMP',
      'first_release_date TIMESTAMP',
      'last_release_date TIMESTAMP',
      'release_frequency REAL DEFAULT 0',
      'version_count INTEGER DEFAULT 0',
      'dependency_count INTEGER DEFAULT 0',
      'dev_dependency_count INTEGER DEFAULT 0',
      'peer_dependency_count INTEGER DEFAULT 0',
      'optional_dependency_count INTEGER DEFAULT 0',
      'engines TEXT',
      'os_compatibility TEXT',
      'cpu_compatibility TEXT',
      'funding TEXT',
      'sponsors TEXT'
    ];

    // Проверяем существующие колонки
    this.db.all("PRAGMA table_info(packages)", (err, columns) => {
      if (err) {
        console.error('Ошибка получения информации о таблице:', err);
        return;
      }

      const existingColumns = columns.map(col => col.name);

      // Добавляем недостающие колонки
      columnsToAdd.forEach(columnDef => {
        const columnName = columnDef.split(' ')[0];

        if (!existingColumns.includes(columnName)) {
          this.db.run(`ALTER TABLE packages ADD COLUMN ${columnDef}`, (err) => {
            if (err && !err.message.includes('duplicate column name')) {
              console.error(`Ошибка добавления колонки ${columnName}:`, err.message);
            } else if (!err) {
              console.log(`✅ Добавлена колонка: ${columnName}`);
            }
          });
        }
      });
    });
  }

  async updatePackageInfo(packageName, packageInfo) {
    return new Promise((resolve, reject) => {
      this.db.serialize(() => {
        // Сначала пытаемся использовать расширенную схему
        this.updatePackageInfoExtended(packageName, packageInfo, resolve, reject);
      });
    });
  }

  // Обновление с расширенной схемой
  updatePackageInfoExtended(packageName, packageInfo, resolve, reject) {
    this.db.run(
      `INSERT OR REPLACE INTO packages
      (name, description, repository, homepage, license, latest_version, author,
       maintainers, contributors, keywords, readme, changelog, bugs_url, npm_url,
       download_count, weekly_downloads, monthly_downloads, yearly_downloads,
       bundle_size, gzip_size, unpacked_size, file_count, has_typescript, has_tests,
       test_coverage, quality_score, popularity_score, maintenance_score,
       github_stars, github_forks, github_issues, github_watchers,
       last_commit_date, first_release_date, last_release_date, release_frequency,
       version_count, dependency_count, dev_dependency_count, peer_dependency_count,
       optional_dependency_count, engines, os_compatibility, cpu_compatibility,
       funding, sponsors, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)`,
          [
            packageName,
            packageInfo.description || null,
            packageInfo.repository || null,
            packageInfo.homepage || null,
            packageInfo.license || null,
            packageInfo.latest_version || null,
            packageInfo.author || null,
            packageInfo.maintainers ? JSON.stringify(packageInfo.maintainers) : null,
            packageInfo.contributors ? JSON.stringify(packageInfo.contributors) : null,
            packageInfo.keywords ? JSON.stringify(packageInfo.keywords) : null,
            packageInfo.readme || null,
            packageInfo.changelog || null,
            packageInfo.bugs_url || null,
            packageInfo.npm_url || `https://www.npmjs.com/package/${packageName}`,
            packageInfo.download_count || 0,
            packageInfo.weekly_downloads || 0,
            packageInfo.monthly_downloads || 0,
            packageInfo.yearly_downloads || 0,
            packageInfo.bundle_size || 0,
            packageInfo.gzip_size || 0,
            packageInfo.unpacked_size || 0,
            packageInfo.file_count || 0,
            packageInfo.has_typescript || 0,
            packageInfo.has_tests || 0,
            packageInfo.test_coverage || 0,
            packageInfo.quality_score || 0,
            packageInfo.popularity_score || 0,
            packageInfo.maintenance_score || 0,
            packageInfo.github_stars || 0,
            packageInfo.github_forks || 0,
            packageInfo.github_issues || 0,
            packageInfo.github_watchers || 0,
            packageInfo.last_commit_date || null,
            packageInfo.first_release_date || null,
            packageInfo.last_release_date || null,
            packageInfo.release_frequency || 0,
            packageInfo.version_count || (packageInfo.versions ? packageInfo.versions.length : 0),
            packageInfo.dependency_count || 0,
            packageInfo.dev_dependency_count || 0,
            packageInfo.peer_dependency_count || 0,
            packageInfo.optional_dependency_count || 0,
            packageInfo.engines ? JSON.stringify(packageInfo.engines) : null,
            packageInfo.os_compatibility ? JSON.stringify(packageInfo.os_compatibility) : null,
            packageInfo.cpu_compatibility ? JSON.stringify(packageInfo.cpu_compatibility) : null,
            packageInfo.funding ? JSON.stringify(packageInfo.funding) : null,
            packageInfo.sponsors ? JSON.stringify(packageInfo.sponsors) : null,
            new Date().toISOString(), // created_at
            new Date().toISOString()  // updated_at
          ],
          (err) => {
            if (err) {
              // Если расширенная схема не работает, пробуем базовую
              console.log('Переход на базовую схему БД:', err.message);
              this.updatePackageInfoBasic(packageName, packageInfo, resolve, reject);
            } else {
              // Добавляем версии пакета (если они переданы)
              if (packageInfo.versions && packageInfo.versions.length > 0) {
                this.saveVersionsData(packageName, packageInfo.versions, resolve, reject);
              } else {
                resolve();
              }
            }
          }
        );
  }

  // Обновление с базовой схемой (fallback)
  updatePackageInfoBasic(packageName, packageInfo, resolve, reject) {
    this.db.run(
      `INSERT OR REPLACE INTO packages
      (name, description, repository, homepage, license, latest_version, author,
       keywords, download_count, bundle_size, gzip_size, updated_at)
      VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, datetime('now'))`,
      [
        packageName,
        packageInfo.description || null,
        packageInfo.repository || null,
        packageInfo.homepage || null,
        packageInfo.license || null,
        packageInfo.latest_version || null,
        packageInfo.author || null,
        packageInfo.keywords ? JSON.stringify(packageInfo.keywords) : null,
        packageInfo.download_count || 0,
        packageInfo.bundle_size || 0,
        packageInfo.gzip_size || 0
      ],
      (err) => {
        if (err) return reject(err);

        // Добавляем версии пакета (если они переданы)
        if (packageInfo.versions && packageInfo.versions.length > 0) {
          this.saveVersionsData(packageName, packageInfo.versions, resolve, reject);
        } else {
          resolve();
        }
      }
    );
  }

  // Сохранение данных о версиях
  async saveVersionsData(packageName, versionsData, resolve, reject) {
    try {
      const versionStmt = this.db.prepare(
        `INSERT OR REPLACE INTO versions
        (package_name, version, dependencies, dev_dependencies, peer_dependencies, optional_dependencies, published_at)
        VALUES (?, ?, ?, ?, ?, ?, ?)`
      );

      const depStmt = this.db.prepare(
        `INSERT OR REPLACE INTO dependencies
        (package_name, version, dependency_name, dependency_version, dependency_type)
        VALUES (?, ?, ?, ?, ?)`
      );

      for (const versionData of versionsData) {
        const version = typeof versionData === 'string' ? versionData : versionData.version;

        // Сохраняем информацию о версии
        versionStmt.run([
          packageName,
          version,
          JSON.stringify(versionData.dependencies || {}),
          JSON.stringify(versionData.devDependencies || {}),
          JSON.stringify(versionData.peerDependencies || {}),
          JSON.stringify(versionData.optionalDependencies || {}),
          versionData.published_at || new Date().toISOString()
        ]);

        // Сохраняем нормализованные зависимости
        const depTypes = [
          { deps: versionData.dependencies, type: 'dependencies' },
          { deps: versionData.devDependencies, type: 'devDependencies' },
          { deps: versionData.peerDependencies, type: 'peerDependencies' },
          { deps: versionData.optionalDependencies, type: 'optionalDependencies' }
        ];

        for (const { deps, type } of depTypes) {
          if (deps && typeof deps === 'object') {
            for (const [depName, depVersion] of Object.entries(deps)) {
              depStmt.run([packageName, version, depName, depVersion, type]);
            }
          }
        }
      }

      versionStmt.finalize();
      depStmt.finalize();
      resolve();
    } catch (error) {
      reject(error);
    }
  }

  // Получение детальной информации о пакете
  async getPackageDetails(packageName) {
    return new Promise((resolve, reject) => {
      // Получаем основную информацию о пакете
      this.db.get(
        `SELECT * FROM packages WHERE name = ?`,
        [packageName],
        (err, packageData) => {
          if (err) return reject(err);
          if (!packageData) return resolve(null);

          // Получаем версии с зависимостями
          this.db.all(
            `SELECT v.*,
             GROUP_CONCAT(d.dependency_name || ':' || d.dependency_version || ':' || d.dependency_type) as deps
             FROM versions v
             LEFT JOIN dependencies d ON v.package_name = d.package_name AND v.version = d.version
             WHERE v.package_name = ?
             GROUP BY v.package_name, v.version
             ORDER BY v.published_at DESC`,
            [packageName],
            (err, versions) => {
              if (err) return reject(err);

              // Обрабатываем зависимости
              const processedVersions = versions.map(version => {
                const dependencies = {};
                const devDependencies = {};
                const peerDependencies = {};
                const optionalDependencies = {};

                if (version.deps) {
                  version.deps.split(',').forEach(dep => {
                    const [name, ver, type] = dep.split(':');
                    if (name && ver && type) {
                      switch (type) {
                        case 'dependencies':
                          dependencies[name] = ver;
                          break;
                        case 'devDependencies':
                          devDependencies[name] = ver;
                          break;
                        case 'peerDependencies':
                          peerDependencies[name] = ver;
                          break;
                        case 'optionalDependencies':
                          optionalDependencies[name] = ver;
                          break;
                      }
                    }
                  });
                }

                return {
                  version: version.version,
                  published_at: version.published_at,
                  deprecated: version.deprecated,
                  dependencies,
                  devDependencies,
                  peerDependencies,
                  optionalDependencies
                };
              });

              // Получаем метрики
              this.db.get(
                `SELECT * FROM package_metrics WHERE package_name = ?`,
                [packageName],
                (err, metrics) => {
                  if (err) return reject(err);

                  // Получаем уязвимости
                  this.db.all(
                    `SELECT * FROM vulnerabilities WHERE package_name = ? ORDER BY reported_at DESC`,
                    [packageName],
                    (err, vulnerabilities) => {
                      if (err) return reject(err);

                      resolve({
                        ...packageData,
                        keywords: packageData.keywords ? JSON.parse(packageData.keywords) : [],
                        versions: processedVersions,
                        metrics: metrics || {},
                        vulnerabilities: vulnerabilities || []
                      });
                    }
                  );
                }
              );
            }
          );
        }
      );
    });
  }

  // Получение списка пакетов с пагинацией
  async getPackagesList(search = '', limit = 20, offset = 0) {
    return new Promise((resolve, reject) => {
      const searchCondition = search ? `WHERE name LIKE '%${search}%' OR description LIKE '%${search}%'` : '';

      this.db.get(
        `SELECT COUNT(*) as total FROM packages ${searchCondition}`,
        (err, countResult) => {
          if (err) return reject(err);

          this.db.all(
            `SELECT p.*,
             COUNT(v.version) as version_count,
             COUNT(d.dependency_name) as dependency_count
             FROM packages p
             LEFT JOIN versions v ON p.name = v.package_name
             LEFT JOIN dependencies d ON p.name = d.package_name AND d.dependency_type = 'dependencies'
             ${searchCondition}
             GROUP BY p.name
             ORDER BY p.updated_at DESC
             LIMIT ? OFFSET ?`,
            [limit, offset],
            (err, packages) => {
              if (err) return reject(err);

              resolve({
                packages: packages.map(pkg => ({
                  ...pkg,
                  keywords: pkg.keywords ? JSON.parse(pkg.keywords) : []
                })),
                pagination: {
                  total: countResult.total,
                  limit,
                  offset,
                  page: Math.floor(offset / limit) + 1,
                  pages: Math.ceil(countResult.total / limit)
                }
              });
            }
          );
        }
      );
    });
  }

  // Расширенный анализ зависимостей для package.json
  async analyzeDependencies(dependencies, devDependencies = {}) {
    return new Promise((resolve, reject) => {
      const allDeps = { ...dependencies, ...devDependencies };
      const depNames = Object.keys(allDeps);

      if (depNames.length === 0) {
        return resolve({
          totalDependencies: 0,
          knownPackages: [],
          unknownPackages: [],
          potentialIssues: [],
          recommendations: [],
          analysis: {
            securityIssues: 0,
            outdatedPackages: 0,
            unknownPackages: 0,
            qualityIssues: 0,
            maintenanceIssues: 0,
            totalIssues: 0,
            riskLevel: 'low'
          }
        });
      }

      const placeholders = depNames.map(() => '?').join(',');

      // Получаем расширенную информацию о пакетах
      this.db.all(
        `SELECT p.*,
                COALESCE(v.vulnerabilities_count, 0) as vulnerabilities_count,
                GROUP_CONCAT(v.severity) as vulnerability_severities
         FROM packages p
         LEFT JOIN (
           SELECT package_name, COUNT(*) as vulnerabilities_count, GROUP_CONCAT(severity) as severity
           FROM vulnerabilities
           GROUP BY package_name
         ) v ON p.name = v.package_name
         WHERE p.name IN (${placeholders})`,
        depNames,
        (err, rows) => {
          if (err) return reject(err);

          const knownPackages = [];
          const unknownPackages = [];
          const potentialIssues = [];
          const recommendations = [];
          let securityIssues = 0;
          let qualityIssues = 0;
          let maintenanceIssues = 0;

          depNames.forEach(depName => {
            const dbPackage = rows.find(row => row.name === depName);
            const requestedVersion = allDeps[depName];
            const isDev = devDependencies.hasOwnProperty(depName);

            if (dbPackage) {
              // Расширенная информация о пакете
              const packageInfo = {
                name: depName,
                requested_version: requestedVersion,
                latest_version: dbPackage.latest_version,
                description: dbPackage.description,
                license: dbPackage.license,
                repository: dbPackage.repository,
                author: dbPackage.author,

                // Метрики качества
                quality_score: dbPackage.quality_score || 0,
                popularity_score: dbPackage.popularity_score || 0,
                maintenance_score: dbPackage.maintenance_score || 0,

                // Статистика
                weekly_downloads: dbPackage.weekly_downloads || 0,
                monthly_downloads: dbPackage.monthly_downloads || 0,
                yearly_downloads: dbPackage.yearly_downloads || 0,
                github_stars: dbPackage.github_stars || 0,
                github_forks: dbPackage.github_forks || 0,
                github_issues: dbPackage.github_issues || 0,

                // Технические характеристики
                has_typescript: dbPackage.has_typescript || false,
                has_tests: dbPackage.has_tests || false,
                version_count: dbPackage.version_count || 0,

                // Безопасность
                vulnerabilities_count: dbPackage.vulnerabilities_count || 0,
                vulnerability_severities: dbPackage.vulnerability_severities ?
                  dbPackage.vulnerability_severities.split(',') : [],

                // Даты
                last_commit_date: dbPackage.last_commit_date,
                last_release_date: dbPackage.last_release_date,
                first_release_date: dbPackage.first_release_date,

                // Тип зависимости
                dependency_type: isDev ? 'devDependency' : 'dependency'
              };

              knownPackages.push(packageInfo);

              // Анализ проблем и рекомендаций

              // 1. Проверка версий
              if (this.isVersionOutdated(requestedVersion, dbPackage.latest_version)) {
                recommendations.push({
                  package: depName,
                  type: 'update',
                  current: requestedVersion,
                  latest: dbPackage.latest_version,
                  message: `Доступна новая версия ${dbPackage.latest_version}`,
                  priority: 'medium'
                });
              }

              // 2. Проверка безопасности
              if (dbPackage.vulnerabilities_count > 0) {
                securityIssues++;
                const severities = dbPackage.vulnerability_severities ? dbPackage.vulnerability_severities.split(',') : [];
                const hasCritical = severities.includes('critical');
                const hasHigh = severities.includes('high');

                potentialIssues.push({
                  package: depName,
                  type: 'security',
                  severity: hasCritical ? 'critical' : hasHigh ? 'high' : 'medium',
                  message: `Найдено ${dbPackage.vulnerabilities_count} уязвимостей`,
                  recommendation: 'Обновите пакет до безопасной версии'
                });
              }

              // 3. Проверка качества
              if (dbPackage.quality_score < 0.3) {
                qualityIssues++;
                potentialIssues.push({
                  package: depName,
                  type: 'quality',
                  severity: 'low',
                  message: `Низкое качество пакета (${(dbPackage.quality_score * 100).toFixed(1)}%)`,
                  recommendation: 'Рассмотрите альтернативные пакеты'
                });
              }

              // 4. Проверка поддержки
              if (dbPackage.maintenance_score < 0.2) {
                maintenanceIssues++;
                potentialIssues.push({
                  package: depName,
                  type: 'maintenance',
                  severity: 'medium',
                  message: `Пакет плохо поддерживается (${(dbPackage.maintenance_score * 100).toFixed(1)}%)`,
                  recommendation: 'Проверьте активность разработки'
                });
              }

              // 5. Проверка популярности (для production зависимостей)
              if (!isDev && dbPackage.weekly_downloads < 1000) {
                potentialIssues.push({
                  package: depName,
                  type: 'popularity',
                  severity: 'low',
                  message: `Низкая популярность пакета (${dbPackage.weekly_downloads} загрузок в неделю)`,
                  recommendation: 'Убедитесь в надежности пакета'
                });
              }

              // 6. Проверка лицензии
              if (!dbPackage.license || dbPackage.license === 'UNLICENSED') {
                potentialIssues.push({
                  package: depName,
                  type: 'license',
                  severity: 'medium',
                  message: 'Отсутствует или неопределенная лицензия',
                  recommendation: 'Проверьте правовые аспекты использования'
                });
              }

              // 7. Рекомендации по улучшению
              if (!dbPackage.has_typescript && !isDev) {
                recommendations.push({
                  package: depName,
                  type: 'typescript',
                  message: 'Пакет не поддерживает TypeScript',
                  recommendation: 'Установите @types/' + depName + ' для типизации',
                  priority: 'low'
                });
              }

            } else {
              unknownPackages.push(depName);
              potentialIssues.push({
                package: depName,
                type: 'unknown',
                severity: 'high',
                message: 'Пакет не найден в базе данных',
                recommendation: 'Проверьте правильность названия пакета или обновите базу данных'
              });
            }
          });

          resolve({
            totalDependencies: depNames.length,
            knownPackages,
            unknownPackages,
            potentialIssues,
            recommendations,
            analysis: {
              securityIssues,
              outdatedPackages: recommendations.filter(r => r.type === 'update').length,
              unknownPackages: unknownPackages.length,
              qualityIssues,
              maintenanceIssues,
              totalIssues: potentialIssues.length,
              riskLevel: this.calculateRiskLevel(potentialIssues, securityIssues)
            }
          });
        }
      );
    });
  }

  // Вычисление уровня риска проекта
  calculateRiskLevel(issues, securityIssues) {
    const criticalIssues = issues.filter(i => i.severity === 'critical').length;
    const highIssues = issues.filter(i => i.severity === 'high').length;

    if (criticalIssues > 0 || securityIssues > 2) return 'high';
    if (highIssues > 2 || securityIssues > 0) return 'medium';
    return 'low';
  }

  // Простая проверка устаревших версий
  isVersionOutdated(current, latest) {
    // Упрощенная проверка - в реальности нужен semver
    const cleanCurrent = current.replace(/[^0-9.]/g, '');
    const cleanLatest = latest.replace(/[^0-9.]/g, '');
    return cleanCurrent !== cleanLatest;
  }

  // Сохранение результатов npm audit
  async saveAuditResults(packageName, auditResults) {
    return new Promise((resolve, reject) => {
      if (!auditResults.vulnerabilities || auditResults.vulnerabilities.length === 0) {
        return resolve();
      }

      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO package_audit_results
        (package_name, package_version, vulnerability_id, severity, title, overview,
         recommendation, vulnerable_versions, patched_versions, cve, cvss_score, reported_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `);

      try {
        auditResults.vulnerabilities.forEach(vuln => {
          stmt.run([
            packageName,
            vuln.package_version || 'latest',
            vuln.vulnerability_id || vuln.title || 'unknown',
            vuln.severity || 'unknown',
            vuln.title || 'Unknown vulnerability',
            vuln.overview || '',
            vuln.recommendation || '',
            vuln.vulnerable_versions || '',
            vuln.patched_versions || '',
            JSON.stringify(vuln.cve || []),
            vuln.cvss_score || 0,
            vuln.reported_at || new Date().toISOString()
          ]);
        });

        stmt.finalize((err) => {
          if (err) reject(err);
          else resolve();
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  // Получение результатов аудита для пакета
  async getAuditResults(packageName) {
    return new Promise((resolve, reject) => {
      // Сначала проверяем, существует ли таблица
      this.db.get(
        `SELECT name FROM sqlite_master WHERE type='table' AND name='package_audit_results'`,
        (err, table) => {
          if (err) return reject(err);

          if (!table) {
            // Таблица не существует, возвращаем пустой массив
            return resolve([]);
          }

          // Таблица существует, получаем данные
          this.db.all(
            `SELECT * FROM package_audit_results WHERE package_name = ? ORDER BY audit_date DESC`,
            [packageName],
            (err, rows) => {
              if (err) return reject(err);

              // Парсим JSON поля
              const processedRows = (rows || []).map(row => ({
                ...row,
                cve: row.cve ? JSON.parse(row.cve) : []
              }));

              resolve(processedRows);
            }
          );
        }
      );
    });
  }

  // Получение альтернатив для пакета
  async getPackageAlternatives(packageName) {
    return new Promise((resolve, reject) => {
      // Сначала проверяем, существует ли таблица
      this.db.get(
        `SELECT name FROM sqlite_master WHERE type='table' AND name='package_alternatives'`,
        (err, table) => {
          if (err) return reject(err);

          if (!table) {
            // Таблица не существует, возвращаем пустой массив
            return resolve([]);
          }

          // Таблица существует, получаем данные
          this.db.all(
            `SELECT * FROM package_alternatives WHERE original_package = ? ORDER BY relevance_score DESC`,
            [packageName],
            (err, rows) => {
              if (err) return reject(err);

              const alternatives = (rows || []).map(row => ({
                ...row,
                match_reasons: row.match_reasons ? JSON.parse(row.match_reasons) : []
              }));

              resolve(alternatives);
            }
          );
        }
      );
    });
  }

  // Создание недостающих таблиц для аудита
  async createAuditTables() {
    return new Promise((resolve, reject) => {
      this.db.serialize(() => {
        // Таблица результатов npm audit
        this.db.run(`
          CREATE TABLE IF NOT EXISTS package_audit_results (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            package_name TEXT NOT NULL,
            package_version TEXT,
            vulnerability_id TEXT,
            severity TEXT,
            title TEXT,
            overview TEXT,
            recommendation TEXT,
            vulnerable_versions TEXT,
            patched_versions TEXT,
            cve TEXT,
            cvss_score REAL,
            reported_at DATETIME,
            audit_date DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(package_name, vulnerability_id)
          )
        `);

        // Таблица альтернатив пакетов
        this.db.run(`
          CREATE TABLE IF NOT EXISTS package_alternatives (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            original_package TEXT NOT NULL,
            alternative_package TEXT NOT NULL,
            similarity_score REAL DEFAULT 0,
            relevance_score REAL DEFAULT 0,
            match_reasons TEXT,
            recommendation TEXT,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            UNIQUE(original_package, alternative_package)
          )
        `, (err) => {
          if (err) reject(err);
          else resolve();
        });
      });
    });
  }

  close() {
    this.db.close();
  }
}

module.exports = Database;
