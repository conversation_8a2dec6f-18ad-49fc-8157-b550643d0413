const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class Database {
  constructor() {
    this.dbPath = path.join(__dirname, 'packages.db');
    this.db = new sqlite3.Database(this.dbPath);
  }

  async initialize() {
    return new Promise((resolve, reject) => {
      this.db.serialize(() => {
        this.db.run(`CREATE TABLE IF NOT EXISTS packages (
          name TEXT PRIMARY KEY,
          description TEXT,
          repository TEXT,
          homepage TEXT,
          license TEXT,
          latest_version TEXT,
          created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
          updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )`, (err) => err ? reject(err) : resolve());

        this.db.run(`CREATE TABLE IF NOT EXISTS versions (
          package_name TEXT,
          version TEXT,
          dependencies TEXT,
          published_at TIMESTAMP,
          PRIMARY KEY (package_name, version),
          FOREIGN KEY (package_name) REFERENCES packages(name)
        )`, (err) => err ? reject(err) : resolve());
      });
    });
  }

  async updatePackageInfo(packageName, packageInfo) {
    return new Promise((resolve, reject) => {
      this.db.serialize(() => {
        this.db.run(
          `INSERT OR REPLACE INTO packages
          (name, description, repository, homepage, license, latest_version)
          VALUES (?, ?, ?, ?, ?, ?)`,
          [
            packageName,
            packageInfo.description,
            packageInfo.repository,
            packageInfo.homepage,
            packageInfo.license,
            packageInfo.latest_version
          ],
          (err) => {
            if (err) return reject(err);

            // Добавляем версии пакета (если они переданы)
            if (packageInfo.versions && Array.isArray(packageInfo.versions)) {
              const stmt = this.db.prepare(
                `INSERT OR REPLACE INTO versions
                (package_name, version, dependencies, published_at)
                VALUES (?, ?, ?, datetime('now'))`
              );

              for (const version of packageInfo.versions) {
                const deps = packageInfo.dependencies || {};
                stmt.run([
                  packageName,
                  version,
                  JSON.stringify(deps)
                ]);
              }

              stmt.finalize(err => err ? reject(err) : resolve());
            } else {
              resolve();
            }
          }
        );
      });
    });
  }

  close() {
    this.db.close();
  }
}

module.exports = Database;
