// Миграция базы данных для расширения схемы
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class DatabaseMigration {
  constructor() {
    this.dbPath = path.join(__dirname, '..', 'packages.db');
    this.db = new sqlite3.Database(this.dbPath);
  }

  async migrate() {
    console.log('🔄 Начинаем миграцию базы данных...');

    try {
      await this.ensureBaseTables();
      await this.addNewColumns();
      await this.createNewTables();
      await this.createIndexes();
      console.log('✅ Миграция завершена успешно!');
    } catch (error) {
      console.error('❌ Ошибка миграции:', error);
      throw error;
    }
  }

  async ensureBaseTables() {
    console.log('🏗️ Проверяем базовые таблицы...');

    // Создаем базовые таблицы если их нет
    await this.createTable('packages_base', `
      CREATE TABLE IF NOT EXISTS packages (
        name TEXT PRIMARY KEY,
        description TEXT,
        repository TEXT,
        homepage TEXT,
        license TEXT,
        latest_version TEXT,
        author TEXT,
        keywords TEXT,
        download_count INTEGER DEFAULT 0,
        bundle_size INTEGER DEFAULT 0,
        gzip_size INTEGER DEFAULT 0,
        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
      )
    `);

    await this.createTable('versions_base', `
      CREATE TABLE IF NOT EXISTS versions (
        package_name TEXT,
        version TEXT,
        dependencies TEXT,
        dev_dependencies TEXT,
        peer_dependencies TEXT,
        optional_dependencies TEXT,
        published_at TIMESTAMP,
        deprecated BOOLEAN DEFAULT FALSE,
        PRIMARY KEY (package_name, version),
        FOREIGN KEY (package_name) REFERENCES packages(name)
      )
    `);

    await this.createTable('dependencies_base', `
      CREATE TABLE IF NOT EXISTS dependencies (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        package_name TEXT,
        version TEXT,
        dependency_name TEXT,
        dependency_version TEXT,
        dependency_type TEXT,
        FOREIGN KEY (package_name, version) REFERENCES versions(package_name, version)
      )
    `);

    await this.createTable('package_metrics_base', `
      CREATE TABLE IF NOT EXISTS package_metrics (
        package_name TEXT PRIMARY KEY,
        weekly_downloads INTEGER DEFAULT 0,
        monthly_downloads INTEGER DEFAULT 0,
        yearly_downloads INTEGER DEFAULT 0,
        github_stars INTEGER DEFAULT 0,
        github_forks INTEGER DEFAULT 0,
        github_issues INTEGER DEFAULT 0,
        last_commit_date TIMESTAMP,
        maintenance_score REAL DEFAULT 0,
        popularity_score REAL DEFAULT 0,
        quality_score REAL DEFAULT 0,
        FOREIGN KEY (package_name) REFERENCES packages(name)
      )
    `);

    await this.createTable('vulnerabilities_base', `
      CREATE TABLE IF NOT EXISTS vulnerabilities (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        package_name TEXT,
        version_range TEXT,
        severity TEXT,
        title TEXT,
        description TEXT,
        cve_id TEXT,
        fixed_in TEXT,
        reported_at TIMESTAMP,
        FOREIGN KEY (package_name) REFERENCES packages(name)
      )
    `);
  }

  async addNewColumns() {
    console.log('📊 Добавляем новые колонки в таблицу packages...');

    const newColumns = [
      'maintainers TEXT',
      'contributors TEXT',
      'readme TEXT',
      'changelog TEXT',
      'bugs_url TEXT',
      'npm_url TEXT',
      'weekly_downloads INTEGER DEFAULT 0',
      'monthly_downloads INTEGER DEFAULT 0',
      'yearly_downloads INTEGER DEFAULT 0',
      'unpacked_size INTEGER DEFAULT 0',
      'file_count INTEGER DEFAULT 0',
      'has_typescript BOOLEAN DEFAULT 0',
      'has_tests BOOLEAN DEFAULT 0',
      'test_coverage REAL DEFAULT 0',
      'quality_score REAL DEFAULT 0',
      'popularity_score REAL DEFAULT 0',
      'maintenance_score REAL DEFAULT 0',
      'github_stars INTEGER DEFAULT 0',
      'github_forks INTEGER DEFAULT 0',
      'github_issues INTEGER DEFAULT 0',
      'github_watchers INTEGER DEFAULT 0',
      'last_commit_date TIMESTAMP',
      'first_release_date TIMESTAMP',
      'last_release_date TIMESTAMP',
      'release_frequency REAL DEFAULT 0',
      'version_count INTEGER DEFAULT 0',
      'dependency_count INTEGER DEFAULT 0',
      'dev_dependency_count INTEGER DEFAULT 0',
      'peer_dependency_count INTEGER DEFAULT 0',
      'optional_dependency_count INTEGER DEFAULT 0',
      'engines TEXT',
      'os_compatibility TEXT',
      'cpu_compatibility TEXT',
      'funding TEXT',
      'sponsors TEXT'
    ];

    for (const column of newColumns) {
      try {
        await this.addColumn('packages', column);
      } catch (error) {
        // Игнорируем ошибки если колонка уже существует
        if (!error.message.includes('duplicate column name')) {
          throw error;
        }
      }
    }
  }

  async addColumn(tableName, columnDefinition) {
    return new Promise((resolve, reject) => {
      this.db.run(`ALTER TABLE ${tableName} ADD COLUMN ${columnDefinition}`, (err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  async createNewTables() {
    console.log('🗂️ Создаем дополнительные таблицы...');

    // Таблица для файлов пакета
    await this.createTable('package_files', `
      CREATE TABLE IF NOT EXISTS package_files (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        package_name TEXT,
        version TEXT,
        file_path TEXT,
        file_size INTEGER,
        file_type TEXT,
        is_main BOOLEAN DEFAULT 0,
        is_binary BOOLEAN DEFAULT 0,
        FOREIGN KEY (package_name, version) REFERENCES versions(package_name, version)
      )
    `);

    // Таблица для тегов и категорий
    await this.createTable('package_tags', `
      CREATE TABLE IF NOT EXISTS package_tags (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        package_name TEXT,
        tag TEXT,
        tag_type TEXT, -- 'keyword', 'category', 'framework', 'tool'
        FOREIGN KEY (package_name) REFERENCES packages(name)
      )
    `);

    // Таблица для истории релизов
    await this.createTable('release_history', `
      CREATE TABLE IF NOT EXISTS release_history (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        package_name TEXT,
        version TEXT,
        release_date TIMESTAMP,
        release_notes TEXT,
        breaking_changes BOOLEAN DEFAULT 0,
        security_fixes BOOLEAN DEFAULT 0,
        bug_fixes INTEGER DEFAULT 0,
        new_features INTEGER DEFAULT 0,
        FOREIGN KEY (package_name, version) REFERENCES versions(package_name, version)
      )
    `);

    // Таблица для связей между пакетами
    await this.createTable('package_relationships', `
      CREATE TABLE IF NOT EXISTS package_relationships (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        package_name TEXT,
        related_package TEXT,
        relationship_type TEXT, -- 'alternative', 'complement', 'competitor', 'successor'
        strength REAL DEFAULT 0, -- 0-1 сила связи
        FOREIGN KEY (package_name) REFERENCES packages(name),
        FOREIGN KEY (related_package) REFERENCES packages(name)
      )
    `);

    // Таблица для лицензий
    await this.createTable('package_licenses', `
      CREATE TABLE IF NOT EXISTS package_licenses (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        package_name TEXT,
        license_type TEXT,
        license_text TEXT,
        is_osi_approved BOOLEAN DEFAULT 0,
        is_commercial_friendly BOOLEAN DEFAULT 0,
        FOREIGN KEY (package_name) REFERENCES packages(name)
      )
    `);

    // Таблица для статистики использования
    await this.createTable('usage_stats', `
      CREATE TABLE IF NOT EXISTS usage_stats (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        package_name TEXT,
        date DATE,
        downloads INTEGER DEFAULT 0,
        dependent_packages INTEGER DEFAULT 0,
        github_clones INTEGER DEFAULT 0,
        npm_searches INTEGER DEFAULT 0,
        FOREIGN KEY (package_name) REFERENCES packages(name)
      )
    `);
  }

  async createTable(tableName, sql) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, (err) => {
        if (err) {
          reject(err);
        } else {
          console.log(`✅ Таблица ${tableName} создана`);
          resolve();
        }
      });
    });
  }

  async createIndexes() {
    console.log('🔍 Создаем индексы для оптимизации...');

    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_packages_author ON packages(author)',
      'CREATE INDEX IF NOT EXISTS idx_packages_license ON packages(license)',
      'CREATE INDEX IF NOT EXISTS idx_packages_downloads ON packages(weekly_downloads DESC)',
      'CREATE INDEX IF NOT EXISTS idx_packages_stars ON packages(github_stars DESC)',
      'CREATE INDEX IF NOT EXISTS idx_packages_quality ON packages(quality_score DESC)',
      'CREATE INDEX IF NOT EXISTS idx_versions_published ON versions(published_at DESC)',
      'CREATE INDEX IF NOT EXISTS idx_dependencies_name ON dependencies(dependency_name)',
      'CREATE INDEX IF NOT EXISTS idx_dependencies_type ON dependencies(dependency_type)',
      'CREATE INDEX IF NOT EXISTS idx_vulnerabilities_severity ON vulnerabilities(severity)',
      'CREATE INDEX IF NOT EXISTS idx_package_tags_tag ON package_tags(tag)',
      'CREATE INDEX IF NOT EXISTS idx_usage_stats_date ON usage_stats(date DESC)'
    ];

    for (const indexSql of indexes) {
      await this.createIndex(indexSql);
    }
  }

  async createIndex(sql) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, (err) => {
        if (err && !err.message.includes('already exists')) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  close() {
    this.db.close();
  }
}

// Запуск миграции
if (require.main === module) {
  const migration = new DatabaseMigration();
  migration.migrate()
    .then(() => {
      console.log('🎉 Миграция базы данных завершена!');
      migration.close();
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Ошибка миграции:', error);
      migration.close();
      process.exit(1);
    });
}

module.exports = DatabaseMigration;
