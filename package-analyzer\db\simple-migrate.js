// Простая миграция для добавления колонок
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

class SimpleMigration {
  constructor() {
    this.dbPath = path.join(__dirname, '..', 'packages.db');
    this.db = new sqlite3.Database(this.dbPath);
  }

  async migrate() {
    console.log('🔄 Простая миграция базы данных...');
    
    try {
      // Список новых колонок для добавления
      const newColumns = [
        'maintainers TEXT',
        'contributors TEXT', 
        'readme TEXT',
        'changelog TEXT',
        'bugs_url TEXT',
        'npm_url TEXT',
        'weekly_downloads INTEGER DEFAULT 0',
        'monthly_downloads INTEGER DEFAULT 0',
        'yearly_downloads INTEGER DEFAULT 0',
        'unpacked_size INTEGER DEFAULT 0',
        'file_count INTEGER DEFAULT 0',
        'has_typescript BOOLEAN DEFAULT 0',
        'has_tests BOOLEAN DEFAULT 0',
        'test_coverage REAL DEFAULT 0',
        'quality_score REAL DEFAULT 0',
        'popularity_score REAL DEFAULT 0',
        'maintenance_score REAL DEFAULT 0',
        'github_stars INTEGER DEFAULT 0',
        'github_forks INTEGER DEFAULT 0',
        'github_issues INTEGER DEFAULT 0',
        'github_watchers INTEGER DEFAULT 0',
        'last_commit_date TIMESTAMP',
        'first_release_date TIMESTAMP',
        'last_release_date TIMESTAMP',
        'release_frequency REAL DEFAULT 0',
        'version_count INTEGER DEFAULT 0',
        'dependency_count INTEGER DEFAULT 0',
        'dev_dependency_count INTEGER DEFAULT 0',
        'peer_dependency_count INTEGER DEFAULT 0',
        'optional_dependency_count INTEGER DEFAULT 0',
        'engines TEXT',
        'os_compatibility TEXT',
        'cpu_compatibility TEXT',
        'funding TEXT',
        'sponsors TEXT'
      ];

      console.log(`📊 Добавляем ${newColumns.length} новых колонок...`);
      
      for (let i = 0; i < newColumns.length; i++) {
        const column = newColumns[i];
        try {
          await this.addColumn('packages', column);
          console.log(`✅ [${i + 1}/${newColumns.length}] Добавлена колонка: ${column.split(' ')[0]}`);
        } catch (error) {
          if (error.message.includes('duplicate column name')) {
            console.log(`⚠️ [${i + 1}/${newColumns.length}] Колонка уже существует: ${column.split(' ')[0]}`);
          } else {
            console.error(`❌ [${i + 1}/${newColumns.length}] Ошибка добавления ${column}:`, error.message);
          }
        }
      }
      
      // Проверяем результат
      await this.checkColumns();
      
      console.log('✅ Простая миграция завершена!');
    } catch (error) {
      console.error('❌ Ошибка миграции:', error);
      throw error;
    }
  }

  async addColumn(tableName, columnDefinition) {
    return new Promise((resolve, reject) => {
      const sql = `ALTER TABLE ${tableName} ADD COLUMN ${columnDefinition}`;
      this.db.run(sql, (err) => {
        if (err) {
          reject(err);
        } else {
          resolve();
        }
      });
    });
  }

  async checkColumns() {
    return new Promise((resolve, reject) => {
      this.db.all("PRAGMA table_info(packages)", (err, columns) => {
        if (err) {
          reject(err);
          return;
        }
        
        console.log(`\n📋 Текущая структура таблицы packages:`);
        console.log(`📊 Всего колонок: ${columns.length}`);
        
        const columnNames = columns.map(col => col.name);
        console.log(`📝 Колонки: ${columnNames.join(', ')}`);
        
        // Проверяем наличие ключевых колонок
        const keyColumns = ['weekly_downloads', 'github_stars', 'quality_score', 'has_typescript'];
        const existingKeyColumns = keyColumns.filter(col => columnNames.includes(col));
        
        console.log(`✅ Ключевые колонки добавлены: ${existingKeyColumns.length}/${keyColumns.length}`);
        
        resolve();
      });
    });
  }

  close() {
    this.db.close();
  }
}

// Запуск миграции
if (require.main === module) {
  const migration = new SimpleMigration();
  migration.migrate()
    .then(() => {
      console.log('🎉 Простая миграция завершена успешно!');
      migration.close();
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Ошибка простой миграции:', error);
      migration.close();
      process.exit(1);
    });
}

module.exports = SimpleMigration;
