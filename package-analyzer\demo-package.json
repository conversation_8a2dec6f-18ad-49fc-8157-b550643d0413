{"name": "demo-project", "version": "1.0.0", "description": "Демонстрационный проект для тестирования Package Analyzer", "main": "index.js", "scripts": {"start": "node index.js", "test": "jest", "build": "webpack --mode production", "dev": "webpack-dev-server --mode development"}, "dependencies": {"express": "^4.18.2", "lodash": "^4.17.21", "axios": "^1.6.2", "moment": "^2.29.4", "react": "^18.2.0", "react-dom": "^18.2.0", "vue": "^3.3.8", "angular": "^1.8.3", "jquery": "^3.7.1", "bootstrap": "^5.3.2", "webpack": "^5.89.0", "babel-core": "^6.26.3", "typescript": "^5.3.2"}, "devDependencies": {"jest": "^29.7.0", "webpack-dev-server": "^4.15.1", "babel-loader": "^9.1.3", "css-loader": "^6.8.1", "style-loader": "^3.3.3", "eslint": "^8.54.0", "prettier": "^3.1.0", "@types/node": "^20.9.4", "nodemon": "^3.0.2"}, "peerDependencies": {"react": ">=16.0.0", "vue": ">=3.0.0"}, "engines": {"node": ">=16.0.0", "npm": ">=8.0.0"}, "keywords": ["demo", "package-analyzer", "dependencies", "npm"], "author": "Package Analyzer Demo", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/demo/demo-project.git"}, "bugs": {"url": "https://github.com/demo/demo-project/issues"}, "homepage": "https://github.com/demo/demo-project#readme"}