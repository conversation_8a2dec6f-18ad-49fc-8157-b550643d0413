// Скрипт для исправления схемы базы данных
const fs = require('fs');
const path = require('path');
const Database = require('./db/database');

async function fixDatabaseSchema() {
  console.log('🔧 Исправление схемы базы данных...');
  
  const dbPath = path.join(__dirname, 'db', 'packages.db');
  const backupPath = path.join(__dirname, 'db', 'packages_backup.db');
  
  try {
    // Создаем резервную копию существующей БД
    if (fs.existsSync(dbPath)) {
      console.log('📦 Создание резервной копии базы данных...');
      fs.copyFileSync(dbPath, backupPath);
      console.log('✅ Резервная копия создана: packages_backup.db');
    }
    
    // Инициализируем новую базу данных с правильной схемой
    console.log('🏗️ Инициализация новой схемы базы данных...');
    const db = new Database();
    await db.initialize();
    
    console.log('✅ База данных успешно инициализирована с правильной схемой!');
    
    // Проверяем структуру таблицы
    console.log('🔍 Проверка структуры таблицы packages...');
    
    db.db.all("PRAGMA table_info(packages)", (err, columns) => {
      if (err) {
        console.error('❌ Ошибка проверки структуры:', err);
        return;
      }
      
      console.log('📊 Структура таблицы packages:');
      columns.forEach(col => {
        console.log(`   - ${col.name}: ${col.type}${col.notnull ? ' NOT NULL' : ''}${col.dflt_value ? ` DEFAULT ${col.dflt_value}` : ''}`);
      });
      
      console.log('\n🎉 Схема базы данных исправлена!');
      console.log('📝 Теперь можно безопасно собирать пакеты без ошибок.');
      
      // Закрываем соединение
      db.db.close((err) => {
        if (err) {
          console.error('Ошибка закрытия БД:', err);
        } else {
          console.log('✅ Соединение с БД закрыто');
        }
        process.exit(0);
      });
    });
    
  } catch (error) {
    console.error('❌ Ошибка исправления схемы:', error.message);
    
    // Восстанавливаем из резервной копии при ошибке
    if (fs.existsSync(backupPath)) {
      console.log('🔄 Восстановление из резервной копии...');
      fs.copyFileSync(backupPath, dbPath);
      console.log('✅ База данных восстановлена из резервной копии');
    }
    
    process.exit(1);
  }
}

// Запускаем исправление
if (require.main === module) {
  fixDatabaseSchema().catch(error => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
}

module.exports = { fixDatabaseSchema };
