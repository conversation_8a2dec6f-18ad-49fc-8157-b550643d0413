const { GPU } = require('gpu.js');

class GPUAccelerator {
  constructor() {
    this.gpu = new GPU();
    this.initialized = false;
    this.initGPU();
  }

  initGPU() {
    try {
      // Проверяем доступность GPU
      const gpu = new GPU();
      const isGPUAvailable = gpu.getMode() === 'gpu';
      
      if (isGPUAvailable) {
        console.log('GPU доступен для вычислений');
        this.initialized = true;
      } else {
        console.log('GPU недоступен, используем CPU');
        this.initialized = false;
      }
    } catch (error) {
      console.error('Ошибка при инициализации GPU:', error.message);
      this.initialized = false;
    }
  }

  // Анализ совместимости версий с использованием GPU
  analyzeVersionCompatibility(versions) {
    if (!this.initialized) {
      return this.cpuAnalyzeVersionCompatibility(versions);
    }

    const kernel = this.gpu.createKernel(function(versions, count) {
      const i = this.thread.x;
      const j = this.thread.y;
      
      if (i >= count || j >= count) return 0;
      
      // Простая проверка совместимости (можно усложнить)
      const versionA = versions[i];
      const versionB = versions[j];
      
      // Сравниваем мажорные версии
      const majorA = Math.floor(versionA);
      const majorB = Math.floor(versionB);
      
      if (majorA !== majorB) return 0; // Несовместимы
      return 1; // Совместимы
    }).setOutput([versions.length, versions.length]);

    return kernel(versions, versions.length);
  }

  // Резервный метод для CPU, если GPU недоступен
  cpuAnalyzeVersionCompatibility(versions) {
    const result = [];
    for (let i = 0; i < versions.length; i++) {
      const row = [];
      for (let j = 0; j < versions.length; j++) {
        const majorA = Math.floor(versions[i]);
        const majorB = Math.floor(versions[j]);
        row.push(majorA === majorB ? 1 : 0);
      }
      result.push(row);
    }
    return result;
  }

  // Анализ зависимостей с использованием GPU
  analyzeDependencyGraph(dependencies) {
    if (!this.initialized) {
      return this.cpuAnalyzeDependencyGraph(dependencies);
    }

    // Преобразуем граф зависимостей в матрицу смежности
    const packageNames = Object.keys(dependencies);
    const adjacencyMatrix = [];
    
    for (let i = 0; i < packageNames.length; i++) {
      const row = new Array(packageNames.length).fill(0);
      const deps = dependencies[packageNames[i]] || {};
      
      for (let j = 0; j < packageNames.length; j++) {
        if (deps[packageNames[j]]) {
          row[j] = 1;
        }
      }
      
      adjacencyMatrix.push(row);
    }

    // Используем GPU для поиска циклических зависимостей
    const kernel = this.gpu.createKernel(function(matrix, size) {
      let result = 0;
      const i = this.thread.x;
      
      // Проверяем путь длиной до 3 (можно увеличить для более глубокого анализа)
      for (let j = 0; j < size; j++) {
        if (matrix[i][j] === 1) {
          for (let k = 0; k < size; k++) {
            if (matrix[j][k] === 1 && matrix[k][i] === 1) {
              result = 1; // Найден цикл
            }
          }
        }
      }
      
      return result;
    }).setOutput([packageNames.length]);

    const cycles = kernel(adjacencyMatrix, packageNames.length);
    
    // Преобразуем результаты в удобный формат
    const result = [];
    for (let i = 0; i < cycles.length; i++) {
      if (cycles[i] === 1) {
        result.push(packageNames[i]);
      }
    }
    
    return result;
  }

  // Резервный метод для CPU
  cpuAnalyzeDependencyGraph(dependencies) {
    // Аналогичная логика, но без GPU
    // ...
    return [];
  }
}

module.exports = GPUAccelerator;
