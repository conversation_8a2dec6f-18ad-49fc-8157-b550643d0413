const os = require('os');
const { execSync } = require('child_process');

class GPUAccelerator {
  constructor() {
    this.initialized = false;
    this.gpuInfo = null;
    this.cudaAvailable = false;
    this.initGPU();
  }

  initGPU() {
    try {
      console.log('🔍 Проверяем доступность GPU и CUDA...');

      // Проверяем CUDA
      this.checkCUDA();

      // Проверяем GPU
      this.checkGPU();

      if (this.cudaAvailable && this.gpuInfo) {
        console.log('✅ GPU ускорение доступно');
        console.log(`📊 GPU: ${this.gpuInfo.name}`);
        console.log(`💾 VRAM: ${this.gpuInfo.memory}`);
        console.log(`🔧 CUDA: ${this.gpuInfo.cudaVersion}`);
        this.initialized = true;
      } else {
        console.log('⚠️ GPU недоступен, используем CPU вычисления');
        this.initialized = false;
      }
    } catch (error) {
      console.error('❌ Ошибка при инициализации GPU:', error.message);
      this.initialized = false;
    }
  }

  checkCUDA() {
    try {
      // Проверяем наличие nvidia-smi
      const nvidiaSmi = execSync('nvidia-smi --query-gpu=name,memory.total,driver_version --format=csv,noheader,nounits',
        { encoding: 'utf8', timeout: 5000 });

      if (nvidiaSmi) {
        const lines = nvidiaSmi.trim().split('\n');
        const gpuData = lines[0].split(', ');

        this.gpuInfo = {
          name: gpuData[0],
          memory: `${gpuData[1]} MB`,
          driverVersion: gpuData[2]
        };

        // Проверяем CUDA
        try {
          const nvccVersion = execSync('nvcc --version', { encoding: 'utf8', timeout: 3000 });
          const cudaMatch = nvccVersion.match(/release (\d+\.\d+)/);
          this.gpuInfo.cudaVersion = cudaMatch ? cudaMatch[1] : 'Unknown';
          this.cudaAvailable = true;
        } catch (e) {
          console.log('⚠️ CUDA toolkit не найден');
          this.gpuInfo.cudaVersion = 'Not installed';
        }
      }
    } catch (error) {
      console.log('⚠️ NVIDIA GPU не найден или драйверы не установлены');
    }
  }

  checkGPU() {
    // Дополнительная проверка через WMI на Windows
    if (os.platform() === 'win32') {
      try {
        const wmic = execSync('wmic path win32_VideoController get name,AdapterRAM /format:csv',
          { encoding: 'utf8', timeout: 5000 });

        const lines = wmic.split('\n').filter(line => line.includes('NVIDIA') || line.includes('AMD'));
        if (lines.length > 0 && !this.gpuInfo) {
          // Парсим информацию о GPU из WMI если nvidia-smi недоступен
          this.gpuInfo = {
            name: 'GPU detected via WMI',
            memory: 'Unknown',
            driverVersion: 'Unknown',
            cudaVersion: 'Not available'
          };
        }
      } catch (e) {
        // Игнорируем ошибки WMI
      }
    }
  }

  getGPUStatus() {
    return {
      available: this.initialized,
      cuda: this.cudaAvailable,
      info: this.gpuInfo,
      platform: os.platform(),
      arch: os.arch()
    };
  }

  // Анализ совместимости версий с использованием GPU
  analyzeVersionCompatibility(versions) {
    if (!this.initialized) {
      return this.cpuAnalyzeVersionCompatibility(versions);
    }

    const kernel = this.gpu.createKernel(function(versions, count) {
      const i = this.thread.x;
      const j = this.thread.y;

      if (i >= count || j >= count) return 0;

      // Простая проверка совместимости (можно усложнить)
      const versionA = versions[i];
      const versionB = versions[j];

      // Сравниваем мажорные версии
      const majorA = Math.floor(versionA);
      const majorB = Math.floor(versionB);

      if (majorA !== majorB) return 0; // Несовместимы
      return 1; // Совместимы
    }).setOutput([versions.length, versions.length]);

    return kernel(versions, versions.length);
  }

  // Резервный метод для CPU, если GPU недоступен
  cpuAnalyzeVersionCompatibility(versions) {
    const result = [];
    for (let i = 0; i < versions.length; i++) {
      const row = [];
      for (let j = 0; j < versions.length; j++) {
        const majorA = Math.floor(versions[i]);
        const majorB = Math.floor(versions[j]);
        row.push(majorA === majorB ? 1 : 0);
      }
      result.push(row);
    }
    return result;
  }

  // Анализ зависимостей с использованием GPU
  analyzeDependencyGraph(dependencies) {
    if (!this.initialized) {
      return this.cpuAnalyzeDependencyGraph(dependencies);
    }

    // Преобразуем граф зависимостей в матрицу смежности
    const packageNames = Object.keys(dependencies);
    const adjacencyMatrix = [];

    for (let i = 0; i < packageNames.length; i++) {
      const row = new Array(packageNames.length).fill(0);
      const deps = dependencies[packageNames[i]] || {};

      for (let j = 0; j < packageNames.length; j++) {
        if (deps[packageNames[j]]) {
          row[j] = 1;
        }
      }

      adjacencyMatrix.push(row);
    }

    // Используем GPU для поиска циклических зависимостей
    const kernel = this.gpu.createKernel(function(matrix, size) {
      let result = 0;
      const i = this.thread.x;

      // Проверяем путь длиной до 3 (можно увеличить для более глубокого анализа)
      for (let j = 0; j < size; j++) {
        if (matrix[i][j] === 1) {
          for (let k = 0; k < size; k++) {
            if (matrix[j][k] === 1 && matrix[k][i] === 1) {
              result = 1; // Найден цикл
            }
          }
        }
      }

      return result;
    }).setOutput([packageNames.length]);

    const cycles = kernel(adjacencyMatrix, packageNames.length);

    // Преобразуем результаты в удобный формат
    const result = [];
    for (let i = 0; i < cycles.length; i++) {
      if (cycles[i] === 1) {
        result.push(packageNames[i]);
      }
    }

    return result;
  }

  // Резервный метод для CPU
  cpuAnalyzeDependencyGraph(dependencies) {
    // Аналогичная логика, но без GPU
    // ...
    return [];
  }
}

module.exports = GPUAccelerator;
