const path = require('path');
const fs = require('fs');
const GPUAccelerator = require('./gpu-accelerator');
const semver = require('semver');

class PackageAnalyzer {
  constructor() {
    this.gpuAccelerator = new GPUAccelerator();
  }

  async analyzePackage(packagePath) {
    try {
      const absolutePath = path.resolve(packagePath);
      const packageJson = JSON.parse(fs.readFileSync(absolutePath, 'utf8'));
      
      // Собираем все зависимости
      const dependencies = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies,
        ...packageJson.peerDependencies
      };
      
      // Преобразуем версии в числовой формат для GPU-анализа
      const versionMap = {};
      const versionArray = [];
      
      Object.entries(dependencies).forEach(([name, versionStr]) => {
        // Удаляем префиксы типа ^, ~ и т.д.
        const cleanVersion = versionStr.replace(/[\^~>=<]/g, '');
        // Берем только первые две части версии (major.minor)
        const parts = cleanVersion.split('.');
        const numericVersion = parseFloat(`${parts[0]}.${parts[1] || 0}`);
        
        versionMap[name] = numericVersion;
        versionArray.push(numericVersion);
      });
      
      // Используем GPU для анализа совместимости версий
      const compatibilityMatrix = this.gpuAccelerator.analyzeVersionCompatibility(versionArray);
      
      // Анализируем циклические зависимости
      const cyclicDependencies = this.gpuAccelerator.analyzeDependencyGraph(dependencies);
      
      // Формируем результат анализа
      return {
        name: packageJson.name,
        version: packageJson.version,
        dependenciesCount: Object.keys(dependencies).length,
        compatibilityMatrix,
        cyclicDependencies,
        potentialIssues: this.identifyPotentialIssues(dependencies)
      };
    } catch (error) {
      return {
        success: false,
        error: error.message
      };
    }
  }

  identifyPotentialIssues(dependencies) {
    const issues = [];
    
    // Проверка на устаревшие версии
    Object.entries(dependencies).forEach(([name, version]) => {
      if (version.startsWith('^0.') || version.startsWith('~0.')) {
        issues.push({
          package: name,
          version,
          issue: 'Используется нестабильная версия (0.x)'
        });
      }
    });
    
    return issues;
  }

  async resolveConflicts(packagePath) {
    // Логика разрешения конфликтов
    // ...
    return { resolved: true, message: 'Конфликты успешно разрешены' };
  }
}

module.exports = PackageAnalyzer;
