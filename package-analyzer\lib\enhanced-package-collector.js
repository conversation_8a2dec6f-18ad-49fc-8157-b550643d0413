// Улучшенный сборщик пакетов с npm audit и полным анализом зависимостей
const axios = require('axios');
const { exec, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');
const util = require('util');

const execAsync = util.promisify(exec);

class EnhancedPackageCollector {
  constructor(database) {
    this.db = database;
    this.tempDir = path.join(os.tmpdir(), 'package-analyzer-temp');
    this.auditCache = new Map();
    this.dependencyCache = new Map();
    
    // Создаем временную директорию
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  // Получение полной информации о пакете с npm audit
  async collectPackageWithAudit(packageName) {
    console.log(`🔍 Полный сбор пакета с audit: ${packageName}`);
    
    try {
      // 1. Получаем базовую информацию из npm registry
      const registryInfo = await this.getRegistryInfo(packageName);
      
      // 2. Получаем информацию об уязвимостях через npm audit
      const auditInfo = await this.getAuditInfo(packageName);
      
      // 3. Анализируем все зависимости
      const dependencyAnalysis = await this.analyzeDependencies(packageName, registryInfo);
      
      // 4. Получаем статистику загрузок
      const downloadStats = await this.getDownloadStats(packageName);
      
      // 5. Получаем информацию с GitHub (если есть)
      const githubInfo = await this.getGithubInfo(registryInfo.repository);
      
      // 6. Объединяем всю информацию
      const completePackageInfo = {
        ...registryInfo,
        ...auditInfo,
        ...dependencyAnalysis,
        ...downloadStats,
        ...githubInfo,
        
        // Метаданные сбора
        collected_at: new Date().toISOString(),
        collection_method: 'enhanced_with_audit',
        data_completeness: this.calculateDataCompleteness(registryInfo, auditInfo, dependencyAnalysis)
      };
      
      console.log(`✅ Полный сбор завершен: ${packageName} (${completePackageInfo.data_completeness}% данных)`);
      return completePackageInfo;
      
    } catch (error) {
      console.error(`❌ Ошибка сбора ${packageName}:`, error.message);
      throw error;
    }
  }

  // Получение информации из npm registry
  async getRegistryInfo(packageName) {
    const response = await axios.get(`https://registry.npmjs.org/${packageName}`, {
      timeout: 15000
    });

    const data = response.data;
    const latestVersion = data['dist-tags']?.latest;
    const latestVersionData = data.versions?.[latestVersion];

    return {
      name: packageName,
      description: data.description || '',
      repository: data.repository?.url || data.repository || '',
      homepage: data.homepage || '',
      license: typeof data.license === 'string' ? data.license : data.license?.type || '',
      latest_version: latestVersion,
      author: typeof data.author === 'string' ? data.author : data.author?.name || '',
      
      // Расширенная информация
      maintainers: data.maintainers || [],
      contributors: data.contributors || [],
      keywords: data.keywords || [],
      readme: data.readme || '',
      
      // Ссылки
      bugs_url: data.bugs?.url || data.bugs || '',
      npm_url: `https://www.npmjs.com/package/${packageName}`,
      
      // Техническая информация
      engines: data.engines || {},
      os_compatibility: data.os || [],
      cpu_compatibility: data.cpu || [],
      funding: data.funding || {},
      
      // Временные метки
      first_release_date: data.time?.created || '',
      last_release_date: data.time?.modified || '',
      
      // Версии и зависимости
      version_count: Object.keys(data.versions || {}).length,
      all_versions: Object.keys(data.versions || {}),
      
      // Зависимости последней версии
      dependencies: latestVersionData?.dependencies || {},
      devDependencies: latestVersionData?.devDependencies || {},
      peerDependencies: latestVersionData?.peerDependencies || {},
      optionalDependencies: latestVersionData?.optionalDependencies || {},
      
      // Размеры и файлы
      unpacked_size: latestVersionData?.dist?.unpackedSize || 0,
      file_count: latestVersionData?.dist?.fileCount || 0,
      tarball_size: latestVersionData?.dist?.size || 0,
      
      // Технические характеристики
      has_typescript: !!(latestVersionData?.types || latestVersionData?.typings),
      has_tests: !!(latestVersionData?.scripts?.test),
      main_file: latestVersionData?.main || '',
      module_file: latestVersionData?.module || '',
      types_file: latestVersionData?.types || latestVersionData?.typings || '',
      
      // Все версии с зависимостями
      versions_data: this.extractVersionsData(data.versions, data.time)
    };
  }

  // Получение информации об уязвимостях через npm audit
  async getAuditInfo(packageName) {
    try {
      // Создаем временный package.json для audit
      const tempPackageDir = path.join(this.tempDir, `audit-${packageName}-${Date.now()}`);
      fs.mkdirSync(tempPackageDir, { recursive: true });
      
      const tempPackageJson = {
        name: "temp-audit-project",
        version: "1.0.0",
        dependencies: {
          [packageName]: "latest"
        }
      };
      
      fs.writeFileSync(
        path.join(tempPackageDir, 'package.json'),
        JSON.stringify(tempPackageJson, null, 2)
      );
      
      // Устанавливаем пакет
      await execAsync('npm install --package-lock-only', { cwd: tempPackageDir });
      
      // Запускаем npm audit
      let auditResult;
      try {
        const { stdout } = await execAsync('npm audit --json', { cwd: tempPackageDir });
        auditResult = JSON.parse(stdout);
      } catch (auditError) {
        // npm audit возвращает код ошибки при наличии уязвимостей
        if (auditError.stdout) {
          auditResult = JSON.parse(auditError.stdout);
        } else {
          throw auditError;
        }
      }
      
      // Очищаем временную директорию
      fs.rmSync(tempPackageDir, { recursive: true, force: true });
      
      // Обрабатываем результаты audit
      const vulnerabilities = [];
      const auditSummary = {
        total_vulnerabilities: 0,
        critical_vulnerabilities: 0,
        high_vulnerabilities: 0,
        moderate_vulnerabilities: 0,
        low_vulnerabilities: 0,
        info_vulnerabilities: 0
      };
      
      if (auditResult.vulnerabilities) {
        for (const [vulnPackage, vulnData] of Object.entries(auditResult.vulnerabilities)) {
          if (vulnPackage === packageName || vulnData.via) {
            vulnerabilities.push({
              package_name: vulnPackage,
              severity: vulnData.severity,
              title: vulnData.title || 'Unknown vulnerability',
              overview: vulnData.overview || '',
              recommendation: vulnData.recommendation || '',
              references: vulnData.references || [],
              vulnerable_versions: vulnData.range || '',
              patched_versions: vulnData.fixAvailable ? 'Available' : 'None',
              cwe: vulnData.cwe || [],
              cvss: vulnData.cvss || {},
              reported_at: new Date().toISOString()
            });
            
            auditSummary.total_vulnerabilities++;
            auditSummary[`${vulnData.severity}_vulnerabilities`]++;
          }
        }
      }
      
      return {
        vulnerabilities,
        audit_summary: auditSummary,
        audit_metadata: auditResult.metadata || {},
        last_audit_date: new Date().toISOString()
      };
      
    } catch (error) {
      console.warn(`⚠️ Не удалось получить audit для ${packageName}:`, error.message);
      return {
        vulnerabilities: [],
        audit_summary: {
          total_vulnerabilities: 0,
          critical_vulnerabilities: 0,
          high_vulnerabilities: 0,
          moderate_vulnerabilities: 0,
          low_vulnerabilities: 0,
          info_vulnerabilities: 0
        },
        audit_error: error.message,
        last_audit_date: new Date().toISOString()
      };
    }
  }

  // Анализ всех зависимостей пакета
  async analyzeDependencies(packageName, registryInfo) {
    const allDependencies = {
      ...registryInfo.dependencies,
      ...registryInfo.devDependencies,
      ...registryInfo.peerDependencies,
      ...registryInfo.optionalDependencies
    };
    
    const dependencyAnalysis = {
      dependency_count: Object.keys(registryInfo.dependencies || {}).length,
      dev_dependency_count: Object.keys(registryInfo.devDependencies || {}).length,
      peer_dependency_count: Object.keys(registryInfo.peerDependencies || {}).length,
      optional_dependency_count: Object.keys(registryInfo.optionalDependencies || {}).length,
      total_dependencies: Object.keys(allDependencies).length,
      
      dependency_tree: {},
      dependency_conflicts: [],
      circular_dependencies: [],
      outdated_dependencies: [],
      deprecated_dependencies: []
    };
    
    // Анализируем каждую зависимость
    for (const [depName, depVersion] of Object.entries(allDependencies)) {
      try {
        const depInfo = await this.getBasicPackageInfo(depName);
        if (depInfo) {
          dependencyAnalysis.dependency_tree[depName] = {
            requested_version: depVersion,
            latest_version: depInfo.latest_version,
            is_outdated: this.isVersionOutdated(depVersion, depInfo.latest_version),
            is_deprecated: depInfo.deprecated || false,
            vulnerabilities_count: 0 // Будет заполнено позже
          };
          
          if (dependencyAnalysis.dependency_tree[depName].is_outdated) {
            dependencyAnalysis.outdated_dependencies.push({
              name: depName,
              requested: depVersion,
              latest: depInfo.latest_version
            });
          }
        }
      } catch (error) {
        console.warn(`⚠️ Не удалось проанализировать зависимость ${depName}:`, error.message);
      }
    }
    
    return dependencyAnalysis;
  }

  // Получение статистики загрузок
  async getDownloadStats(packageName) {
    try {
      const [weeklyResponse, monthlyResponse, yearlyResponse] = await Promise.allSettled([
        axios.get(`https://api.npmjs.org/downloads/point/last-week/${packageName}`, { timeout: 5000 }),
        axios.get(`https://api.npmjs.org/downloads/point/last-month/${packageName}`, { timeout: 5000 }),
        axios.get(`https://api.npmjs.org/downloads/point/last-year/${packageName}`, { timeout: 5000 })
      ]);
      
      return {
        weekly_downloads: weeklyResponse.status === 'fulfilled' ? weeklyResponse.value.data.downloads || 0 : 0,
        monthly_downloads: monthlyResponse.status === 'fulfilled' ? monthlyResponse.value.data.downloads || 0 : 0,
        yearly_downloads: yearlyResponse.status === 'fulfilled' ? yearlyResponse.value.data.downloads || 0 : 0,
        download_count: weeklyResponse.status === 'fulfilled' ? weeklyResponse.value.data.downloads || 0 : 0
      };
    } catch (error) {
      return {
        weekly_downloads: 0,
        monthly_downloads: 0,
        yearly_downloads: 0,
        download_count: 0
      };
    }
  }

  // Получение информации с GitHub
  async getGithubInfo(repositoryUrl) {
    if (!repositoryUrl) return {};
    
    try {
      const githubMatch = repositoryUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
      if (!githubMatch) return {};
      
      const [, owner, repo] = githubMatch;
      const cleanRepo = repo.replace(/\.git$/, '');
      
      const response = await axios.get(`https://api.github.com/repos/${owner}/${cleanRepo}`, {
        timeout: 5000,
        headers: {
          'User-Agent': 'Package-Analyzer'
        }
      });
      
      const data = response.data;
      
      return {
        github_stars: data.stargazers_count || 0,
        github_forks: data.forks_count || 0,
        github_issues: data.open_issues_count || 0,
        github_watchers: data.watchers_count || 0,
        last_commit_date: data.pushed_at || '',
        github_language: data.language || '',
        github_topics: data.topics || [],
        github_archived: data.archived || false,
        github_disabled: data.disabled || false
      };
    } catch (error) {
      return {};
    }
  }

  // Вспомогательные методы
  extractVersionsData(versions, timeData) {
    const versionsArray = [];
    
    for (const [version, versionData] of Object.entries(versions || {})) {
      versionsArray.push({
        version,
        dependencies: versionData.dependencies || {},
        devDependencies: versionData.devDependencies || {},
        peerDependencies: versionData.peerDependencies || {},
        optionalDependencies: versionData.optionalDependencies || {},
        published_at: timeData?.[version] || '',
        deprecated: versionData.deprecated || false,
        engines: versionData.engines || {},
        scripts: Object.keys(versionData.scripts || {}),
        main: versionData.main || '',
        module: versionData.module || '',
        types: versionData.types || versionData.typings || ''
      });
    }
    
    return versionsArray;
  }

  async getBasicPackageInfo(packageName) {
    if (this.dependencyCache.has(packageName)) {
      return this.dependencyCache.get(packageName);
    }
    
    try {
      const response = await axios.get(`https://registry.npmjs.org/${packageName}`, {
        timeout: 5000
      });
      
      const info = {
        latest_version: response.data['dist-tags']?.latest,
        deprecated: response.data.versions?.[response.data['dist-tags']?.latest]?.deprecated || false
      };
      
      this.dependencyCache.set(packageName, info);
      return info;
    } catch (error) {
      return null;
    }
  }

  isVersionOutdated(requested, latest) {
    // Упрощенная проверка - можно улучшить с semver
    return requested !== latest && !requested.includes(latest);
  }

  calculateDataCompleteness(registryInfo, auditInfo, dependencyAnalysis) {
    let score = 0;
    let maxScore = 10;
    
    if (registryInfo.description) score++;
    if (registryInfo.repository) score++;
    if (registryInfo.license) score++;
    if (registryInfo.keywords?.length > 0) score++;
    if (registryInfo.readme) score++;
    if (auditInfo.vulnerabilities !== undefined) score++;
    if (dependencyAnalysis.dependency_tree) score++;
    if (registryInfo.weekly_downloads > 0) score++;
    if (registryInfo.github_stars !== undefined) score++;
    if (registryInfo.versions_data?.length > 0) score++;
    
    return Math.round((score / maxScore) * 100);
  }
}

module.exports = EnhancedPackageCollector;
