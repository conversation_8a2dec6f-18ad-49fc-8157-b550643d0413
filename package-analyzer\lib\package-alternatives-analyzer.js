// Анализатор альтернатив пакетов с максимально качественным подбором
const axios = require('axios');

class PackageAlternativesAnalyzer {
  constructor(database) {
    this.db = database;
    this.alternativesCache = new Map();
    this.similarityThreshold = 0.3;
    this.maxAlternatives = 10;
  }

  // Поиск альтернатив для пакета
  async findAlternatives(packageName, options = {}) {
    const {
      includeDeprecated = false,
      minDownloads = 1000,
      maxAge = 365, // дней
      includeExperimental = false,
      preferredLicenses = ['MIT', 'Apache-2.0', 'BSD-3-Clause', 'ISC'],
      requireTypeScript = false,
      requireTests = false
    } = options;

    console.log(`🔍 Поиск альтернатив для пакета: ${packageName}`);

    try {
      // Получаем информацию о целевом пакете
      const targetPackage = await this.db.getPackageDetails(packageName);
      if (!targetPackage) {
        throw new Error(`Пакет ${packageName} не найден в базе данных`);
      }

      // Ищем альтернативы по разным критериям
      const alternatives = await this.searchAlternatives(targetPackage, {
        includeDeprecated,
        minDownloads,
        maxAge,
        includeExperimental,
        preferredLicenses,
        requireTypeScript,
        requireTests
      });

      // Сортируем по релевантности
      const sortedAlternatives = this.rankAlternatives(targetPackage, alternatives);

      // Ограничиваем количество результатов
      const limitedAlternatives = sortedAlternatives.slice(0, this.maxAlternatives);

      console.log(`✅ Найдено ${limitedAlternatives.length} альтернатив для ${packageName}`);

      return {
        targetPackage: {
          name: targetPackage.name,
          description: targetPackage.description,
          version: targetPackage.latest_version,
          downloads: targetPackage.weekly_downloads,
          license: targetPackage.license,
          keywords: targetPackage.keywords
        },
        alternatives: limitedAlternatives,
        searchCriteria: options,
        totalFound: alternatives.length
      };

    } catch (error) {
      console.error(`❌ Ошибка поиска альтернатив для ${packageName}:`, error.message);
      throw error;
    }
  }

  // Поиск альтернатив по различным критериям
  async searchAlternatives(targetPackage, options) {
    const alternatives = new Map();

    // 1. Поиск по ключевым словам
    if (targetPackage.keywords && targetPackage.keywords.length > 0) {
      const keywordAlternatives = await this.findByKeywords(targetPackage.keywords, targetPackage.name);
      keywordAlternatives.forEach(pkg => {
        if (!alternatives.has(pkg.name)) {
          alternatives.set(pkg.name, { ...pkg, matchReasons: ['keywords'] });
        } else {
          alternatives.get(pkg.name).matchReasons.push('keywords');
        }
      });
    }

    // 2. Поиск по описанию
    if (targetPackage.description) {
      const descriptionAlternatives = await this.findByDescription(targetPackage.description, targetPackage.name);
      descriptionAlternatives.forEach(pkg => {
        if (!alternatives.has(pkg.name)) {
          alternatives.set(pkg.name, { ...pkg, matchReasons: ['description'] });
        } else {
          alternatives.get(pkg.name).matchReasons.push('description');
        }
      });
    }

    // 3. Поиск по зависимостям (пакеты с похожими зависимостями)
    const dependencyAlternatives = await this.findBySimilarDependencies(targetPackage, targetPackage.name);
    dependencyAlternatives.forEach(pkg => {
      if (!alternatives.has(pkg.name)) {
        alternatives.set(pkg.name, { ...pkg, matchReasons: ['dependencies'] });
      } else {
        alternatives.get(pkg.name).matchReasons.push('dependencies');
      }
    });

    // 4. Поиск по категории/функциональности
    const categoryAlternatives = await this.findByCategory(targetPackage, targetPackage.name);
    categoryAlternatives.forEach(pkg => {
      if (!alternatives.has(pkg.name)) {
        alternatives.set(pkg.name, { ...pkg, matchReasons: ['category'] });
      } else {
        alternatives.get(pkg.name).matchReasons.push('category');
      }
    });

    // Фильтруем по критериям
    const filteredAlternatives = Array.from(alternatives.values()).filter(pkg => {
      return this.passesFilters(pkg, options);
    });

    return filteredAlternatives;
  }

  // Поиск по ключевым словам
  async findByKeywords(keywords, excludePackage) {
    const results = [];
    
    try {
      // Получаем пакеты с похожими ключевыми словами
      const packages = await this.db.searchPackagesByKeywords(keywords);
      
      for (const pkg of packages) {
        if (pkg.name === excludePackage) continue;
        
        const commonKeywords = this.getCommonKeywords(keywords, pkg.keywords || []);
        if (commonKeywords.length > 0) {
          results.push({
            ...pkg,
            similarity: commonKeywords.length / Math.max(keywords.length, pkg.keywords?.length || 1),
            commonKeywords
          });
        }
      }
    } catch (error) {
      console.warn('⚠️ Ошибка поиска по ключевым словам:', error.message);
    }

    return results;
  }

  // Поиск по описанию
  async findByDescription(description, excludePackage) {
    const results = [];
    
    try {
      // Извлекаем ключевые слова из описания
      const descriptionKeywords = this.extractKeywordsFromText(description);
      
      // Ищем пакеты с похожими описаниями
      const packages = await this.db.searchPackagesByDescription(descriptionKeywords);
      
      for (const pkg of packages) {
        if (pkg.name === excludePackage) continue;
        
        const similarity = this.calculateTextSimilarity(description, pkg.description || '');
        if (similarity > this.similarityThreshold) {
          results.push({
            ...pkg,
            similarity,
            matchType: 'description'
          });
        }
      }
    } catch (error) {
      console.warn('⚠️ Ошибка поиска по описанию:', error.message);
    }

    return results;
  }

  // Поиск по похожим зависимостям
  async findBySimilarDependencies(targetPackage, excludePackage) {
    const results = [];
    
    try {
      // Получаем зависимости целевого пакета
      const targetDeps = this.extractDependencies(targetPackage);
      
      if (targetDeps.length === 0) return results;
      
      // Ищем пакеты с похожими зависимостями
      const packages = await this.db.searchPackagesByDependencies(targetDeps);
      
      for (const pkg of packages) {
        if (pkg.name === excludePackage) continue;
        
        const pkgDeps = this.extractDependencies(pkg);
        const commonDeps = this.getCommonDependencies(targetDeps, pkgDeps);
        
        if (commonDeps.length > 0) {
          const similarity = commonDeps.length / Math.max(targetDeps.length, pkgDeps.length);
          results.push({
            ...pkg,
            similarity,
            commonDependencies: commonDeps,
            matchType: 'dependencies'
          });
        }
      }
    } catch (error) {
      console.warn('⚠️ Ошибка поиска по зависимостям:', error.message);
    }

    return results;
  }

  // Поиск по категории
  async findByCategory(targetPackage, excludePackage) {
    const results = [];
    
    try {
      // Определяем категорию пакета
      const category = this.determinePackageCategory(targetPackage);
      
      if (!category) return results;
      
      // Ищем пакеты той же категории
      const packages = await this.db.searchPackagesByCategory(category);
      
      for (const pkg of packages) {
        if (pkg.name === excludePackage) continue;
        
        results.push({
          ...pkg,
          category,
          similarity: 0.5, // Базовая схожесть по категории
          matchType: 'category'
        });
      }
    } catch (error) {
      console.warn('⚠️ Ошибка поиска по категории:', error.message);
    }

    return results;
  }

  // Ранжирование альтернатив по релевантности
  rankAlternatives(targetPackage, alternatives) {
    return alternatives.map(alt => {
      let score = 0;
      
      // Базовая схожесть
      score += (alt.similarity || 0) * 40;
      
      // Количество причин совпадения
      score += (alt.matchReasons?.length || 1) * 10;
      
      // Популярность (нормализованная)
      const targetDownloads = targetPackage.weekly_downloads || 1;
      const altDownloads = alt.weekly_downloads || 1;
      const popularityRatio = Math.min(altDownloads / targetDownloads, 2); // Максимум 2x
      score += popularityRatio * 15;
      
      // Качество пакета
      score += (alt.quality_score || 0) * 20;
      
      // Поддержка
      score += (alt.maintenance_score || 0) * 10;
      
      // Бонусы
      if (alt.has_typescript) score += 5;
      if (alt.has_tests) score += 5;
      if (alt.license && ['MIT', 'Apache-2.0', 'BSD-3-Clause'].includes(alt.license)) score += 3;
      
      // Штрафы
      if (alt.deprecated) score -= 20;
      if (!alt.weekly_downloads || alt.weekly_downloads < 100) score -= 10;
      
      return {
        ...alt,
        relevanceScore: Math.max(0, Math.min(100, score)),
        recommendation: this.generateRecommendation(targetPackage, alt)
      };
    }).sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  // Проверка фильтров
  passesFilters(pkg, options) {
    // Проверяем deprecated
    if (!options.includeDeprecated && pkg.deprecated) return false;
    
    // Проверяем минимальные загрузки
    if ((pkg.weekly_downloads || 0) < options.minDownloads) return false;
    
    // Проверяем возраст последнего релиза
    if (pkg.last_release_date) {
      const daysSinceRelease = (Date.now() - new Date(pkg.last_release_date)) / (1000 * 60 * 60 * 24);
      if (daysSinceRelease > options.maxAge) return false;
    }
    
    // Проверяем TypeScript
    if (options.requireTypeScript && !pkg.has_typescript) return false;
    
    // Проверяем тесты
    if (options.requireTests && !pkg.has_tests) return false;
    
    // Проверяем лицензию
    if (options.preferredLicenses.length > 0 && !options.preferredLicenses.includes(pkg.license)) {
      return false;
    }
    
    return true;
  }

  // Вспомогательные методы
  getCommonKeywords(keywords1, keywords2) {
    return keywords1.filter(k => keywords2.includes(k));
  }

  extractKeywordsFromText(text) {
    if (!text) return [];
    
    return text.toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 3)
      .slice(0, 10);
  }

  calculateTextSimilarity(text1, text2) {
    if (!text1 || !text2) return 0;
    
    const words1 = this.extractKeywordsFromText(text1);
    const words2 = this.extractKeywordsFromText(text2);
    
    const common = words1.filter(w => words2.includes(w));
    return common.length / Math.max(words1.length, words2.length);
  }

  extractDependencies(pkg) {
    const deps = [];
    
    if (pkg.versions && pkg.versions.length > 0) {
      const latestVersion = pkg.versions[pkg.versions.length - 1];
      if (latestVersion.dependencies) {
        deps.push(...Object.keys(latestVersion.dependencies));
      }
    }
    
    return deps;
  }

  getCommonDependencies(deps1, deps2) {
    return deps1.filter(d => deps2.includes(d));
  }

  determinePackageCategory(pkg) {
    const keywords = pkg.keywords || [];
    const description = (pkg.description || '').toLowerCase();
    
    // Определяем категорию на основе ключевых слов и описания
    if (keywords.includes('react') || description.includes('react')) return 'react';
    if (keywords.includes('vue') || description.includes('vue')) return 'vue';
    if (keywords.includes('angular') || description.includes('angular')) return 'angular';
    if (keywords.includes('testing') || keywords.includes('test')) return 'testing';
    if (keywords.includes('build') || keywords.includes('webpack')) return 'build-tools';
    if (keywords.includes('utility') || keywords.includes('util')) return 'utilities';
    if (keywords.includes('cli') || description.includes('command line')) return 'cli';
    
    return null;
  }

  generateRecommendation(targetPackage, alternative) {
    const reasons = [];
    
    if (alternative.weekly_downloads > (targetPackage.weekly_downloads || 0)) {
      reasons.push('более популярен');
    }
    
    if (alternative.quality_score > (targetPackage.quality_score || 0)) {
      reasons.push('лучшее качество');
    }
    
    if (alternative.has_typescript && !targetPackage.has_typescript) {
      reasons.push('поддержка TypeScript');
    }
    
    if (alternative.maintenance_score > (targetPackage.maintenance_score || 0)) {
      reasons.push('лучшая поддержка');
    }
    
    if (reasons.length === 0) {
      return 'Альтернативный пакет с похожей функциональностью';
    }
    
    return `Рекомендуется: ${reasons.join(', ')}`;
  }
}

module.exports = PackageAlternativesAnalyzer;
