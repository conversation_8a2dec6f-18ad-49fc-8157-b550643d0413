// Анализатор совместимости версий и поиск альтернатив с npm audit
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');
const util = require('util');

const execAsync = util.promisify(exec);

let semver;
try {
  semver = require('semver');
} catch (error) {
  // Fallback если semver не установлен
  semver = {
    valid: (version) => /^\d+\.\d+\.\d+/.test(version),
    satisfies: (version, range) => true, // Упрощенная проверка
    lt: (v1, v2) => v1 < v2,
    major: (version) => parseInt(version.split('.')[0]) || 0,
    rcompare: (a, b) => b.localeCompare(a),
    validRange: (range) => range,
    intersects: (r1, r2) => true,
    coerce: (version) => version
  };
}

class VersionCompatibilityAnalyzer {
  constructor(database) {
    this.db = database;
    this.compatibilityCache = new Map();
    this.alternativesCache = new Map();
    this.tempDir = path.join(os.tmpdir(), 'package-analyzer-audit');

    // Создаем временную директорию для npm audit
    if (!fs.existsSync(this.tempDir)) {
      fs.mkdirSync(this.tempDir, { recursive: true });
    }
  }

  // Основной анализ совместимости package.json
  async analyzePackageCompatibility(packageJson, options = {}) {
    const {
      checkDevDependencies = true,
      findAlternatives = true,
      deepAnalysis = true
    } = options;

    try {
      console.log('🔍 Запуск полного анализа совместимости версий...');

    const dependencies = packageJson.dependencies || {};
    const devDependencies = checkDevDependencies ? (packageJson.devDependencies || {}) : {};
    const peerDependencies = packageJson.peerDependencies || {};
    const optionalDependencies = packageJson.optionalDependencies || {};

    const allDependencies = {
      ...dependencies,
      ...devDependencies,
      ...peerDependencies,
      ...optionalDependencies
    };

    const results = {
      summary: {
        totalDependencies: Object.keys(allDependencies).length,
        compatibleDependencies: 0,
        incompatibleDependencies: 0,
        unknownDependencies: 0,
        alternativesFound: 0,
        riskLevel: 'low'
      },
      dependencies: {},
      conflicts: [],
      recommendations: [],
      alternatives: {},
      dependencyTree: {},
      securityIssues: []
    };

    // Анализируем каждую зависимость
    for (const [depName, requestedVersion] of Object.entries(allDependencies)) {
      console.log(`📦 Анализ ${depName}@${requestedVersion}...`);

      const analysis = await this.analyzeSingleDependency(
        depName,
        requestedVersion,
        packageJson,
        deepAnalysis
      );

      results.dependencies[depName] = analysis;

      // Обновляем счетчики
      if (analysis.status === 'compatible') {
        results.summary.compatibleDependencies++;
      } else if (analysis.status === 'incompatible') {
        results.summary.incompatibleDependencies++;

        // Ищем альтернативы для несовместимых пакетов
        if (findAlternatives) {
          const alternatives = await this.findAlternatives(depName, requestedVersion);
          if (alternatives.length > 0) {
            results.alternatives[depName] = alternatives;
            results.summary.alternativesFound++;
          }
        }
      } else {
        results.summary.unknownDependencies++;
      }

      // Добавляем конфликты
      if (analysis.conflicts && analysis.conflicts.length > 0) {
        results.conflicts.push(...analysis.conflicts);
      }

      // Добавляем рекомендации
      if (analysis.recommendations && analysis.recommendations.length > 0) {
        results.recommendations.push(...analysis.recommendations);
      }

      // Добавляем проблемы безопасности
      if (analysis.securityIssues && analysis.securityIssues.length > 0) {
        results.securityIssues.push(...analysis.securityIssues);
      }

      // Строим дерево зависимостей
      if (deepAnalysis && analysis.subDependencies) {
        results.dependencyTree[depName] = analysis.subDependencies;
      }
    }

    // Анализируем конфликты между зависимостями
    const crossConflicts = await this.analyzeCrossDependencyConflicts(allDependencies);
    results.conflicts.push(...crossConflicts);

    // Вычисляем общий уровень риска
    results.summary.riskLevel = this.calculateOverallRisk(results);

      console.log(`✅ Анализ завершен: ${results.summary.compatibleDependencies}/${results.summary.totalDependencies} совместимых`);

      return results;

    } catch (error) {
      console.error('❌ Ошибка анализа совместимости:', error.message);

      // Возвращаем базовый результат при ошибке
      const dependencies = packageJson.dependencies || {};
      const devDependencies = checkDevDependencies ? (packageJson.devDependencies || {}) : {};
      const peerDependencies = packageJson.peerDependencies || {};
      const optionalDependencies = packageJson.optionalDependencies || {};
      const allDeps = { ...dependencies, ...devDependencies, ...peerDependencies, ...optionalDependencies };

      return {
        summary: {
          totalDependencies: Object.keys(allDeps).length,
          compatibleDependencies: Object.keys(allDeps).length,
          incompatibleDependencies: 0,
          unknownDependencies: 0,
          alternativesFound: 0,
          riskLevel: 'low'
        },
        dependencies: {},
        conflicts: [],
        recommendations: [{
          type: 'analysis_error',
          severity: 'medium',
          message: `Ошибка анализа совместимости: ${error.message}`,
          recommendation: 'Повторите анализ позже или проверьте базу данных'
        }],
        alternatives: {},
        dependencyTree: {},
        securityIssues: []
      };
    }
  }

  // Анализ одной зависимости
  async analyzeSingleDependency(packageName, requestedVersion, parentPackageJson, deepAnalysis = true) {
    const cacheKey = `${packageName}@${requestedVersion}`;

    if (this.compatibilityCache.has(cacheKey)) {
      return this.compatibilityCache.get(cacheKey);
    }

    const analysis = {
      name: packageName,
      requestedVersion: requestedVersion,
      status: 'unknown',
      availableVersions: [],
      compatibleVersions: [],
      latestVersion: null,
      recommendedVersion: null,
      issues: [],
      conflicts: [],
      recommendations: [],
      securityIssues: [],
      subDependencies: {},
      metrics: {}
    };

    try {
      // Получаем информацию о пакете из БД
      const packageInfo = await this.db.getPackageDetails(packageName);

      if (!packageInfo) {
        analysis.status = 'unknown';
        analysis.issues.push({
          type: 'not_found',
          severity: 'high',
          message: `Пакет ${packageName} не найден в базе данных`,
          recommendation: 'Проверьте правильность названия пакета или обновите базу данных'
        });

        this.compatibilityCache.set(cacheKey, analysis);
        return analysis;
      }

      // Заполняем базовую информацию
      analysis.latestVersion = packageInfo.latest_version;
      analysis.availableVersions = packageInfo.versions.map(v => v.version);
      analysis.metrics = {
        totalVersions: packageInfo.versions.length,
        weeklyDownloads: packageInfo.weekly_downloads || 0,
        githubStars: packageInfo.github_stars || 0,
        qualityScore: packageInfo.quality_score || 0,
        maintenanceScore: packageInfo.maintenance_score || 0
      };

      // Проверяем совместимость версий
      const compatibilityResult = this.checkVersionCompatibility(
        requestedVersion,
        analysis.availableVersions,
        analysis.latestVersion
      );

      analysis.status = compatibilityResult.status;
      analysis.compatibleVersions = compatibilityResult.compatibleVersions;
      analysis.recommendedVersion = compatibilityResult.recommendedVersion;
      analysis.issues.push(...compatibilityResult.issues);
      analysis.recommendations.push(...compatibilityResult.recommendations);

      // Проверяем безопасность
      if (packageInfo.vulnerabilities && packageInfo.vulnerabilities.length > 0) {
        analysis.securityIssues = packageInfo.vulnerabilities.map(vuln => ({
          type: 'vulnerability',
          severity: vuln.severity || 'medium',
          message: vuln.title || 'Обнаружена уязвимость безопасности',
          cve: vuln.cve,
          affectedVersions: vuln.vulnerable_versions,
          recommendation: 'Обновите до безопасной версии'
        }));
      }

      // Анализируем качество пакета
      this.analyzePackageQuality(packageInfo, analysis);

      // Глубокий анализ зависимостей
      if (deepAnalysis) {
        analysis.subDependencies = await this.analyzeSubDependencies(
          packageName,
          analysis.recommendedVersion || analysis.latestVersion,
          parentPackageJson
        );
      }

      this.compatibilityCache.set(cacheKey, analysis);
      return analysis;

    } catch (error) {
      analysis.status = 'error';
      analysis.issues.push({
        type: 'analysis_error',
        severity: 'medium',
        message: `Ошибка анализа: ${error.message}`,
        recommendation: 'Повторите анализ позже'
      });

      this.compatibilityCache.set(cacheKey, analysis);
      return analysis;
    }
  }

  // Проверка совместимости версий
  checkVersionCompatibility(requestedVersion, availableVersions, latestVersion) {
    const result = {
      status: 'unknown',
      compatibleVersions: [],
      recommendedVersion: null,
      issues: [],
      recommendations: []
    };

    try {
      // Очищаем и валидируем версии
      const validVersions = availableVersions
        .filter(v => semver.valid(v))
        .sort(semver.rcompare);

      if (validVersions.length === 0) {
        result.status = 'incompatible';
        result.issues.push({
          type: 'no_valid_versions',
          severity: 'high',
          message: 'Не найдено валидных версий пакета',
          recommendation: 'Проверьте доступность пакета'
        });
        return result;
      }

      // Находим совместимые версии
      result.compatibleVersions = validVersions.filter(version => {
        try {
          return semver.satisfies(version, requestedVersion);
        } catch (error) {
          return false;
        }
      });

      if (result.compatibleVersions.length > 0) {
        result.status = 'compatible';
        result.recommendedVersion = result.compatibleVersions[0]; // Самая новая совместимая

        // Проверяем, не устарела ли запрашиваемая версия
        if (semver.lt(result.recommendedVersion, latestVersion)) {
          result.recommendations.push({
            type: 'update_available',
            severity: 'low',
            message: `Доступна более новая версия ${latestVersion}`,
            currentVersion: result.recommendedVersion,
            latestVersion: latestVersion,
            recommendation: `Рассмотрите обновление до ${latestVersion}`
          });
        }

      } else {
        result.status = 'incompatible';

        // Пытаемся найти ближайшую совместимую версию
        const nearestVersion = this.findNearestCompatibleVersion(requestedVersion, validVersions);

        if (nearestVersion) {
          result.recommendedVersion = nearestVersion;
          result.recommendations.push({
            type: 'version_mismatch',
            severity: 'medium',
            message: `Запрашиваемая версия ${requestedVersion} недоступна`,
            requestedVersion: requestedVersion,
            nearestVersion: nearestVersion,
            recommendation: `Используйте версию ${nearestVersion} или обновите требования`
          });
        } else {
          result.issues.push({
            type: 'no_compatible_version',
            severity: 'high',
            message: `Не найдено версий, совместимых с ${requestedVersion}`,
            recommendation: 'Пересмотрите требования к версии или найдите альтернативный пакет'
          });
        }
      }

    } catch (error) {
      result.status = 'error';
      result.issues.push({
        type: 'version_parse_error',
        severity: 'medium',
        message: `Ошибка парсинга версии: ${error.message}`,
        recommendation: 'Проверьте корректность указания версии'
      });
    }

    return result;
  }

  // Поиск ближайшей совместимой версии
  findNearestCompatibleVersion(requestedVersion, availableVersions) {
    try {
      // Пытаемся найти версию, которая удовлетворяет более мягким требованиям
      const range = semver.validRange(requestedVersion);
      if (!range) return null;

      // Сортируем версии по убыванию
      const sortedVersions = availableVersions
        .filter(v => semver.valid(v))
        .sort(semver.rcompare);

      // Ищем самую новую версию, которая может подойти
      for (const version of sortedVersions) {
        // Проверяем мажорную совместимость
        const requestedMajor = semver.major(semver.coerce(requestedVersion));
        const versionMajor = semver.major(version);

        if (requestedMajor === versionMajor) {
          return version;
        }
      }

      // Если мажорная совместимость не найдена, возвращаем последнюю версию
      return sortedVersions[0];

    } catch (error) {
      return null;
    }
  }

  // Анализ качества пакета
  analyzePackageQuality(packageInfo, analysis) {
    // Проверяем популярность
    if (packageInfo.weekly_downloads < 1000) {
      analysis.issues.push({
        type: 'low_popularity',
        severity: 'low',
        message: `Низкая популярность: ${packageInfo.weekly_downloads} загрузок в неделю`,
        recommendation: 'Убедитесь в надежности пакета'
      });
    }

    // Проверяем поддержку
    if (packageInfo.maintenance_score < 0.3) {
      analysis.issues.push({
        type: 'poor_maintenance',
        severity: 'medium',
        message: `Плохая поддержка: ${(packageInfo.maintenance_score * 100).toFixed(1)}%`,
        recommendation: 'Рассмотрите альтернативные пакеты'
      });
    }

    // Проверяем качество
    if (packageInfo.quality_score < 0.4) {
      analysis.issues.push({
        type: 'low_quality',
        severity: 'medium',
        message: `Низкое качество: ${(packageInfo.quality_score * 100).toFixed(1)}%`,
        recommendation: 'Проверьте документацию и тесты'
      });
    }

    // Проверяем лицензию
    if (!packageInfo.license || packageInfo.license === 'UNLICENSED') {
      analysis.issues.push({
        type: 'no_license',
        severity: 'medium',
        message: 'Отсутствует лицензия',
        recommendation: 'Проверьте правовые аспекты использования'
      });
    }
  }

  // Анализ подзависимостей
  async analyzeSubDependencies(packageName, version, parentPackageJson, depth = 0, maxDepth = 2) {
    if (depth >= maxDepth) return {};

    try {
      const packageInfo = await this.db.getPackageDetails(packageName);
      if (!packageInfo || !packageInfo.versions) return {};

      // Находим нужную версию
      const versionInfo = packageInfo.versions.find(v => v.version === version);
      if (!versionInfo || !versionInfo.dependencies) return {};

      const subDeps = {};

      // Анализируем каждую подзависимость
      for (const [depName, depVersion] of Object.entries(versionInfo.dependencies)) {
        if (depName === packageName) continue; // Избегаем циклических зависимостей

        const subAnalysis = await this.analyzeSingleDependency(
          depName,
          depVersion,
          parentPackageJson,
          false // Отключаем глубокий анализ для подзависимостей
        );

        subDeps[depName] = {
          version: depVersion,
          status: subAnalysis.status,
          issues: subAnalysis.issues.length,
          // Рекурсивно анализируем подзависимости
          dependencies: await this.analyzeSubDependencies(depName, subAnalysis.recommendedVersion || depVersion, parentPackageJson, depth + 1, maxDepth)
        };
      }

      return subDeps;

    } catch (error) {
      console.error(`Ошибка анализа подзависимостей для ${packageName}:`, error.message);
      return {};
    }
  }

  // Анализ конфликтов между зависимостями
  async analyzeCrossDependencyConflicts(dependencies) {
    const conflicts = [];
    const dependencyVersions = new Map();

    // Собираем все версии зависимостей
    for (const [depName, depVersion] of Object.entries(dependencies)) {
      const packageInfo = await this.db.getPackageDetails(depName);
      if (!packageInfo) continue;

      // Анализируем зависимости этого пакета
      const latestVersionInfo = packageInfo.versions.find(v => v.version === packageInfo.latest_version);
      if (!latestVersionInfo || !latestVersionInfo.dependencies) continue;

      for (const [subDepName, subDepVersion] of Object.entries(latestVersionInfo.dependencies)) {
        if (!dependencyVersions.has(subDepName)) {
          dependencyVersions.set(subDepName, []);
        }
        dependencyVersions.get(subDepName).push({
          requiredBy: depName,
          version: subDepVersion
        });
      }
    }

    // Ищем конфликты версий
    for (const [depName, requirements] of dependencyVersions.entries()) {
      if (requirements.length <= 1) continue;

      const conflictingRequirements = [];

      for (let i = 0; i < requirements.length; i++) {
        for (let j = i + 1; j < requirements.length; j++) {
          const req1 = requirements[i];
          const req2 = requirements[j];

          // Проверяем совместимость версий
          if (!this.areVersionsCompatible(req1.version, req2.version)) {
            conflictingRequirements.push({ req1, req2 });
          }
        }
      }

      if (conflictingRequirements.length > 0) {
        conflicts.push({
          type: 'version_conflict',
          package: depName,
          severity: 'high',
          message: `Конфликт версий для ${depName}`,
          conflicts: conflictingRequirements,
          recommendation: 'Обновите зависимости для разрешения конфликта'
        });
      }
    }

    return conflicts;
  }

  // Проверка совместимости двух версий
  areVersionsCompatible(version1, version2) {
    try {
      const range1 = semver.validRange(version1);
      const range2 = semver.validRange(version2);

      if (!range1 || !range2) return false;

      // Проверяем пересечение диапазонов
      return semver.intersects(range1, range2);
    } catch (error) {
      return false;
    }
  }

  // Поиск альтернативных пакетов
  async findAlternatives(packageName, requestedVersion) {
    const cacheKey = `alt_${packageName}`;

    if (this.alternativesCache.has(cacheKey)) {
      return this.alternativesCache.get(cacheKey);
    }

    const alternatives = [];

    try {
      // Получаем информацию о пакете
      const packageInfo = await this.db.getPackageDetails(packageName);
      if (!packageInfo) return alternatives;

      // Ищем пакеты с похожими ключевыми словами
      const keywords = packageInfo.keywords || [];
      if (keywords.length > 0) {
        const similarPackages = await this.findPackagesByKeywords(keywords, packageName);
        alternatives.push(...similarPackages);
      }

      // Ищем пакеты с похожим описанием
      if (packageInfo.description) {
        const descriptionAlternatives = await this.findPackagesByDescription(packageInfo.description, packageName);
        alternatives.push(...descriptionAlternatives);
      }

      // Сортируем по релевантности
      alternatives.sort((a, b) => b.relevanceScore - a.relevanceScore);

      // Ограничиваем количество альтернатив
      const limitedAlternatives = alternatives.slice(0, 5);

      this.alternativesCache.set(cacheKey, limitedAlternatives);
      return limitedAlternatives;

    } catch (error) {
      console.error(`Ошибка поиска альтернатив для ${packageName}:`, error.message);
      return alternatives;
    }
  }

  // Поиск пакетов по ключевым словам
  async findPackagesByKeywords(keywords, excludePackage) {
    // Это упрощенная реализация - в реальности нужен полнотекстовый поиск
    return [];
  }

  // Поиск пакетов по описанию
  async findPackagesByDescription(description, excludePackage) {
    // Это упрощенная реализация - в реальности нужен полнотекстовый поиск
    return [];
  }

  // Вычисление общего уровня риска
  calculateOverallRisk(results) {
    const { summary, conflicts, securityIssues } = results;

    let riskScore = 0;

    // Несовместимые зависимости
    riskScore += summary.incompatibleDependencies * 3;

    // Неизвестные зависимости
    riskScore += summary.unknownDependencies * 2;

    // Конфликты
    riskScore += conflicts.length * 2;

    // Проблемы безопасности
    riskScore += securityIssues.length * 4;

    // Нормализуем к общему количеству зависимостей
    const normalizedScore = riskScore / Math.max(summary.totalDependencies, 1);

    if (normalizedScore >= 2) return 'high';
    if (normalizedScore >= 1) return 'medium';
    return 'low';
  }

  // Анализ совместимости с npm audit
  async analyzeWithNpmAudit(packageJson, options = {}) {
    console.log('🔍 Запуск анализа совместимости с npm audit...');

    try {
      // Создаем временный проект для анализа
      const tempProjectDir = path.join(this.tempDir, `audit-${Date.now()}`);
      fs.mkdirSync(tempProjectDir, { recursive: true });

      // Создаем package.json для анализа
      const tempPackageJson = {
        name: "temp-compatibility-analysis",
        version: "1.0.0",
        dependencies: packageJson.dependencies || {},
        devDependencies: packageJson.devDependencies || {},
        peerDependencies: packageJson.peerDependencies || {},
        optionalDependencies: packageJson.optionalDependencies || {}
      };

      fs.writeFileSync(
        path.join(tempProjectDir, 'package.json'),
        JSON.stringify(tempPackageJson, null, 2)
      );

      // Устанавливаем зависимости
      console.log('📦 Установка зависимостей для анализа...');
      await execAsync('npm install --package-lock-only', { cwd: tempProjectDir });

      // Запускаем npm audit
      console.log('🔍 Запуск npm audit...');
      let auditResult;
      try {
        const { stdout } = await execAsync('npm audit --json', { cwd: tempProjectDir });
        auditResult = JSON.parse(stdout);
      } catch (auditError) {
        // npm audit возвращает код ошибки при наличии уязвимостей
        if (auditError.stdout) {
          auditResult = JSON.parse(auditError.stdout);
        } else {
          throw auditError;
        }
      }

      // Анализируем совместимость зависимостей
      console.log('🔄 Анализ совместимости зависимостей...');
      let compatibilityResult;
      try {
        const { stdout } = await execAsync('npm ls --json --depth=0', { cwd: tempProjectDir });
        compatibilityResult = JSON.parse(stdout);
      } catch (lsError) {
        // npm ls может возвращать ошибку при конфликтах
        if (lsError.stdout) {
          compatibilityResult = JSON.parse(lsError.stdout);
        } else {
          compatibilityResult = { dependencies: {} };
        }
      }

      // Очищаем временную директорию
      fs.rmSync(tempProjectDir, { recursive: true, force: true });

      // Обрабатываем результаты
      const analysisResult = this.processAuditResults(auditResult, compatibilityResult, packageJson);

      console.log(`✅ Анализ с npm audit завершен: найдено ${analysisResult.summary.totalVulnerabilities || 0} уязвимостей`);
      return analysisResult;

    } catch (error) {
      console.error('❌ Ошибка анализа с npm audit:', error.message);

      // Fallback на обычный анализ
      return await this.analyzePackageCompatibility(packageJson, options);
    }
  }

  // Обработка результатов npm audit
  processAuditResults(auditResult, compatibilityResult, originalPackageJson) {
    const vulnerabilities = [];
    const conflicts = [];
    const dependencies = {};
    const securityIssues = [];

    // Обрабатываем уязвимости
    if (auditResult.vulnerabilities) {
      for (const [packageName, vulnData] of Object.entries(auditResult.vulnerabilities)) {
        vulnerabilities.push({
          package: packageName,
          severity: vulnData.severity,
          title: vulnData.title || 'Unknown vulnerability',
          overview: vulnData.overview || '',
          recommendation: vulnData.recommendation || '',
          vulnerable_versions: vulnData.range || '',
          fix_available: vulnData.fixAvailable || false
        });

        securityIssues.push({
          type: 'vulnerability',
          package: packageName,
          severity: vulnData.severity,
          message: vulnData.title || 'Security vulnerability detected',
          recommendation: vulnData.recommendation || 'Update to a secure version',
          cve: vulnData.cwe || [],
          cvss: vulnData.cvss || {}
        });
      }
    }

    // Обрабатываем конфликты зависимостей
    if (compatibilityResult.problems) {
      compatibilityResult.problems.forEach(problem => {
        conflicts.push({
          type: 'dependency_conflict',
          message: problem,
          severity: 'medium',
          recommendation: 'Resolve dependency conflicts'
        });
      });
    }

    // Анализируем установленные зависимости
    const installedDeps = compatibilityResult.dependencies || {};
    const requestedDeps = {
      ...originalPackageJson.dependencies,
      ...originalPackageJson.devDependencies,
      ...originalPackageJson.peerDependencies,
      ...originalPackageJson.optionalDependencies
    };

    let compatibleCount = 0;
    let incompatibleCount = 0;
    let unknownCount = 0;

    for (const [depName, requestedVersion] of Object.entries(requestedDeps)) {
      const installedInfo = installedDeps[depName];

      if (installedInfo) {
        const isCompatible = !installedInfo.problems && !installedInfo.invalid;

        dependencies[depName] = {
          requestedVersion,
          installedVersion: installedInfo.version,
          status: isCompatible ? 'compatible' : 'incompatible',
          issues: installedInfo.problems || [],
          path: installedInfo.path || '',
          resolved: installedInfo.resolved || ''
        };

        if (isCompatible) {
          compatibleCount++;
        } else {
          incompatibleCount++;
        }
      } else {
        dependencies[depName] = {
          requestedVersion,
          installedVersion: null,
          status: 'unknown',
          issues: ['Package not found or not installable'],
          path: '',
          resolved: ''
        };
        unknownCount++;
      }
    }

    // Определяем уровень риска
    const totalVulnerabilities = vulnerabilities.length;
    const criticalVulns = vulnerabilities.filter(v => v.severity === 'critical').length;
    const highVulns = vulnerabilities.filter(v => v.severity === 'high').length;

    let riskLevel = 'low';
    if (criticalVulns > 0 || incompatibleCount > 5) {
      riskLevel = 'high';
    } else if (highVulns > 0 || incompatibleCount > 2 || totalVulnerabilities > 5) {
      riskLevel = 'medium';
    }

    return {
      summary: {
        totalDependencies: Object.keys(requestedDeps).length,
        compatibleDependencies: compatibleCount,
        incompatibleDependencies: incompatibleCount,
        unknownDependencies: unknownCount,
        totalVulnerabilities,
        criticalVulnerabilities: criticalVulns,
        highVulnerabilities: highVulns,
        moderateVulnerabilities: vulnerabilities.filter(v => v.severity === 'moderate').length,
        lowVulnerabilities: vulnerabilities.filter(v => v.severity === 'low').length,
        riskLevel,
        alternativesFound: 0
      },
      dependencies,
      conflicts,
      vulnerabilities,
      securityIssues,
      recommendations: this.generateRecommendations(vulnerabilities, conflicts, dependencies),
      alternatives: {},
      dependencyTree: installedDeps,
      auditMetadata: auditResult.metadata || {}
    };
  }

  // Генерация рекомендаций на основе анализа
  generateRecommendations(vulnerabilities, conflicts, dependencies) {
    const recommendations = [];

    // Рекомендации по безопасности
    vulnerabilities.forEach(vuln => {
      if (vuln.severity === 'critical' || vuln.severity === 'high') {
        recommendations.push({
          type: 'security',
          priority: 'high',
          package: vuln.package,
          message: `Критическая уязвимость в ${vuln.package}`,
          recommendation: vuln.recommendation || 'Немедленно обновите пакет',
          action: 'update_package'
        });
      }
    });

    // Рекомендации по конфликтам
    conflicts.forEach(conflict => {
      recommendations.push({
        type: 'conflict',
        priority: 'medium',
        message: conflict.message,
        recommendation: 'Разрешите конфликты зависимостей',
        action: 'resolve_conflicts'
      });
    });

    // Рекомендации по обновлениям
    Object.entries(dependencies).forEach(([depName, depInfo]) => {
      if (depInfo.status === 'incompatible') {
        recommendations.push({
          type: 'compatibility',
          priority: 'medium',
          package: depName,
          message: `Несовместимая версия ${depName}`,
          recommendation: 'Обновите до совместимой версии',
          action: 'update_version'
        });
      }
    });

    return recommendations;
  }

  // Очистка кэша
  clearCache() {
    this.compatibilityCache.clear();
    this.alternativesCache.clear();
  }
}

module.exports = VersionCompatibilityAnalyzer;
