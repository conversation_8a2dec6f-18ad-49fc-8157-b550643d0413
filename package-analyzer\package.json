{"name": "package-analyzer", "version": "1.0.0", "description": "Tool for analyzing and resolving npm package dependency conflicts", "main": "index.js", "bin": {"package-analyzer": "./cli.js"}, "scripts": {"test": "jest", "start": "node cli.js", "dashboard": "node start-dashboard.js", "server": "node server.js", "populate-db": "node scripts/populate-db.js", "full-scan": "node scripts/full-scan.js", "lint": "eslint .", "install-cuda": "node-gyp rebuild"}, "dependencies": {"axios": "^1.9.0", "express": "^4.18.2", "semver": "^7.5.4", "sqlite3": "^5.1.6", "ws": "^8.14.2"}, "devDependencies": {"eslint": "^8.56.0", "jest": "^29.7.0", "node-gyp": "^9.0.0"}, "keywords": ["npm", "dependency", "analyzer", "conflict", "resolver"], "author": "", "license": "MIT"}