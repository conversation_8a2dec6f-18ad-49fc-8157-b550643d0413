<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Package Analyzer - Панель управления</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
        }

        .card-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-online { background: #27ae60; }
        .status-offline { background: #e74c3c; }
        .status-warning { background: #f39c12; }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            color: #7f8c8d;
            font-weight: 500;
        }

        .metric-value {
            font-weight: 600;
            color: #2c3e50;
            font-size: 1.1em;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .upload-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .upload-area {
            border: 3px dashed #3498db;
            border-radius: 10px;
            padding: 40px;
            margin: 20px 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #2980b9;
            background: rgba(52, 152, 219, 0.05);
        }

        .upload-area.dragover {
            border-color: #27ae60;
            background: rgba(39, 174, 96, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .logs-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            height: 400px;
            overflow-y: auto;
        }

        .log-entry {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            border-left: 4px solid #3498db;
        }

        .log-info { background: rgba(52, 152, 219, 0.1); border-left-color: #3498db; }
        .log-success { background: rgba(39, 174, 96, 0.1); border-left-color: #27ae60; }
        .log-warning { background: rgba(243, 156, 18, 0.1); border-left-color: #f39c12; }
        .log-error { background: rgba(231, 76, 60, 0.1); border-left-color: #e74c3c; }

        .chart-container {
            height: 200px;
            margin: 20px 0;
        }

        .task-queue {
            max-height: 300px;
            overflow-y: auto;
        }

        .task-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: rgba(52, 152, 219, 0.05);
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .task-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .task-pending { background: #f39c12; color: white; }
        .task-running { background: #3498db; color: white; }
        .task-completed { background: #27ae60; color: white; }
        .task-failed { background: #e74c3c; color: white; }

        /* Стили для загрузки файлов */
        .upload-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            margin: 30px 0;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .upload-area {
            border: 2px dashed #3498db;
            border-radius: 10px;
            padding: 40px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: rgba(52, 152, 219, 0.05);
            position: relative;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .upload-area:hover {
            border-color: #2980b9;
            background: rgba(52, 152, 219, 0.1);
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: #27ae60;
            background: rgba(39, 174, 96, 0.1);
            transform: scale(1.02);
            box-shadow: 0 10px 30px rgba(39, 174, 96, 0.3);
        }

        .upload-area.file-selected {
            border-color: #27ae60;
            background: rgba(39, 174, 96, 0.05);
        }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }
        }

        /* GPU стили */
        .gpu-indicator {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            display: inline-flex;
            align-items: center;
            gap: 4px;
        }

        .gpu-metrics {
            background: rgba(102, 126, 234, 0.1);
            border: 1px solid rgba(102, 126, 234, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin: 10px 0;
        }

        .gpu-analysis-item {
            background: rgba(102, 126, 234, 0.05);
            border-left: 4px solid #667eea;
            padding: 12px;
            margin: 8px 0;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .gpu-analysis-item:hover {
            background: rgba(102, 126, 234, 0.1);
            transform: translateX(2px);
        }

        .risk-score {
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            font-weight: bold;
            color: white;
        }

        .risk-low { background: #27ae60; }
        .risk-medium { background: #f39c12; }
        .risk-high { background: #e74c3c; }

        .metric-value.risk-low { color: #27ae60; }
        .metric-value.risk-medium { color: #f39c12; }
        .metric-value.risk-high { color: #e74c3c; }

        .severity.high { background: #e74c3c; }
        .severity.medium { background: #f39c12; }
        .severity.low { background: #27ae60; }

        .recommendation {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-cube"></i>
                Package Analyzer
            </h1>
            <p class="subtitle">Система анализа и управления зависимостями npm пакетов</p>
        </div>

        <!-- Панель статистики системы -->
        <div class="dashboard">
            <!-- Статус системы -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-server"></i>
                        Статус системы
                    </div>
                    <span class="status-indicator status-online" id="systemStatus"></span>
                </div>
                <div class="metric">
                    <span class="metric-label">Сервер</span>
                    <span class="metric-value" id="serverStatus">Онлайн</span>
                </div>
                <div class="metric">
                    <span class="metric-label">База данных</span>
                    <span class="metric-value" id="dbStatus">Подключена</span>
                </div>
                <div class="metric">
                    <span class="metric-label">GPU ускорение</span>
                    <span class="metric-value" id="gpuStatus">Проверка...</span>
                </div>
                <div class="metric" id="gpuDetails" style="display: none;">
                    <span class="metric-label">GPU модель</span>
                    <span class="metric-value" id="gpuModel">--</span>
                </div>
                <div class="metric" id="gpuMemory" style="display: none;">
                    <span class="metric-label">VRAM</span>
                    <span class="metric-value" id="gpuVram">--</span>
                </div>
                <div class="metric" id="cudaVersion" style="display: none;">
                    <span class="metric-label">CUDA версия</span>
                    <span class="metric-value" id="gpuCuda">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Время работы</span>
                    <span class="metric-value" id="uptime">--</span>
                </div>
            </div>

            <!-- Статистика анализа -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-chart-bar"></i>
                        Статистика анализа
                    </div>
                </div>
                <div class="metric">
                    <span class="metric-label">Всего пакетов в БД</span>
                    <span class="metric-value" id="totalPackages">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Анализов сегодня</span>
                    <span class="metric-value" id="todayAnalyses">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Найдено конфликтов</span>
                    <span class="metric-value" id="conflictsFound">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Разрешено конфликтов</span>
                    <span class="metric-value" id="conflictsResolved">0</span>
                </div>
            </div>

            <!-- Производительность -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-tachometer-alt"></i>
                        Производительность
                    </div>
                </div>
                <div class="metric">
                    <span class="metric-label">Использование CPU</span>
                    <span class="metric-value" id="cpuUsage">--</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="cpuProgress" style="width: 0%"></div>
                </div>
                <div class="metric">
                    <span class="metric-label">Использование памяти</span>
                    <span class="metric-value" id="memoryUsage">--</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="memoryProgress" style="width: 0%"></div>
                </div>
                <div class="metric">
                    <span class="metric-label">Скорость анализа</span>
                    <span class="metric-value" id="analysisSpeed">-- пак/сек</span>
                </div>
            </div>

            <!-- Сбор данных -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-download"></i>
                        Сбор данных
                    </div>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                    <button class="btn btn-success" onclick="startMassiveCollection()" id="massiveCollectionBtn">
                        <i class="fas fa-globe"></i>
                        Собрать ВСЕ npm пакеты
                    </button>
                    <button class="btn btn-warning" onclick="updateExistingPackages()">
                        <i class="fas fa-sync"></i>
                        Обновить существующие
                    </button>
                </div>

                <!-- Прогресс массового сбора -->
                <div id="massiveProgress" class="massive-progress" style="display: none; margin-bottom: 15px; padding: 15px; background: rgba(52, 152, 219, 0.05); border-radius: 8px;">
                    <h5 style="margin-bottom: 10px;">📊 Прогресс массового сбора всех npm пакетов</h5>
                    <div class="progress-bar" style="margin-bottom: 10px;">
                        <div class="progress-fill" id="massiveProgressFill" style="width: 0%; background: linear-gradient(90deg, #3498db, #2ecc71);"></div>
                        <span class="progress-text" id="massiveProgressText" style="position: absolute; left: 50%; transform: translateX(-50%); font-weight: bold;">0%</span>
                    </div>
                    <div class="progress-stats" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 10px; font-size: 0.9em;">
                        <div class="stat">
                            <span class="stat-label">Всего:</span>
                            <span class="stat-value" id="totalPackagesCount">0</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Обработано:</span>
                            <span class="stat-value" id="processedPackagesCount">0</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Ошибок:</span>
                            <span class="stat-value" id="failedPackagesCount">0</span>
                        </div>
                        <div class="stat">
                            <span class="stat-label">Осталось:</span>
                            <span class="stat-value" id="remainingPackagesCount">0</span>
                        </div>
                    </div>
                    <div style="margin-top: 10px; text-align: center;">
                        <button class="btn btn-danger" onclick="stopMassiveCollection()" id="stopMassiveBtn">
                            <i class="fas fa-stop"></i>
                            Остановить сбор
                        </button>
                    </div>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                    <button class="btn" onclick="collectPopularPackages()">
                        <i class="fas fa-star"></i>
                        Популярные пакеты
                    </button>
                    <button class="btn" onclick="collectByCategory()">
                        <i class="fas fa-tags"></i>
                        По категориям
                    </button>
                </div>

                <!-- Форма добавления конкретных пакетов -->
                <div style="margin-top: 15px; padding: 15px; background: rgba(52, 152, 219, 0.05); border-radius: 8px;">
                    <h5 style="margin-bottom: 10px;">Добавить конкретные пакеты:</h5>
                    <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                        <input type="text" id="packageNameInput" placeholder="Название пакета (например: react, lodash)"
                               style="flex: 1; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <button class="btn btn-success" onclick="addSpecificPackage()">
                            <i class="fas fa-plus"></i>
                            Добавить
                        </button>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <textarea id="packageListInput" placeholder="Или список пакетов (по одному на строку)&#10;react&#10;vue&#10;angular&#10;express"
                                  style="flex: 1; padding: 8px; border: 1px solid #ddd; border-radius: 4px; height: 80px; resize: vertical;"></textarea>
                        <button class="btn btn-success" onclick="addPackageList()">
                            <i class="fas fa-list"></i>
                            Добавить список
                        </button>
                    </div>
                </div>
            </div>

            <!-- Очередь задач -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-tasks"></i>
                        Очередь задач
                    </div>
                    <div>
                        <button class="btn btn-success" onclick="startFullScan()">
                            <i class="fas fa-play"></i>
                            Полное сканирование
                        </button>
                        <button class="btn btn-danger" onclick="stopAllTasks()">
                            <i class="fas fa-stop"></i>
                            Остановить все
                        </button>
                    </div>
                </div>
                <div class="task-queue" id="taskQueue">
                    <div class="task-item">
                        <span>Инициализация системы</span>
                        <span class="task-status task-completed">Завершено</span>
                    </div>
                </div>

                <!-- Прогресс текущей задачи -->
                <div id="currentTaskProgress" style="display: none; margin-top: 15px; padding: 15px; background: rgba(52, 152, 219, 0.05); border-radius: 8px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <span id="currentTaskName">Текущая задача</span>
                        <span id="currentTaskPercent">0%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="currentTaskProgressBar" style="width: 0%"></div>
                    </div>
                    <div style="margin-top: 10px; font-size: 0.9em; color: #7f8c8d;">
                        <span id="currentTaskDetails">Подготовка...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Секция загрузки файлов -->
        <div class="upload-section">
            <h2><i class="fas fa-upload"></i> Анализ package.json</h2>
            <div class="upload-area" id="uploadArea">
                <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #3498db; margin-bottom: 15px;"></i>
                <h3 style="margin-bottom: 10px;">Перетащите package.json сюда</h3>
                <p style="color: #7f8c8d; margin-bottom: 15px;">или нажмите для выбора файла</p>
                <input type="file" id="fileInput" accept=".json" style="display: none;">
                <button class="btn" onclick="document.getElementById('fileInput').click()">
                    <i class="fas fa-folder-open"></i>
                    Выбрать файл
                </button>
            </div>
            <button class="btn btn-success" id="analyzeBtn" style="margin-top: 15px;" disabled>
                <i class="fas fa-search"></i>
                Анализировать
            </button>
        </div>

        <!-- Результаты анализа -->
        <div class="card" id="resultsCard" style="display: none;">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-chart-line"></i>
                    Результаты анализа
                </div>
            </div>
            <div id="results"></div>
        </div>

        <!-- База данных пакетов -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-database"></i>
                    База данных пакетов
                </div>
                <div>
                    <input type="text" id="packageSearch" placeholder="Поиск пакетов..."
                           style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px;">
                    <button class="btn" onclick="searchPackages()">
                        <i class="fas fa-search"></i>
                        Поиск
                    </button>
                    <button class="btn btn-success" onclick="refreshPackages()">
                        <i class="fas fa-sync"></i>
                        Обновить
                    </button>
                </div>
            </div>

            <!-- Статистика базы данных -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                <div class="metric">
                    <span class="metric-label">Всего пакетов</span>
                    <span class="metric-value" id="dbTotalPackages">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Последнее обновление</span>
                    <span class="metric-value" id="dbLastUpdate">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Размер БД</span>
                    <span class="metric-value" id="dbSize">--</span>
                </div>
            </div>

            <!-- Таблица пакетов -->
            <div style="max-height: 400px; overflow-y: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead style="background: #f8f9fa; position: sticky; top: 0;">
                        <tr>
                            <th style="padding: 10px; text-align: left; border-bottom: 2px solid #dee2e6;">Название</th>
                            <th style="padding: 10px; text-align: left; border-bottom: 2px solid #dee2e6;">Версия</th>
                            <th style="padding: 10px; text-align: left; border-bottom: 2px solid #dee2e6;">Описание</th>
                            <th style="padding: 10px; text-align: left; border-bottom: 2px solid #dee2e6;">Лицензия</th>
                            <th style="padding: 10px; text-align: center; border-bottom: 2px solid #dee2e6;">Действия</th>
                        </tr>
                    </thead>
                    <tbody id="packagesTableBody">
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 20px; color: #7f8c8d;">
                                Загрузка пакетов...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Пагинация -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 15px;">
                <div id="paginationInfo">Страница 1 из 1</div>
                <div>
                    <button class="btn" onclick="previousPage()" id="prevPageBtn" disabled>
                        <i class="fas fa-chevron-left"></i>
                        Назад
                    </button>
                    <button class="btn" onclick="nextPage()" id="nextPageBtn" disabled>
                        Вперед
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Детали пакета (модальное окно) -->
        <div id="packageModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 15px; padding: 30px; max-width: 800px; max-height: 80vh; overflow-y: auto; width: 90%;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2 id="modalPackageName">Детали пакета</h2>
                    <button onclick="closePackageModal()" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
                </div>
                <div id="modalPackageContent">
                    Загрузка...
                </div>
            </div>
        </div>

        <!-- Логи системы -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-terminal"></i>
                    Логи системы
                </div>
                <div>
                    <button class="btn" onclick="clearLogs()">
                        <i class="fas fa-trash"></i>
                        Очистить
                    </button>
                    <button class="btn" onclick="toggleAutoScroll()" id="autoScrollBtn">
                        <i class="fas fa-arrow-down"></i>
                        Авто-прокрутка
                    </button>
                </div>
            </div>
            <div class="logs-container" id="logsContainer">
                <div class="log-entry log-info">
                    <strong>[INFO]</strong> Система инициализирована
                </div>
                <div class="log-entry log-success">
                    <strong>[SUCCESS]</strong> Подключение к базе данных установлено
                </div>
                <div class="log-entry log-info">
                    <strong>[INFO]</strong> GPU ускорение активировано
                </div>
            </div>
        </div>
    </div>

    <script>
        // Глобальные переменные
        let autoScroll = true;
        let ws = null;
        let systemStats = {
            uptime: 0,
            totalPackages: 0,
            todayAnalyses: 0,
            conflictsFound: 0,
            conflictsResolved: 0
        };

        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            initializeWebSocket();
            initializeFileUpload();
            startStatsUpdater();
            loadSystemStats();
        });

        // WebSocket соединение для обновлений в реальном времени
        function initializeWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;

            try {
                ws = new WebSocket(wsUrl);

                ws.onopen = function() {
                    addLog('success', 'WebSocket соединение установлено');
                };

                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                };

                ws.onclose = function() {
                    addLog('warning', 'WebSocket соединение закрыто. Попытка переподключения...');
                    setTimeout(initializeWebSocket, 5000);
                };

                ws.onerror = function(error) {
                    addLog('error', 'Ошибка WebSocket: ' + error.message);
                };
            } catch (error) {
                addLog('error', 'Не удалось установить WebSocket соединение');
            }
        }



        // Инициализация загрузки файлов
        function initializeFileUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const analyzeBtn = document.getElementById('analyzeBtn');

            // Drag & Drop
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileSelect(files[0]);
                }
            });

            // Выбор файла
            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    handleFileSelect(e.target.files[0]);
                }
            });

            // Кнопка анализа
            analyzeBtn.addEventListener('click', analyzePackage);
        }

        // Глобальная переменная для хранения выбранного файла
        let selectedFile = null;

        // Обработка выбранного файла
        function handleFileSelect(file) {
            if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
                addLog('error', 'Пожалуйста, выберите JSON файл (package.json)');
                return;
            }

            // Сохраняем файл для анализа
            selectedFile = file;

            // Обновляем интерфейс
            document.getElementById('analyzeBtn').disabled = false;
            document.getElementById('uploadArea').innerHTML = `
                <div style="text-align: center;">
                    <i class="fas fa-file-check" style="font-size: 48px; color: #27ae60; margin-bottom: 15px;"></i>
                    <h3 style="margin-bottom: 10px;">Файл готов к анализу</h3>
                    <p style="color: #7f8c8d; margin-bottom: 15px;">${file.name}</p>
                    <p style="color: #7f8c8d; font-size: 0.9em;">Размер: ${(file.size / 1024).toFixed(1)} KB</p>
                    <button class="btn" onclick="resetFileUpload()" style="margin-top: 10px;">
                        <i class="fas fa-times"></i>
                        Выбрать другой файл
                    </button>
                </div>
            `;

            addLog('success', `Файл выбран: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`);
        }

        // Сброс загрузки файла
        function resetFileUpload() {
            selectedFile = null;
            document.getElementById('fileInput').value = '';
            document.getElementById('analyzeBtn').disabled = true;

            // Восстанавливаем исходный вид области загрузки
            document.getElementById('uploadArea').innerHTML = `
                <i class="fas fa-cloud-upload-alt" style="font-size: 48px; color: #3498db; margin-bottom: 15px;"></i>
                <h3 style="margin-bottom: 10px;">Перетащите package.json сюда</h3>
                <p style="color: #7f8c8d; margin-bottom: 15px;">или нажмите для выбора файла</p>
                <input type="file" id="fileInput" accept=".json" style="display: none;">
                <button class="btn" onclick="document.getElementById('fileInput').click()">
                    <i class="fas fa-folder-open"></i>
                    Выбрать файл
                </button>
            `;

            // Переинициализируем обработчики
            initializeFileUpload();

            addLog('info', 'Загрузка файла сброшена');
        }

        // Анализ пакета
        async function analyzePackage() {
            if (!selectedFile) {
                addLog('error', 'Пожалуйста, выберите файл package.json');
                return;
            }

            try {
                addLog('info', `Начинаем анализ файла: ${selectedFile.name}...`);

                // Показываем индикатор загрузки
                document.getElementById('analyzeBtn').innerHTML = `
                    <i class="fas fa-spinner fa-spin"></i>
                    Анализируем...
                `;
                document.getElementById('analyzeBtn').disabled = true;

                const fileContent = await selectedFile.text();

                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        filePath: selectedFile.name,
                        content: fileContent
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                displayAnalysisResults(data);
                addLog('success', 'Анализ завершен успешно');

            } catch (error) {
                addLog('error', 'Ошибка при анализе: ' + error.message);
            } finally {
                // Восстанавливаем кнопку
                document.getElementById('analyzeBtn').innerHTML = `
                    <i class="fas fa-search"></i>
                    Анализировать
                `;
                document.getElementById('analyzeBtn').disabled = false;
            }
        }

        // Отображение результатов анализа с GPU метриками
        function displayAnalysisResults(data) {
            const resultsCard = document.getElementById('resultsCard');
            const resultsDiv = document.getElementById('results');

            resultsCard.style.display = 'block';

            // Проверяем наличие GPU ускорения
            const isGpuAccelerated = data.analysis?.gpuAccelerated || false;
            const gpuMetrics = data.analysis?.processingMetrics || {};
            const totalGpuTime = (gpuMetrics.gpuAnalysisTime || 0) +
                               (gpuMetrics.gpuCycleTime || 0) +
                               (gpuMetrics.gpuCompatibilityTime || 0);

            let html = `
                <div style="margin-bottom: 20px;">
                    <h3>🔍 Результаты анализа ${isGpuAccelerated ? '🎮' : '🖥️'}</h3>

                    ${isGpuAccelerated ? `
                        <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                            <h4 style="margin: 0 0 10px 0;">🎮 GPU Ускорение Активно</h4>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 15px; font-size: 0.9em;">
                                <div>
                                    <div style="opacity: 0.8;">Анализ зависимостей</div>
                                    <div style="font-weight: bold;">${gpuMetrics.gpuAnalysisTime || 0}мс</div>
                                </div>
                                <div>
                                    <div style="opacity: 0.8;">Поиск циклов</div>
                                    <div style="font-weight: bold;">${gpuMetrics.gpuCycleTime || 0}мс</div>
                                </div>
                                <div>
                                    <div style="opacity: 0.8;">Совместимость</div>
                                    <div style="font-weight: bold;">${gpuMetrics.gpuCompatibilityTime || 0}мс</div>
                                </div>
                                <div>
                                    <div style="opacity: 0.8;">Общее время</div>
                                    <div style="font-weight: bold;">${totalGpuTime}мс</div>
                                </div>
                            </div>
                        </div>
                    ` : ''}
                </div>

                <div class="metric">
                    <span class="metric-label">Название пакета</span>
                    <span class="metric-value">${data.name || 'Не указано'}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Версия</span>
                    <span class="metric-value">${data.version || 'Не указано'}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Всего зависимостей</span>
                    <span class="metric-value">${data.dependencies?.total || 0}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Известных пакетов</span>
                    <span class="metric-value">${data.dependencies?.known || 0}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Неизвестных пакетов</span>
                    <span class="metric-value">${data.dependencies?.unknown || 0}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Уровень риска</span>
                    <span class="metric-value" style="color: ${data.analysis?.riskLevel === 'high' ? '#e74c3c' : data.analysis?.riskLevel === 'medium' ? '#f39c12' : '#27ae60'};">
                        ${data.analysis?.riskLevel || 'low'}
                    </span>
                </div>
            `;

            // GPU результаты анализа зависимостей
            if (data.gpuAnalysis && data.gpuAnalysis.dependencies && data.gpuAnalysis.dependencies.length > 0) {
                html += `
                    <div style="margin-top: 20px;">
                        <h4 style="color: #667eea; margin-bottom: 10px;">
                            <i class="fas fa-microchip"></i>
                            🎮 GPU Анализ зависимостей (${data.gpuAnalysis.dependencies.length})
                        </h4>
                        <div style="max-height: 300px; overflow-y: auto;">
                `;

                data.gpuAnalysis.dependencies.forEach(dep => {
                    const riskColor = dep.riskScore > 0.7 ? '#e74c3c' : dep.riskScore > 0.4 ? '#f39c12' : '#27ae60';
                    html += `
                        <div style="background: rgba(102, 126, 234, 0.1); padding: 12px; margin: 8px 0; border-radius: 6px; border-left: 4px solid ${riskColor};">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <strong>${dep.name}</strong>
                                <span style="background: ${riskColor}; color: white; padding: 2px 8px; border-radius: 12px; font-size: 0.8em;">
                                    Риск: ${(dep.riskScore * 100).toFixed(0)}%
                                </span>
                            </div>
                            <div style="font-size: 0.9em; color: #666; margin-bottom: 5px;">
                                ${dep.requestedVersion} → ${dep.availableVersion}
                            </div>
                            <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; font-size: 0.8em; margin-bottom: 8px;">
                                <div>Безопасность: ${(dep.securityScore * 100).toFixed(0)}%</div>
                                <div>Популярность: ${(dep.popularityScore * 100).toFixed(0)}%</div>
                                <div>Совместимость: ${dep.riskScore < 0.3 ? 'Хорошая' : dep.riskScore < 0.7 ? 'Средняя' : 'Плохая'}</div>
                            </div>
                            <div style="font-size: 0.8em; color: #555; font-style: italic;">
                                💡 ${dep.recommendation}
                            </div>
                        </div>
                    `;
                });

                html += '</div></div>';
            }

            if (data.potentialIssues && data.potentialIssues.length > 0) {
                html += `
                    <div style="margin-top: 20px;">
                        <h4 style="color: #e74c3c; margin-bottom: 10px;">
                            <i class="fas fa-exclamation-triangle"></i>
                            Потенциальные проблемы (${data.potentialIssues.length})
                        </h4>
                `;

                data.potentialIssues.forEach(issue => {
                    html += `
                        <div style="background: rgba(231, 76, 60, 0.1); padding: 10px; margin: 5px 0; border-radius: 5px; border-left: 4px solid #e74c3c;">
                            <strong>${issue.package}</strong>: ${issue.message || issue.issue}
                            ${issue.recommendation ? `<div style="font-size: 0.9em; color: #666; margin-top: 5px;">💡 ${issue.recommendation}</div>` : ''}
                        </div>
                    `;
                });

                html += '</div>';
            }

            // GPU циклические зависимости
            if (data.gpuAnalysis && data.gpuAnalysis.cyclicDependencies && data.gpuAnalysis.cyclicDependencies.length > 0) {
                html += `
                    <div style="margin-top: 20px;">
                        <h4 style="color: #f39c12; margin-bottom: 10px;">
                            <i class="fas fa-sync-alt"></i>
                            🎮 GPU Циклические зависимости (${data.gpuAnalysis.cyclicDependencies.length})
                        </h4>
                `;

                data.gpuAnalysis.cyclicDependencies.forEach(cycle => {
                    html += `
                        <div style="background: rgba(243, 156, 18, 0.1); padding: 10px; margin: 5px 0; border-radius: 5px; border-left: 4px solid #f39c12;">
                            <strong>${cycle.node}</strong> - обнаружен цикл
                            <span style="font-size: 0.8em; color: #666;">(уверенность: ${(cycle.confidence * 100).toFixed(1)}%)</span>
                        </div>
                    `;
                });

                html += '</div>';
            } else if (data.cyclicDependencies && data.cyclicDependencies.length > 0) {
                html += `
                    <div style="margin-top: 20px;">
                        <h4 style="color: #f39c12; margin-bottom: 10px;">
                            <i class="fas fa-sync-alt"></i>
                            Циклические зависимости (${data.cyclicDependencies.length})
                        </h4>
                        <div style="background: rgba(243, 156, 18, 0.1); padding: 10px; border-radius: 5px; border-left: 4px solid #f39c12;">
                            ${data.cyclicDependencies.join(', ')}
                        </div>
                    </div>
                `;
            }

            // Рекомендации
            if (data.recommendations && data.recommendations.length > 0) {
                html += `
                    <div style="margin-top: 20px;">
                        <h4 style="color: #3498db; margin-bottom: 10px;">
                            <i class="fas fa-lightbulb"></i>
                            Рекомендации (${data.recommendations.length})
                        </h4>
                `;

                data.recommendations.forEach(rec => {
                    html += `
                        <div style="background: rgba(52, 152, 219, 0.1); padding: 10px; margin: 5px 0; border-radius: 5px; border-left: 4px solid #3498db;">
                            <strong>${rec.package}</strong>: ${rec.message}
                            <div style="font-size: 0.9em; color: #666; margin-top: 5px;">
                                Текущая: ${rec.current} → Рекомендуемая: ${rec.latest}
                            </div>
                        </div>
                    `;
                });

                html += '</div>';
            }

            resultsDiv.innerHTML = html;
        }

        // Загрузка статистики системы
        async function loadSystemStats() {
            try {
                const response = await fetch('/api/stats');
                if (response.ok) {
                    const stats = await response.json();
                    updateSystemStats(stats);
                }
            } catch (error) {
                addLog('warning', 'Не удалось загрузить статистику системы');
            }
        }

        // Обновление статистики системы
        function updateSystemStats(stats) {
            if (stats.totalPackages !== undefined) {
                document.getElementById('totalPackages').textContent = stats.totalPackages.toLocaleString();
            }
            if (stats.todayAnalyses !== undefined) {
                document.getElementById('todayAnalyses').textContent = stats.todayAnalyses.toLocaleString();
            }
            if (stats.conflictsFound !== undefined) {
                document.getElementById('conflictsFound').textContent = stats.conflictsFound.toLocaleString();
            }
            if (stats.conflictsResolved !== undefined) {
                document.getElementById('conflictsResolved').textContent = stats.conflictsResolved.toLocaleString();
            }
            if (stats.uptime !== undefined) {
                document.getElementById('uptime').textContent = formatUptime(stats.uptime);
            }
            if (stats.cpuUsage !== undefined) {
                document.getElementById('cpuUsage').textContent = stats.cpuUsage.toFixed(1) + '%';
                document.getElementById('cpuProgress').style.width = stats.cpuUsage + '%';
            }
            if (stats.memoryUsage !== undefined) {
                document.getElementById('memoryUsage').textContent = stats.memoryUsage.toFixed(1) + '%';
                document.getElementById('memoryProgress').style.width = stats.memoryUsage + '%';
            }
            if (stats.analysisSpeed !== undefined) {
                document.getElementById('analysisSpeed').textContent = stats.analysisSpeed.toFixed(2) + ' пак/сек';
            }

            // Обновляем информацию о GPU
            if (stats.gpuStatus !== undefined) {
                updateGPUStatus(stats.gpuStatus);
            }
        }

        // Обновление статуса GPU
        function updateGPUStatus(gpuStatus) {
            const statusElement = document.getElementById('gpuStatus');
            const detailsElement = document.getElementById('gpuDetails');
            const memoryElement = document.getElementById('gpuMemory');
            const cudaElement = document.getElementById('cudaVersion');

            if (gpuStatus.available) {
                statusElement.textContent = 'Доступно';
                statusElement.style.color = '#27ae60';

                if (gpuStatus.info) {
                    // Показываем детали GPU
                    detailsElement.style.display = 'flex';
                    memoryElement.style.display = 'flex';
                    cudaElement.style.display = 'flex';

                    document.getElementById('gpuModel').textContent = gpuStatus.info.name || 'Неизвестно';
                    document.getElementById('gpuVram').textContent = gpuStatus.info.memory || 'Неизвестно';
                    document.getElementById('gpuCuda').textContent = gpuStatus.info.cudaVersion || 'Неизвестно';
                }
            } else {
                statusElement.textContent = 'Недоступно';
                statusElement.style.color = '#e74c3c';

                // Скрываем детали
                detailsElement.style.display = 'none';
                memoryElement.style.display = 'none';
                cudaElement.style.display = 'none';
            }
        }

        // Форматирование времени работы
        function formatUptime(seconds) {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);

            if (days > 0) {
                return `${days}д ${hours}ч ${minutes}м`;
            } else if (hours > 0) {
                return `${hours}ч ${minutes}м`;
            } else {
                return `${minutes}м`;
            }
        }

        // Добавление лога
        function addLog(level, message) {
            const logsContainer = document.getElementById('logsContainer');
            const timestamp = new Date().toLocaleTimeString();

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${level}`;
            logEntry.innerHTML = `<strong>[${timestamp}] [${level.toUpperCase()}]</strong> ${message}`;

            logsContainer.appendChild(logEntry);

            if (autoScroll) {
                logsContainer.scrollTop = logsContainer.scrollHeight;
            }
        }

        // Очистка логов
        function clearLogs() {
            document.getElementById('logsContainer').innerHTML = '';
            addLog('info', 'Логи очищены');
        }

        // Переключение авто-прокрутки
        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            const btn = document.getElementById('autoScrollBtn');
            btn.innerHTML = autoScroll ?
                '<i class="fas fa-arrow-down"></i> Авто-прокрутка' :
                '<i class="fas fa-pause"></i> Авто-прокрутка';
        }

        // Обновление очереди задач
        function updateTaskQueue(task) {
            const taskQueue = document.getElementById('taskQueue');

            let taskItem = document.getElementById(`task-${task.id}`);
            if (!taskItem) {
                taskItem = document.createElement('div');
                taskItem.className = 'task-item';
                taskItem.id = `task-${task.id}`;
                taskQueue.appendChild(taskItem);
            }

            taskItem.innerHTML = `
                <span>${task.name}</span>
                <span class="task-status task-${task.status}">${getTaskStatusText(task.status)}</span>
            `;
        }

        // Получение текста статуса задачи
        function getTaskStatusText(status) {
            const statusMap = {
                'pending': 'Ожидание',
                'running': 'Выполняется',
                'completed': 'Завершено',
                'failed': 'Ошибка'
            };
            return statusMap[status] || status;
        }

        // Запуск полного сканирования
        async function startFullScan() {
            try {
                addLog('info', 'Запуск полного сканирования npm пакетов...');

                const response = await fetch('/api/full-scan', {
                    method: 'POST'
                });

                if (response.ok) {
                    addLog('success', 'Полное сканирование запущено');
                } else {
                    throw new Error('Ошибка при запуске сканирования');
                }
            } catch (error) {
                addLog('error', 'Ошибка при запуске полного сканирования: ' + error.message);
            }
        }

        // Периодическое обновление статистики
        function startStatsUpdater() {
            setInterval(async () => {
                await loadSystemStats();
            }, 5000); // Обновление каждые 5 секунд
        }

        // Переменные для пагинации пакетов
        let currentPage = 1;
        let totalPages = 1;
        let currentSearch = '';

        // Загрузка пакетов из базы данных
        async function loadPackages(page = 1, search = '') {
            try {
                const response = await fetch(`/api/packages?page=${page}&limit=20&search=${encodeURIComponent(search)}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                displayPackages(data.packages);
                updatePagination(data.pagination);

                // Обновляем статистику БД
                document.getElementById('dbTotalPackages').textContent = data.pagination.total.toLocaleString();

            } catch (error) {
                addLog('error', 'Ошибка загрузки пакетов: ' + error.message);
                document.getElementById('packagesTableBody').innerHTML = `
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px; color: #e74c3c;">
                            Ошибка загрузки пакетов: ${error.message}
                        </td>
                    </tr>
                `;
            }
        }

        // Отображение пакетов в таблице
        function displayPackages(packages) {
            const tbody = document.getElementById('packagesTableBody');

            if (packages.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px; color: #7f8c8d;">
                            Пакеты не найдены
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = packages.map(pkg => `
                <tr style="border-bottom: 1px solid #dee2e6;">
                    <td style="padding: 10px;">
                        <strong>${pkg.name}</strong>
                    </td>
                    <td style="padding: 10px;">
                        <span style="background: #e3f2fd; color: #1976d2; padding: 2px 6px; border-radius: 4px; font-size: 0.8em;">
                            ${pkg.latest_version || 'N/A'}
                        </span>
                    </td>
                    <td style="padding: 10px; max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                        ${pkg.description || 'Описание отсутствует'}
                    </td>
                    <td style="padding: 10px;">
                        <span style="background: #f3e5f5; color: #7b1fa2; padding: 2px 6px; border-radius: 4px; font-size: 0.8em;">
                            ${pkg.license || 'N/A'}
                        </span>
                    </td>
                    <td style="padding: 10px; text-align: center;">
                        <button class="btn" onclick="viewPackageDetails('${pkg.name}')" style="padding: 5px 10px; font-size: 0.8em; margin-right: 5px;">
                            <i class="fas fa-eye"></i>
                            Детали
                        </button>
                        <button class="btn btn-info" onclick="showDetailedPackageInfo('${pkg.name}')" style="padding: 5px 10px; font-size: 0.8em;">
                            <i class="fas fa-database"></i>
                            Полная БД
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // Обновление пагинации
        function updatePagination(pagination) {
            currentPage = pagination.page;
            totalPages = pagination.pages;

            document.getElementById('paginationInfo').textContent =
                `Страница ${pagination.page} из ${pagination.pages} (всего: ${pagination.total.toLocaleString()})`;

            document.getElementById('prevPageBtn').disabled = pagination.page <= 1;
            document.getElementById('nextPageBtn').disabled = pagination.page >= pagination.pages;
        }

        // Поиск пакетов
        function searchPackages() {
            const searchInput = document.getElementById('packageSearch');
            currentSearch = searchInput.value.trim();
            currentPage = 1;
            loadPackages(currentPage, currentSearch);
            addLog('info', `Поиск пакетов: "${currentSearch}"`);
        }

        // Обновление списка пакетов
        function refreshPackages() {
            addLog('info', 'Обновление списка пакетов...');
            loadPackages(currentPage, currentSearch);
        }

        // Предыдущая страница
        function previousPage() {
            if (currentPage > 1) {
                currentPage--;
                loadPackages(currentPage, currentSearch);
            }
        }

        // Следующая страница
        function nextPage() {
            if (currentPage < totalPages) {
                currentPage++;
                loadPackages(currentPage, currentSearch);
            }
        }

        // Просмотр деталей пакета
        async function viewPackageDetails(packageName) {
            try {
                document.getElementById('packageModal').style.display = 'block';
                document.getElementById('modalPackageName').textContent = `Детали пакета: ${packageName}`;
                document.getElementById('modalPackageContent').innerHTML = 'Загрузка...';

                const response = await fetch(`/api/packages/${encodeURIComponent(packageName)}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const packageData = await response.json();
                displayPackageDetails(packageData);

            } catch (error) {
                document.getElementById('modalPackageContent').innerHTML = `
                    <div style="color: #e74c3c; text-align: center; padding: 20px;">
                        Ошибка загрузки деталей пакета: ${error.message}
                    </div>
                `;
            }
        }

        // Отображение полной структурированной информации о пакете
        function displayPackageDetails(pkg) {
            console.log('Отображаем детали пакета:', pkg);

            // Обрабатываем версии с зависимостями
            const versionsHtml = pkg.versions && pkg.versions.length > 0 ?
                pkg.versions.slice(0, 10).map(v => {
                    const depCount = Object.keys(v.dependencies || {}).length;
                    const devDepCount = Object.keys(v.devDependencies || {}).length;
                    const peerDepCount = Object.keys(v.peerDependencies || {}).length;
                    const optionalDepCount = Object.keys(v.optionalDependencies || {}).length;

                    const dependenciesHtml = depCount > 0 ?
                        Object.entries(v.dependencies).slice(0, 5).map(([name, version]) =>
                            `<span style="background: #e3f2fd; color: #1976d2; padding: 2px 4px; border-radius: 3px; font-size: 0.7em; margin: 1px;">${name}@${version}</span>`
                        ).join(' ') + (depCount > 5 ? ` <span style="color: #666;">+${depCount - 5} еще</span>` : '') :
                        '<span style="color: #999;">Нет зависимостей</span>';

                    return `
                        <div style="margin: 5px 0; padding: 12px; background: #f8f9fa; border-radius: 6px; border-left: 4px solid #3498db;">
                            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                <strong style="font-size: 1.1em;">${v.version}</strong>
                                <span style="color: #6c757d; font-size: 0.9em;">
                                    ${new Date(v.published_at).toLocaleDateString()}
                                </span>
                            </div>
                            <div style="margin-bottom: 8px;">
                                <div style="font-size: 0.8em; color: #666; margin-bottom: 4px;">
                                    📦 ${depCount} зависимостей | 🔧 ${devDepCount} dev | 🔗 ${peerDepCount} peer | ⚙️ ${optionalDepCount} optional
                                </div>
                                <div style="line-height: 1.4;">
                                    ${dependenciesHtml}
                                </div>
                            </div>
                        </div>
                    `;
                }).join('') : '<p style="text-align: center; color: #999; padding: 20px;">Версии не найдены</p>';

            // Обрабатываем ключевые слова
            const keywordsHtml = pkg.keywords && pkg.keywords.length > 0 ?
                pkg.keywords.map(keyword =>
                    `<span style="background: #f3e5f5; color: #7b1fa2; padding: 3px 8px; border-radius: 12px; font-size: 0.8em; margin: 2px;">${keyword}</span>`
                ).join(' ') : '<span style="color: #999;">Нет ключевых слов</span>';

            // Обрабатываем maintainers
            const maintainersHtml = pkg.maintainers && pkg.maintainers.length > 0 ?
                pkg.maintainers.map(maintainer => {
                    const name = typeof maintainer === 'string' ? maintainer : maintainer.name;
                    const email = typeof maintainer === 'object' ? maintainer.email : '';
                    return `<span style="background: #e8f5e8; color: #2e7d32; padding: 3px 8px; border-radius: 12px; font-size: 0.8em; margin: 2px;" title="${email}">${name}</span>`;
                }).join(' ') : '<span style="color: #999;">Нет информации</span>';

            // Статистика загрузок
            const downloadsHtml = `
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                    <div class="metric" style="text-align: center; background: #e3f2fd; padding: 10px; border-radius: 6px;">
                        <div style="font-size: 0.8em; color: #666;">Неделя</div>
                        <div style="font-size: 1.2em; font-weight: bold; color: #1976d2;">${(pkg.weekly_downloads || 0).toLocaleString()}</div>
                    </div>
                    <div class="metric" style="text-align: center; background: #f3e5f5; padding: 10px; border-radius: 6px;">
                        <div style="font-size: 0.8em; color: #666;">Месяц</div>
                        <div style="font-size: 1.2em; font-weight: bold; color: #7b1fa2;">${(pkg.monthly_downloads || 0).toLocaleString()}</div>
                    </div>
                    <div class="metric" style="text-align: center; background: #e8f5e8; padding: 10px; border-radius: 6px;">
                        <div style="font-size: 0.8em; color: #666;">Год</div>
                        <div style="font-size: 1.2em; font-weight: bold; color: #2e7d32;">${(pkg.yearly_downloads || 0).toLocaleString()}</div>
                    </div>
                </div>
            `;

            // GitHub метрики
            const githubHtml = (pkg.github_stars || pkg.github_forks || pkg.github_issues) ? `
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                    <div class="metric" style="text-align: center; background: #fff3e0; padding: 10px; border-radius: 6px;">
                        <div style="font-size: 0.8em; color: #666;">⭐ Звезды</div>
                        <div style="font-size: 1.1em; font-weight: bold; color: #f57c00;">${(pkg.github_stars || 0).toLocaleString()}</div>
                    </div>
                    <div class="metric" style="text-align: center; background: #fce4ec; padding: 10px; border-radius: 6px;">
                        <div style="font-size: 0.8em; color: #666;">🍴 Форки</div>
                        <div style="font-size: 1.1em; font-weight: bold; color: #c2185b;">${(pkg.github_forks || 0).toLocaleString()}</div>
                    </div>
                    <div class="metric" style="text-align: center; background: #ffebee; padding: 10px; border-radius: 6px;">
                        <div style="font-size: 0.8em; color: #666;">🐛 Issues</div>
                        <div style="font-size: 1.1em; font-weight: bold; color: #d32f2f;">${(pkg.github_issues || 0).toLocaleString()}</div>
                    </div>
                    <div class="metric" style="text-align: center; background: #e1f5fe; padding: 10px; border-radius: 6px;">
                        <div style="font-size: 0.8em; color: #666;">👀 Watchers</div>
                        <div style="font-size: 1.1em; font-weight: bold; color: #0277bd;">${(pkg.github_watchers || 0).toLocaleString()}</div>
                    </div>
                </div>
            ` : '';

            // Оценки качества
            const qualityHtml = `
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                    <div class="metric" style="text-align: center; background: #f1f8e9; padding: 10px; border-radius: 6px;">
                        <div style="font-size: 0.8em; color: #666;">🏆 Качество</div>
                        <div style="font-size: 1.2em; font-weight: bold; color: #689f38;">${((pkg.quality_score || 0) * 100).toFixed(1)}%</div>
                    </div>
                    <div class="metric" style="text-align: center; background: #e8eaf6; padding: 10px; border-radius: 6px;">
                        <div style="font-size: 0.8em; color: #666;">📈 Популярность</div>
                        <div style="font-size: 1.2em; font-weight: bold; color: #5e35b1;">${((pkg.popularity_score || 0) * 100).toFixed(1)}%</div>
                    </div>
                    <div class="metric" style="text-align: center; background: #fff8e1; padding: 10px; border-radius: 6px;">
                        <div style="font-size: 0.8em; color: #666;">🔧 Поддержка</div>
                        <div style="font-size: 1.2em; font-weight: bold; color: #f9a825;">${((pkg.maintenance_score || 0) * 100).toFixed(1)}%</div>
                    </div>
                </div>
            `;

            // Технические характеристики
            const techHtml = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px; margin-bottom: 15px;">
                    <div>
                        <div class="metric">
                            <span class="metric-label">📝 TypeScript</span>
                            <span class="metric-value" style="color: ${pkg.has_typescript ? '#27ae60' : '#e74c3c'};">
                                ${pkg.has_typescript ? '✅ Да' : '❌ Нет'}
                            </span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">🧪 Тесты</span>
                            <span class="metric-value" style="color: ${pkg.has_tests ? '#27ae60' : '#e74c3c'};">
                                ${pkg.has_tests ? '✅ Да' : '❌ Нет'}
                            </span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">📊 Покрытие тестами</span>
                            <span class="metric-value">${((pkg.test_coverage || 0) * 100).toFixed(1)}%</span>
                        </div>
                    </div>
                    <div>
                        <div class="metric">
                            <span class="metric-label">📦 Версий</span>
                            <span class="metric-value">${pkg.version_count || 0}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">🔗 Зависимостей</span>
                            <span class="metric-value">${pkg.dependency_count || 0}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">📁 Файлов</span>
                            <span class="metric-value">${pkg.file_count || 0}</span>
                        </div>
                    </div>
                </div>
            `;

            // Обрабатываем уязвимости
            const vulnerabilitiesHtml = pkg.vulnerabilities && pkg.vulnerabilities.length > 0 ? `
                <div style="margin-bottom: 20px;">
                    <h4 style="color: #e74c3c;">🚨 Уязвимости (${pkg.vulnerabilities.length})</h4>
                    <div style="max-height: 200px; overflow-y: auto;">
                        ${pkg.vulnerabilities.map(vuln => `
                            <div style="margin: 5px 0; padding: 10px; background: #ffeaea; border-left: 4px solid #e74c3c; border-radius: 4px;">
                                <div style="font-weight: bold; color: #c0392b;">${vuln.title}</div>
                                <div style="font-size: 0.9em; color: #666; margin-top: 5px;">${vuln.description}</div>
                                <div style="font-size: 0.8em; margin-top: 5px;">
                                    <span style="background: #e74c3c; color: white; padding: 2px 6px; border-radius: 3px;">${vuln.severity}</span>
                                    ${vuln.fixed_in ? `<span style="margin-left: 10px; color: #27ae60;">Исправлено в ${vuln.fixed_in}</span>` : ''}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : '';

            document.getElementById('modalPackageContent').innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <h4>📋 Основная информация</h4>
                        <div class="metric">
                            <span class="metric-label">Название</span>
                            <span class="metric-value">${pkg.name}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Последняя версия</span>
                            <span class="metric-value">${pkg.latest_version || 'N/A'}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Лицензия</span>
                            <span class="metric-value">${pkg.license || 'N/A'}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Автор</span>
                            <span class="metric-value">${pkg.author || 'N/A'}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Создан</span>
                            <span class="metric-value">${pkg.first_release_date ? new Date(pkg.first_release_date).toLocaleDateString() : 'N/A'}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Обновлен</span>
                            <span class="metric-value">${pkg.last_release_date ? new Date(pkg.last_release_date).toLocaleDateString() : new Date(pkg.updated_at).toLocaleDateString()}</span>
                        </div>
                    </div>
                    <div>
                        <h4>🔗 Ссылки</h4>
                        <div class="metric">
                            <span class="metric-label">NPM</span>
                            <span class="metric-value">
                                <a href="${pkg.npm_url || `https://www.npmjs.com/package/${pkg.name}`}" target="_blank" style="color: #3498db;">Открыть</a>
                            </span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Репозиторий</span>
                            <span class="metric-value">
                                ${pkg.repository ?
                                    `<a href="${pkg.repository}" target="_blank" style="color: #3498db;">Открыть</a>` :
                                    'N/A'
                                }
                            </span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Домашняя страница</span>
                            <span class="metric-value">
                                ${pkg.homepage ?
                                    `<a href="${pkg.homepage}" target="_blank" style="color: #3498db;">Открыть</a>` :
                                    'N/A'
                                }
                            </span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Баги</span>
                            <span class="metric-value">
                                ${pkg.bugs_url ?
                                    `<a href="${pkg.bugs_url}" target="_blank" style="color: #3498db;">Сообщить</a>` :
                                    'N/A'
                                }
                            </span>
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <h4>📝 Описание</h4>
                    <p style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0; line-height: 1.5;">
                        ${pkg.description || 'Описание отсутствует'}
                    </p>
                </div>

                <div style="margin-bottom: 20px;">
                    <h4>🏷️ Ключевые слова</h4>
                    <div style="line-height: 2;">
                        ${keywordsHtml}
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <h4>👥 Maintainers</h4>
                    <div style="line-height: 2;">
                        ${maintainersHtml}
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <h4>📊 Статистика загрузок</h4>
                    ${downloadsHtml}
                </div>

                ${githubHtml ? `
                    <div style="margin-bottom: 20px;">
                        <h4>🐙 GitHub метрики</h4>
                        ${githubHtml}
                    </div>
                ` : ''}

                <div style="margin-bottom: 20px;">
                    <h4>🎯 Оценки качества</h4>
                    ${qualityHtml}
                </div>

                <div style="margin-bottom: 20px;">
                    <h4>⚙️ Технические характеристики</h4>
                    ${techHtml}
                </div>

                ${vulnerabilitiesHtml}

                <div>
                    <h4>📦 Версии (последние 10)</h4>
                    <div style="max-height: 400px; overflow-y: auto;">
                        ${versionsHtml}
                    </div>
                </div>

                <div style="margin-top: 20px; text-align: right;">
                    <button class="btn btn-warning" onclick="editPackage('${pkg.name}')">
                        <i class="fas fa-edit"></i>
                        Редактировать
                    </button>
                </div>
            `;
        }

        // Закрытие модального окна
        function closePackageModal() {
            document.getElementById('packageModal').style.display = 'none';
        }

        // Редактирование пакета (заглушка)
        function editPackage(packageName) {
            addLog('info', `Редактирование пакета ${packageName} (функция в разработке)`);
            closePackageModal();
        }

        // Показ полной детальной информации из базы данных
        async function showDetailedPackageInfo(packageName) {
            try {
                document.getElementById('packageModal').style.display = 'block';
                document.getElementById('modalPackageName').textContent = `Полная информация из БД: ${packageName}`;
                document.getElementById('modalPackageContent').innerHTML = 'Загрузка полной информации из базы данных...';

                const response = await fetch(`/api/packages/${encodeURIComponent(packageName)}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const packageData = await response.json();
                displayDetailedPackageInfo(packageData);

            } catch (error) {
                document.getElementById('modalPackageContent').innerHTML = `
                    <div style="color: #e74c3c; text-align: center; padding: 20px;">
                        Ошибка загрузки полной информации: ${error.message}
                    </div>
                `;
            }
        }

        // Отображение полной детальной информации из базы данных
        function displayDetailedPackageInfo(pkg) {
            console.log('Полная информация из БД:', pkg);

            // Функция для безопасного отображения значений
            const safeValue = (value, defaultValue = 'N/A') => {
                if (value === null || value === undefined || value === '') return defaultValue;
                if (typeof value === 'boolean') return value ? '✅ Да' : '❌ Нет';
                if (typeof value === 'number') return value.toLocaleString();
                if (typeof value === 'object') return JSON.stringify(value, null, 2);
                return value;
            };

            // Функция для отображения статуса
            const getStatusBadge = (status, message) => {
                const colors = {
                    'secure': '#27ae60',
                    'low_risk': '#f39c12',
                    'moderate_risk': '#e67e22',
                    'high_risk': '#e74c3c',
                    'critical': '#c0392b',
                    'unknown': '#95a5a6',
                    'active': '#27ae60',
                    'maintained': '#2ecc71',
                    'slow': '#f39c12',
                    'stale': '#e67e22',
                    'abandoned': '#e74c3c'
                };

                const color = colors[status] || '#95a5a6';
                return `<span style="background: ${color}; color: white; padding: 3px 8px; border-radius: 12px; font-size: 0.8em;">${message || status}</span>`;
            };

            // Результаты npm audit
            const auditHtml = pkg.audit_results && pkg.audit_results.length > 0 ? `
                <div style="margin-bottom: 20px; padding: 15px; background: #fff5f5; border-left: 4px solid #e74c3c; border-radius: 8px;">
                    <h4 style="color: #e74c3c; margin-bottom: 15px;">🛡️ Результаты npm audit (${pkg.audit_results.length})</h4>
                    <div style="max-height: 300px; overflow-y: auto;">
                        ${pkg.audit_results.map(audit => `
                            <div style="margin: 10px 0; padding: 12px; background: white; border-radius: 6px; border: 1px solid #fecaca;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px;">
                                    <strong style="color: #dc2626;">${audit.title || 'Уязвимость'}</strong>
                                    ${getStatusBadge(audit.severity, audit.severity?.toUpperCase())}
                                </div>
                                <div style="font-size: 0.9em; color: #666; margin-bottom: 8px;">
                                    ${audit.overview || 'Описание недоступно'}
                                </div>
                                <div style="font-size: 0.8em; color: #555;">
                                    <div><strong>Уязвимые версии:</strong> ${audit.vulnerable_versions || 'N/A'}</div>
                                    <div><strong>Исправлено в:</strong> ${audit.patched_versions || 'Не исправлено'}</div>
                                    <div><strong>Рекомендация:</strong> ${audit.recommendation || 'Обновите пакет'}</div>
                                    <div><strong>CVSS:</strong> ${audit.cvss_score || 'N/A'}</div>
                                    <div><strong>Дата аудита:</strong> ${audit.audit_date ? new Date(audit.audit_date).toLocaleString() : 'N/A'}</div>
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : `
                <div style="margin-bottom: 20px; padding: 15px; background: #f0fff4; border-left: 4px solid #27ae60; border-radius: 8px;">
                    <h4 style="color: #27ae60;">🛡️ Результаты npm audit</h4>
                    <p style="color: #666;">Уязвимости не обнаружены или аудит не проводился</p>
                </div>
            `;

            // Альтернативы пакета
            const alternativesHtml = pkg.alternatives && pkg.alternatives.length > 0 ? `
                <div style="margin-bottom: 20px; padding: 15px; background: #f8f9ff; border-left: 4px solid #6366f1; border-radius: 8px;">
                    <h4 style="color: #6366f1; margin-bottom: 15px;">🔄 Альтернативы пакета (${pkg.alternatives.length})</h4>
                    <div style="max-height: 200px; overflow-y: auto;">
                        ${pkg.alternatives.map(alt => `
                            <div style="margin: 8px 0; padding: 10px; background: white; border-radius: 6px; border: 1px solid #e0e7ff;">
                                <div style="display: flex; justify-content: space-between; align-items: center;">
                                    <strong style="color: #4338ca;">${alt.alternative_package}</strong>
                                    <span style="font-size: 0.8em; color: #666;">Релевантность: ${(alt.relevance_score || 0).toFixed(1)}%</span>
                                </div>
                                <div style="font-size: 0.8em; color: #666; margin-top: 5px;">
                                    ${alt.recommendation || 'Альтернативный пакет'}
                                </div>
                                <div style="font-size: 0.7em; color: #888; margin-top: 3px;">
                                    Причины: ${alt.match_reasons ? alt.match_reasons.join(', ') : 'N/A'}
                                </div>
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : '';

            // Статусы и ранги
            const statusHtml = `
                <div style="margin-bottom: 20px;">
                    <h4>📊 Статусы и ранги</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 15px;">
                        <div>
                            <div class="metric">
                                <span class="metric-label">🛡️ Безопасность</span>
                                <span class="metric-value">
                                    ${pkg.security_status ? getStatusBadge(pkg.security_status.status, pkg.security_status.message) : 'N/A'}
                                </span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">🔧 Поддержка</span>
                                <span class="metric-value">
                                    ${pkg.maintenance_status ? getStatusBadge(pkg.maintenance_status.status, pkg.maintenance_status.message) : 'N/A'}
                                </span>
                            </div>
                        </div>
                        <div>
                            <div class="metric">
                                <span class="metric-label">📈 Ранг популярности</span>
                                <span class="metric-value">${getStatusBadge(pkg.popularity_rank || 'unknown', (pkg.popularity_rank || 'unknown').replace('_', ' '))}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">🏆 Ранг качества</span>
                                <span class="metric-value">${getStatusBadge(pkg.quality_rank || 'unknown', (pkg.quality_rank || 'unknown').replace('_', ' '))}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Полная техническая информация
            const technicalHtml = `
                <div style="margin-bottom: 20px;">
                    <h4>⚙️ Полная техническая информация</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 15px;">
                        <div>
                            <div class="metric">
                                <span class="metric-label">📦 Размер (unpacked)</span>
                                <span class="metric-value">${pkg.unpacked_size ? (pkg.unpacked_size / 1024).toFixed(1) + ' KB' : 'N/A'}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">📁 Количество файлов</span>
                                <span class="metric-value">${safeValue(pkg.file_count)}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">🔗 Всего зависимостей</span>
                                <span class="metric-value">${safeValue((pkg.dependency_count || 0) + (pkg.dev_dependency_count || 0) + (pkg.peer_dependency_count || 0) + (pkg.optional_dependency_count || 0))}</span>
                            </div>
                        </div>
                        <div>
                            <div class="metric">
                                <span class="metric-label">📊 Покрытие тестами</span>
                                <span class="metric-value">${pkg.test_coverage ? (pkg.test_coverage * 100).toFixed(1) + '%' : 'N/A'}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">🔄 Частота релизов</span>
                                <span class="metric-value">${pkg.release_frequency ? pkg.release_frequency.toFixed(2) + '/месяц' : 'N/A'}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">📅 Последний коммит</span>
                                <span class="metric-value">${pkg.last_commit_date ? new Date(pkg.last_commit_date).toLocaleDateString() : 'N/A'}</span>
                            </div>
                        </div>
                        <div>
                            <div class="metric">
                                <span class="metric-label">💰 Финансирование</span>
                                <span class="metric-value">${pkg.funding && Object.keys(pkg.funding).length > 0 ? '✅ Есть' : '❌ Нет'}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">🏷️ Ключевых слов</span>
                                <span class="metric-value">${pkg.keywords ? pkg.keywords.length : 0}</span>
                            </div>
                            <div class="metric">
                                <span class="metric-label">👥 Maintainers</span>
                                <span class="metric-value">${pkg.maintainers ? pkg.maintainers.length : 0}</span>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Engines и совместимость
            const enginesHtml = pkg.engines && Object.keys(pkg.engines).length > 0 ? `
                <div style="margin-bottom: 20px;">
                    <h4>🔧 Требования к движкам</h4>
                    <div style="background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        <pre style="margin: 0; font-size: 0.9em; color: #333;">${JSON.stringify(pkg.engines, null, 2)}</pre>
                    </div>
                </div>
            ` : '';

            // Все версии с детальной информацией
            const allVersionsHtml = pkg.versions && pkg.versions.length > 0 ? `
                <div style="margin-bottom: 20px;">
                    <h4>📦 Все версии (${pkg.versions.length})</h4>
                    <div style="max-height: 400px; overflow-y: auto; background: #f8f9fa; padding: 15px; border-radius: 8px;">
                        ${pkg.versions.map(v => `
                            <div style="margin: 8px 0; padding: 10px; background: white; border-radius: 6px; border-left: 3px solid #3498db;">
                                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 5px;">
                                    <strong>${v.version}</strong>
                                    <span style="font-size: 0.8em; color: #666;">
                                        ${v.published_at ? new Date(v.published_at).toLocaleDateString() : 'N/A'}
                                    </span>
                                </div>
                                <div style="font-size: 0.8em; color: #666;">
                                    📦 ${Object.keys(v.dependencies || {}).length} deps |
                                    🔧 ${Object.keys(v.devDependencies || {}).length} dev |
                                    🔗 ${Object.keys(v.peerDependencies || {}).length} peer |
                                    ⚙️ ${Object.keys(v.optionalDependencies || {}).length} optional
                                    ${v.deprecated ? ' | ⚠️ DEPRECATED' : ''}
                                </div>
                                ${Object.keys(v.dependencies || {}).length > 0 ? `
                                    <details style="margin-top: 5px;">
                                        <summary style="cursor: pointer; font-size: 0.8em; color: #666;">Показать зависимости</summary>
                                        <div style="margin-top: 5px; padding: 5px; background: #f1f3f4; border-radius: 4px; font-size: 0.7em;">
                                            ${Object.entries(v.dependencies).map(([name, ver]) => `${name}@${ver}`).join(', ')}
                                        </div>
                                    </details>
                                ` : ''}
                            </div>
                        `).join('')}
                    </div>
                </div>
            ` : '';

            // Сырые данные из БД
            const rawDataHtml = `
                <div style="margin-bottom: 20px;">
                    <h4>🗄️ Сырые данные из базы данных</h4>
                    <details>
                        <summary style="cursor: pointer; padding: 10px; background: #f8f9fa; border-radius: 6px;">
                            Показать все поля БД (JSON)
                        </summary>
                        <div style="margin-top: 10px; padding: 15px; background: #2d3748; color: #e2e8f0; border-radius: 8px; overflow-x: auto;">
                            <pre style="margin: 0; font-size: 0.8em; white-space: pre-wrap;">${JSON.stringify(pkg, null, 2)}</pre>
                        </div>
                    </details>
                </div>
            `;

            document.getElementById('modalPackageContent').innerHTML = `
                <div style="max-height: 80vh; overflow-y: auto;">
                    ${statusHtml}
                    ${auditHtml}
                    ${alternativesHtml}
                    ${technicalHtml}
                    ${enginesHtml}
                    ${allVersionsHtml}
                    ${rawDataHtml}
                </div>
            `;
        }

        // ========== ФУНКЦИИ СБОРА ДАННЫХ ==========

        // Запуск сбора пакетов
        async function startPackageCollection() {
            try {
                addLog('info', 'Запуск автоматического сбора популярных пакетов...');

                const response = await fetch('/api/collect/auto', {
                    method: 'POST'
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('success', `Запущен сбор ${result.count} пакетов`);
                    showTaskProgress('Сбор популярных пакетов', 0);
                } else {
                    throw new Error('Ошибка запуска сбора');
                }
            } catch (error) {
                addLog('error', 'Ошибка при запуске сбора: ' + error.message);
            }
        }

        // Обновление существующих пакетов
        async function updateExistingPackages() {
            try {
                addLog('info', 'Запуск обновления существующих пакетов...');

                const response = await fetch('/api/collect/update', {
                    method: 'POST'
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('success', `Запущено обновление ${result.count} пакетов`);
                    showTaskProgress('Обновление пакетов', 0);
                } else {
                    throw new Error('Ошибка запуска обновления');
                }
            } catch (error) {
                addLog('error', 'Ошибка при обновлении: ' + error.message);
            }
        }

        // Сбор популярных пакетов
        async function collectPopularPackages() {
            try {
                addLog('info', 'Запуск сбора топ-1000 популярных пакетов...');

                const response = await fetch('/api/collect/popular', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ limit: 1000 })
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('success', `Запущен сбор ${result.count} популярных пакетов`);
                    showTaskProgress('Сбор популярных пакетов', 0);
                } else {
                    throw new Error('Ошибка запуска сбора популярных пакетов');
                }
            } catch (error) {
                addLog('error', 'Ошибка при сборе популярных пакетов: ' + error.message);
            }
        }

        // Сбор по категориям
        async function collectByCategory() {
            const categories = [
                'framework', 'library', 'utility', 'build-tool', 'testing',
                'react', 'vue', 'angular', 'node', 'webpack', 'babel'
            ];

            try {
                addLog('info', 'Запуск сбора пакетов по категориям...');

                const response = await fetch('/api/collect/categories', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ categories: categories })
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('success', `Запущен сбор пакетов по ${categories.length} категориям`);
                    showTaskProgress('Сбор по категориям', 0);
                } else {
                    throw new Error('Ошибка запуска сбора по категориям');
                }
            } catch (error) {
                addLog('error', 'Ошибка при сборе по категориям: ' + error.message);
            }
        }

        // Добавление конкретного пакета
        async function addSpecificPackage() {
            const packageName = document.getElementById('packageNameInput').value.trim();

            if (!packageName) {
                addLog('warning', 'Введите название пакета');
                return;
            }

            try {
                addLog('info', `Добавление пакета: ${packageName}`);

                const response = await fetch('/api/collect/package', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ packageName: packageName })
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('success', `Пакет ${packageName} добавлен в очередь`);
                    document.getElementById('packageNameInput').value = '';

                    // Обновляем список пакетов
                    refreshPackages();
                } else {
                    const error = await response.json();
                    throw new Error(error.error || 'Ошибка добавления пакета');
                }
            } catch (error) {
                addLog('error', `Ошибка добавления пакета ${packageName}: ${error.message}`);
            }
        }

        // Добавление списка пакетов
        async function addPackageList() {
            const packageList = document.getElementById('packageListInput').value.trim();

            if (!packageList) {
                addLog('warning', 'Введите список пакетов');
                return;
            }

            const packages = packageList.split('\n')
                .map(pkg => pkg.trim())
                .filter(pkg => pkg.length > 0);

            if (packages.length === 0) {
                addLog('warning', 'Список пакетов пуст');
                return;
            }

            try {
                addLog('info', `Добавление ${packages.length} пакетов...`);

                const response = await fetch('/api/collect/packages', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ packages: packages })
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('success', `${packages.length} пакетов добавлено в очередь`);
                    document.getElementById('packageListInput').value = '';
                    showTaskProgress('Добавление пакетов', 0);

                    // Обновляем список пакетов
                    refreshPackages();
                } else {
                    const error = await response.json();
                    throw new Error(error.error || 'Ошибка добавления пакетов');
                }
            } catch (error) {
                addLog('error', `Ошибка добавления пакетов: ${error.message}`);
            }
        }

        // Остановка всех задач
        async function stopAllTasks() {
            try {
                addLog('warning', 'Остановка всех задач...');

                const response = await fetch('/api/tasks/stop', {
                    method: 'POST'
                });

                if (response.ok) {
                    addLog('success', 'Все задачи остановлены');
                    hideTaskProgress();
                } else {
                    throw new Error('Ошибка остановки задач');
                }
            } catch (error) {
                addLog('error', 'Ошибка при остановке задач: ' + error.message);
            }
        }

        // Показать прогресс задачи
        function showTaskProgress(taskName, progress) {
            const progressDiv = document.getElementById('currentTaskProgress');
            const nameSpan = document.getElementById('currentTaskName');
            const percentSpan = document.getElementById('currentTaskPercent');
            const progressBar = document.getElementById('currentTaskProgressBar');
            const detailsSpan = document.getElementById('currentTaskDetails');

            progressDiv.style.display = 'block';
            nameSpan.textContent = taskName;
            percentSpan.textContent = Math.round(progress) + '%';
            progressBar.style.width = progress + '%';
            detailsSpan.textContent = 'Выполняется...';
        }

        // Обновить прогресс задачи
        function updateTaskProgress(progress, details) {
            const percentSpan = document.getElementById('currentTaskPercent');
            const progressBar = document.getElementById('currentTaskProgressBar');
            const detailsSpan = document.getElementById('currentTaskDetails');

            if (percentSpan) {
                percentSpan.textContent = Math.round(progress) + '%';
                progressBar.style.width = progress + '%';
                if (details) {
                    detailsSpan.textContent = details;
                }
            }
        }

        // Скрыть прогресс задачи
        function hideTaskProgress() {
            const progressDiv = document.getElementById('currentTaskProgress');
            progressDiv.style.display = 'none';
        }

        // Обработка сообщений WebSocket для прогресса
        function handleWebSocketMessage(data) {
            switch(data.type) {
                case 'stats':
                    updateSystemStats(data.stats);
                    break;
                case 'log':
                    addLog(data.level, data.message);
                    break;
                case 'task':
                    updateTaskQueue(data.task);
                    break;
                case 'progress':
                    updateTaskProgress(data.progress, data.details);
                    break;
                case 'task_complete':
                    hideTaskProgress();
                    addLog('success', `Задача завершена: ${data.taskName}`);
                    refreshPackages(); // Обновляем список пакетов
                    break;
                case 'analysis':
                    displayAnalysisResults(data.results);
                    break;
                case 'massive_collection_progress':
                    updateMassiveCollectionProgress(data);
                    break;
                case 'massive_collection_complete':
                    onMassiveCollectionComplete(data);
                    break;
            }
        }

        // Запуск массового сбора всех npm пакетов
        async function startMassiveCollection() {
            try {
                addLog('info', '🌍 Запуск массового сбора ВСЕХ npm пакетов...');

                // Показываем прогресс
                document.getElementById('massiveProgress').style.display = 'block';
                document.getElementById('massiveCollectionBtn').disabled = true;
                document.getElementById('massiveCollectionBtn').innerHTML = `
                    <i class="fas fa-spinner fa-spin"></i>
                    Сбор запущен...
                `;

                const response = await fetch('/api/collect/massive', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('success', 'Массовый сбор npm пакетов запущен');
                } else {
                    const error = await response.json();
                    throw new Error(error.error || 'Ошибка запуска массового сбора');
                }
            } catch (error) {
                addLog('error', `Ошибка запуска массового сбора: ${error.message}`);
                resetMassiveCollectionUI();
            }
        }

        // Остановка массового сбора
        async function stopMassiveCollection() {
            try {
                addLog('warning', '⏹️ Остановка массового сбора...');

                const response = await fetch('/api/collect/massive/stop', {
                    method: 'POST'
                });

                if (response.ok) {
                    addLog('success', 'Массовый сбор остановлен');
                    resetMassiveCollectionUI();
                } else {
                    throw new Error('Ошибка остановки массового сбора');
                }
            } catch (error) {
                addLog('error', `Ошибка остановки массового сбора: ${error.message}`);
            }
        }

        // Обновление прогресса массового сбора
        function updateMassiveCollectionProgress(data) {
            const progressFill = document.getElementById('massiveProgressFill');
            const progressText = document.getElementById('massiveProgressText');
            const totalCount = document.getElementById('totalPackagesCount');
            const processedCount = document.getElementById('processedPackagesCount');
            const failedCount = document.getElementById('failedPackagesCount');
            const remainingCount = document.getElementById('remainingPackagesCount');

            if (progressFill && progressText) {
                const progress = Math.round(data.progress || 0);
                progressFill.style.width = progress + '%';
                progressText.textContent = progress + '%';

                totalCount.textContent = (data.totalPackages || 0).toLocaleString();
                processedCount.textContent = (data.processedPackages || 0).toLocaleString();
                failedCount.textContent = (data.failedPackages || 0).toLocaleString();
                remainingCount.textContent = (data.remainingPackages || 0).toLocaleString();
            }
        }

        // Завершение массового сбора
        function onMassiveCollectionComplete(data) {
            addLog('success', `🎉 Массовый сбор завершен! Собрано ${data.processedPackages} пакетов`);
            resetMassiveCollectionUI();
            refreshPackages(); // Обновляем список пакетов
        }

        // Сброс UI массового сбора
        function resetMassiveCollectionUI() {
            document.getElementById('massiveProgress').style.display = 'none';
            document.getElementById('massiveCollectionBtn').disabled = false;
            document.getElementById('massiveCollectionBtn').innerHTML = `
                <i class="fas fa-globe"></i>
                Собрать ВСЕ npm пакеты
            `;
        }

        // Добавляем обработчик Enter для поиска и ввода пакетов
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('packageSearch').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchPackages();
                }
            });

            document.getElementById('packageNameInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    addSpecificPackage();
                }
            });

            // Инициализируем загрузку файлов
            initializeFileUpload();

            // Загружаем пакеты при инициализации
            loadPackages();
        });
    </script>
</body>
</html>
