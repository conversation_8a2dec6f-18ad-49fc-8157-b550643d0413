<!DOCTYPE html>
<html lang="ru">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Package Analyzer - Панель управления</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .header h1 {
            color: #2c3e50;
            font-size: 2.5em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header .subtitle {
            color: #7f8c8d;
            font-size: 1.1em;
        }

        .dashboard {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 20px;
        }

        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
        }

        .card-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 2px solid #ecf0f1;
        }

        .card-title {
            font-size: 1.3em;
            font-weight: 600;
            color: #2c3e50;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }

        .status-online { background: #27ae60; }
        .status-offline { background: #e74c3c; }
        .status-warning { background: #f39c12; }

        .metric {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #ecf0f1;
        }

        .metric:last-child {
            border-bottom: none;
        }

        .metric-label {
            color: #7f8c8d;
            font-weight: 500;
        }

        .metric-value {
            font-weight: 600;
            color: #2c3e50;
            font-size: 1.1em;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #ecf0f1;
            border-radius: 4px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #3498db, #2ecc71);
            border-radius: 4px;
            transition: width 0.3s ease;
        }

        .upload-section {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 30px;
            text-align: center;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }

        .upload-area {
            border: 3px dashed #3498db;
            border-radius: 10px;
            padding: 40px;
            margin: 20px 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: #2980b9;
            background: rgba(52, 152, 219, 0.05);
        }

        .upload-area.dragover {
            border-color: #27ae60;
            background: rgba(39, 174, 96, 0.1);
        }

        .btn {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 600;
            transition: all 0.3s ease;
            display: inline-flex;
            align-items: center;
            gap: 8px;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(52, 152, 219, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #229954);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }

        .btn-warning {
            background: linear-gradient(135deg, #f39c12, #e67e22);
        }

        .logs-container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
            height: 400px;
            overflow-y: auto;
        }

        .log-entry {
            padding: 8px 12px;
            margin: 5px 0;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 0.9em;
            border-left: 4px solid #3498db;
        }

        .log-info { background: rgba(52, 152, 219, 0.1); border-left-color: #3498db; }
        .log-success { background: rgba(39, 174, 96, 0.1); border-left-color: #27ae60; }
        .log-warning { background: rgba(243, 156, 18, 0.1); border-left-color: #f39c12; }
        .log-error { background: rgba(231, 76, 60, 0.1); border-left-color: #e74c3c; }

        .chart-container {
            height: 200px;
            margin: 20px 0;
        }

        .task-queue {
            max-height: 300px;
            overflow-y: auto;
        }

        .task-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: rgba(52, 152, 219, 0.05);
            border-radius: 8px;
            border-left: 4px solid #3498db;
        }

        .task-status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
            text-transform: uppercase;
        }

        .task-pending { background: #f39c12; color: white; }
        .task-running { background: #3498db; color: white; }
        .task-completed { background: #27ae60; color: white; }
        .task-failed { background: #e74c3c; color: white; }

        @media (max-width: 768px) {
            .dashboard {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>
                <i class="fas fa-cube"></i>
                Package Analyzer
            </h1>
            <p class="subtitle">Система анализа и управления зависимостями npm пакетов</p>
        </div>

        <!-- Панель статистики системы -->
        <div class="dashboard">
            <!-- Статус системы -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-server"></i>
                        Статус системы
                    </div>
                    <span class="status-indicator status-online" id="systemStatus"></span>
                </div>
                <div class="metric">
                    <span class="metric-label">Сервер</span>
                    <span class="metric-value" id="serverStatus">Онлайн</span>
                </div>
                <div class="metric">
                    <span class="metric-label">База данных</span>
                    <span class="metric-value" id="dbStatus">Подключена</span>
                </div>
                <div class="metric">
                    <span class="metric-label">GPU ускорение</span>
                    <span class="metric-value" id="gpuStatus">Проверка...</span>
                </div>
                <div class="metric" id="gpuDetails" style="display: none;">
                    <span class="metric-label">GPU модель</span>
                    <span class="metric-value" id="gpuModel">--</span>
                </div>
                <div class="metric" id="gpuMemory" style="display: none;">
                    <span class="metric-label">VRAM</span>
                    <span class="metric-value" id="gpuVram">--</span>
                </div>
                <div class="metric" id="cudaVersion" style="display: none;">
                    <span class="metric-label">CUDA версия</span>
                    <span class="metric-value" id="gpuCuda">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Время работы</span>
                    <span class="metric-value" id="uptime">--</span>
                </div>
            </div>

            <!-- Статистика анализа -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-chart-bar"></i>
                        Статистика анализа
                    </div>
                </div>
                <div class="metric">
                    <span class="metric-label">Всего пакетов в БД</span>
                    <span class="metric-value" id="totalPackages">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Анализов сегодня</span>
                    <span class="metric-value" id="todayAnalyses">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Найдено конфликтов</span>
                    <span class="metric-value" id="conflictsFound">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Разрешено конфликтов</span>
                    <span class="metric-value" id="conflictsResolved">0</span>
                </div>
            </div>

            <!-- Производительность -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-tachometer-alt"></i>
                        Производительность
                    </div>
                </div>
                <div class="metric">
                    <span class="metric-label">Использование CPU</span>
                    <span class="metric-value" id="cpuUsage">--</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="cpuProgress" style="width: 0%"></div>
                </div>
                <div class="metric">
                    <span class="metric-label">Использование памяти</span>
                    <span class="metric-value" id="memoryUsage">--</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" id="memoryProgress" style="width: 0%"></div>
                </div>
                <div class="metric">
                    <span class="metric-label">Скорость анализа</span>
                    <span class="metric-value" id="analysisSpeed">-- пак/сек</span>
                </div>
            </div>

            <!-- Сбор данных -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-download"></i>
                        Сбор данных
                    </div>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                    <button class="btn btn-success" onclick="startPackageCollection()">
                        <i class="fas fa-plus"></i>
                        Добавить пакеты
                    </button>
                    <button class="btn btn-warning" onclick="updateExistingPackages()">
                        <i class="fas fa-sync"></i>
                        Обновить существующие
                    </button>
                </div>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 15px;">
                    <button class="btn" onclick="collectPopularPackages()">
                        <i class="fas fa-star"></i>
                        Популярные пакеты
                    </button>
                    <button class="btn" onclick="collectByCategory()">
                        <i class="fas fa-tags"></i>
                        По категориям
                    </button>
                </div>

                <!-- Форма добавления конкретных пакетов -->
                <div style="margin-top: 15px; padding: 15px; background: rgba(52, 152, 219, 0.05); border-radius: 8px;">
                    <h5 style="margin-bottom: 10px;">Добавить конкретные пакеты:</h5>
                    <div style="display: flex; gap: 10px; margin-bottom: 10px;">
                        <input type="text" id="packageNameInput" placeholder="Название пакета (например: react, lodash)"
                               style="flex: 1; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        <button class="btn btn-success" onclick="addSpecificPackage()">
                            <i class="fas fa-plus"></i>
                            Добавить
                        </button>
                    </div>
                    <div style="display: flex; gap: 10px;">
                        <textarea id="packageListInput" placeholder="Или список пакетов (по одному на строку)&#10;react&#10;vue&#10;angular&#10;express"
                                  style="flex: 1; padding: 8px; border: 1px solid #ddd; border-radius: 4px; height: 80px; resize: vertical;"></textarea>
                        <button class="btn btn-success" onclick="addPackageList()">
                            <i class="fas fa-list"></i>
                            Добавить список
                        </button>
                    </div>
                </div>
            </div>

            <!-- Очередь задач -->
            <div class="card">
                <div class="card-header">
                    <div class="card-title">
                        <i class="fas fa-tasks"></i>
                        Очередь задач
                    </div>
                    <div>
                        <button class="btn btn-success" onclick="startFullScan()">
                            <i class="fas fa-play"></i>
                            Полное сканирование
                        </button>
                        <button class="btn btn-danger" onclick="stopAllTasks()">
                            <i class="fas fa-stop"></i>
                            Остановить все
                        </button>
                    </div>
                </div>
                <div class="task-queue" id="taskQueue">
                    <div class="task-item">
                        <span>Инициализация системы</span>
                        <span class="task-status task-completed">Завершено</span>
                    </div>
                </div>

                <!-- Прогресс текущей задачи -->
                <div id="currentTaskProgress" style="display: none; margin-top: 15px; padding: 15px; background: rgba(52, 152, 219, 0.05); border-radius: 8px;">
                    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                        <span id="currentTaskName">Текущая задача</span>
                        <span id="currentTaskPercent">0%</span>
                    </div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="currentTaskProgressBar" style="width: 0%"></div>
                    </div>
                    <div style="margin-top: 10px; font-size: 0.9em; color: #7f8c8d;">
                        <span id="currentTaskDetails">Подготовка...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Секция загрузки файлов -->
        <div class="upload-section">
            <h2><i class="fas fa-upload"></i> Анализ package.json</h2>
            <div class="upload-area" id="uploadArea">
                <i class="fas fa-cloud-upload-alt" style="font-size: 3em; color: #3498db; margin-bottom: 15px;"></i>
                <p style="font-size: 1.2em; margin-bottom: 10px;">Перетащите файл package.json сюда</p>
                <p style="color: #7f8c8d;">или</p>
                <input type="file" id="fileInput" accept=".json" style="display: none;">
                <button class="btn" onclick="document.getElementById('fileInput').click()">
                    <i class="fas fa-folder-open"></i>
                    Выбрать файл
                </button>
            </div>
            <button class="btn btn-success" id="analyzeBtn" style="margin-top: 15px;" disabled>
                <i class="fas fa-search"></i>
                Анализировать
            </button>
        </div>

        <!-- Результаты анализа -->
        <div class="card" id="resultsCard" style="display: none;">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-chart-line"></i>
                    Результаты анализа
                </div>
            </div>
            <div id="results"></div>
        </div>

        <!-- База данных пакетов -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-database"></i>
                    База данных пакетов
                </div>
                <div>
                    <input type="text" id="packageSearch" placeholder="Поиск пакетов..."
                           style="padding: 8px; border: 1px solid #ddd; border-radius: 4px; margin-right: 10px;">
                    <button class="btn" onclick="searchPackages()">
                        <i class="fas fa-search"></i>
                        Поиск
                    </button>
                    <button class="btn btn-success" onclick="refreshPackages()">
                        <i class="fas fa-sync"></i>
                        Обновить
                    </button>
                </div>
            </div>

            <!-- Статистика базы данных -->
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 20px;">
                <div class="metric">
                    <span class="metric-label">Всего пакетов</span>
                    <span class="metric-value" id="dbTotalPackages">0</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Последнее обновление</span>
                    <span class="metric-value" id="dbLastUpdate">--</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Размер БД</span>
                    <span class="metric-value" id="dbSize">--</span>
                </div>
            </div>

            <!-- Таблица пакетов -->
            <div style="max-height: 400px; overflow-y: auto;">
                <table style="width: 100%; border-collapse: collapse;">
                    <thead style="background: #f8f9fa; position: sticky; top: 0;">
                        <tr>
                            <th style="padding: 10px; text-align: left; border-bottom: 2px solid #dee2e6;">Название</th>
                            <th style="padding: 10px; text-align: left; border-bottom: 2px solid #dee2e6;">Версия</th>
                            <th style="padding: 10px; text-align: left; border-bottom: 2px solid #dee2e6;">Описание</th>
                            <th style="padding: 10px; text-align: left; border-bottom: 2px solid #dee2e6;">Лицензия</th>
                            <th style="padding: 10px; text-align: center; border-bottom: 2px solid #dee2e6;">Действия</th>
                        </tr>
                    </thead>
                    <tbody id="packagesTableBody">
                        <tr>
                            <td colspan="5" style="text-align: center; padding: 20px; color: #7f8c8d;">
                                Загрузка пакетов...
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>

            <!-- Пагинация -->
            <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 15px;">
                <div id="paginationInfo">Страница 1 из 1</div>
                <div>
                    <button class="btn" onclick="previousPage()" id="prevPageBtn" disabled>
                        <i class="fas fa-chevron-left"></i>
                        Назад
                    </button>
                    <button class="btn" onclick="nextPage()" id="nextPageBtn" disabled>
                        Вперед
                        <i class="fas fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Детали пакета (модальное окно) -->
        <div id="packageModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000;">
            <div style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); background: white; border-radius: 15px; padding: 30px; max-width: 800px; max-height: 80vh; overflow-y: auto; width: 90%;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                    <h2 id="modalPackageName">Детали пакета</h2>
                    <button onclick="closePackageModal()" style="background: none; border: none; font-size: 24px; cursor: pointer;">&times;</button>
                </div>
                <div id="modalPackageContent">
                    Загрузка...
                </div>
            </div>
        </div>

        <!-- Логи системы -->
        <div class="card">
            <div class="card-header">
                <div class="card-title">
                    <i class="fas fa-terminal"></i>
                    Логи системы
                </div>
                <div>
                    <button class="btn" onclick="clearLogs()">
                        <i class="fas fa-trash"></i>
                        Очистить
                    </button>
                    <button class="btn" onclick="toggleAutoScroll()" id="autoScrollBtn">
                        <i class="fas fa-arrow-down"></i>
                        Авто-прокрутка
                    </button>
                </div>
            </div>
            <div class="logs-container" id="logsContainer">
                <div class="log-entry log-info">
                    <strong>[INFO]</strong> Система инициализирована
                </div>
                <div class="log-entry log-success">
                    <strong>[SUCCESS]</strong> Подключение к базе данных установлено
                </div>
                <div class="log-entry log-info">
                    <strong>[INFO]</strong> GPU ускорение активировано
                </div>
            </div>
        </div>
    </div>

    <script>
        // Глобальные переменные
        let autoScroll = true;
        let ws = null;
        let systemStats = {
            uptime: 0,
            totalPackages: 0,
            todayAnalyses: 0,
            conflictsFound: 0,
            conflictsResolved: 0
        };

        // Инициализация при загрузке страницы
        document.addEventListener('DOMContentLoaded', function() {
            initializeWebSocket();
            initializeFileUpload();
            startStatsUpdater();
            loadSystemStats();
        });

        // WebSocket соединение для обновлений в реальном времени
        function initializeWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;

            try {
                ws = new WebSocket(wsUrl);

                ws.onopen = function() {
                    addLog('success', 'WebSocket соединение установлено');
                };

                ws.onmessage = function(event) {
                    const data = JSON.parse(event.data);
                    handleWebSocketMessage(data);
                };

                ws.onclose = function() {
                    addLog('warning', 'WebSocket соединение закрыто. Попытка переподключения...');
                    setTimeout(initializeWebSocket, 5000);
                };

                ws.onerror = function(error) {
                    addLog('error', 'Ошибка WebSocket: ' + error.message);
                };
            } catch (error) {
                addLog('error', 'Не удалось установить WebSocket соединение');
            }
        }



        // Инициализация загрузки файлов
        function initializeFileUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const analyzeBtn = document.getElementById('analyzeBtn');

            // Drag & Drop
            uploadArea.addEventListener('dragover', function(e) {
                e.preventDefault();
                uploadArea.classList.add('dragover');
            });

            uploadArea.addEventListener('dragleave', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');
            });

            uploadArea.addEventListener('drop', function(e) {
                e.preventDefault();
                uploadArea.classList.remove('dragover');

                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    handleFileSelect(files[0]);
                }
            });

            // Выбор файла
            fileInput.addEventListener('change', function(e) {
                if (e.target.files.length > 0) {
                    handleFileSelect(e.target.files[0]);
                }
            });

            // Кнопка анализа
            analyzeBtn.addEventListener('click', analyzePackage);
        }

        // Обработка выбранного файла
        function handleFileSelect(file) {
            if (file.type !== 'application/json' && !file.name.endsWith('.json')) {
                addLog('error', 'Пожалуйста, выберите JSON файл');
                return;
            }

            document.getElementById('analyzeBtn').disabled = false;
            addLog('info', `Файл выбран: ${file.name}`);
        }

        // Анализ пакета
        async function analyzePackage() {
            const fileInput = document.getElementById('fileInput');
            const file = fileInput.files[0];

            if (!file) {
                addLog('error', 'Пожалуйста, выберите файл package.json');
                return;
            }

            try {
                addLog('info', 'Начинаем анализ пакета...');
                const fileContent = await file.text();

                const response = await fetch('/api/analyze', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        filePath: file.name,
                        content: fileContent
                    })
                });

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                displayAnalysisResults(data);
                addLog('success', 'Анализ завершен успешно');

            } catch (error) {
                addLog('error', 'Ошибка при анализе: ' + error.message);
            }
        }

        // Отображение результатов анализа
        function displayAnalysisResults(data) {
            const resultsCard = document.getElementById('resultsCard');
            const resultsDiv = document.getElementById('results');

            resultsCard.style.display = 'block';

            let html = `
                <div class="metric">
                    <span class="metric-label">Название пакета</span>
                    <span class="metric-value">${data.name || 'Не указано'}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Версия</span>
                    <span class="metric-value">${data.version || 'Не указано'}</span>
                </div>
                <div class="metric">
                    <span class="metric-label">Количество зависимостей</span>
                    <span class="metric-value">${data.dependenciesCount || 0}</span>
                </div>
            `;

            if (data.potentialIssues && data.potentialIssues.length > 0) {
                html += `
                    <div style="margin-top: 20px;">
                        <h4 style="color: #e74c3c; margin-bottom: 10px;">
                            <i class="fas fa-exclamation-triangle"></i>
                            Потенциальные проблемы (${data.potentialIssues.length})
                        </h4>
                `;

                data.potentialIssues.forEach(issue => {
                    html += `
                        <div style="background: rgba(231, 76, 60, 0.1); padding: 10px; margin: 5px 0; border-radius: 5px; border-left: 4px solid #e74c3c;">
                            <strong>${issue.package}@${issue.version}</strong>: ${issue.issue}
                        </div>
                    `;
                });

                html += '</div>';
            }

            if (data.cyclicDependencies && data.cyclicDependencies.length > 0) {
                html += `
                    <div style="margin-top: 20px;">
                        <h4 style="color: #f39c12; margin-bottom: 10px;">
                            <i class="fas fa-sync-alt"></i>
                            Циклические зависимости (${data.cyclicDependencies.length})
                        </h4>
                        <div style="background: rgba(243, 156, 18, 0.1); padding: 10px; border-radius: 5px; border-left: 4px solid #f39c12;">
                            ${data.cyclicDependencies.join(', ')}
                        </div>
                    </div>
                `;
            }

            resultsDiv.innerHTML = html;
        }

        // Загрузка статистики системы
        async function loadSystemStats() {
            try {
                const response = await fetch('/api/stats');
                if (response.ok) {
                    const stats = await response.json();
                    updateSystemStats(stats);
                }
            } catch (error) {
                addLog('warning', 'Не удалось загрузить статистику системы');
            }
        }

        // Обновление статистики системы
        function updateSystemStats(stats) {
            if (stats.totalPackages !== undefined) {
                document.getElementById('totalPackages').textContent = stats.totalPackages.toLocaleString();
            }
            if (stats.todayAnalyses !== undefined) {
                document.getElementById('todayAnalyses').textContent = stats.todayAnalyses.toLocaleString();
            }
            if (stats.conflictsFound !== undefined) {
                document.getElementById('conflictsFound').textContent = stats.conflictsFound.toLocaleString();
            }
            if (stats.conflictsResolved !== undefined) {
                document.getElementById('conflictsResolved').textContent = stats.conflictsResolved.toLocaleString();
            }
            if (stats.uptime !== undefined) {
                document.getElementById('uptime').textContent = formatUptime(stats.uptime);
            }
            if (stats.cpuUsage !== undefined) {
                document.getElementById('cpuUsage').textContent = stats.cpuUsage.toFixed(1) + '%';
                document.getElementById('cpuProgress').style.width = stats.cpuUsage + '%';
            }
            if (stats.memoryUsage !== undefined) {
                document.getElementById('memoryUsage').textContent = stats.memoryUsage.toFixed(1) + '%';
                document.getElementById('memoryProgress').style.width = stats.memoryUsage + '%';
            }
            if (stats.analysisSpeed !== undefined) {
                document.getElementById('analysisSpeed').textContent = stats.analysisSpeed.toFixed(2) + ' пак/сек';
            }

            // Обновляем информацию о GPU
            if (stats.gpuStatus !== undefined) {
                updateGPUStatus(stats.gpuStatus);
            }
        }

        // Обновление статуса GPU
        function updateGPUStatus(gpuStatus) {
            const statusElement = document.getElementById('gpuStatus');
            const detailsElement = document.getElementById('gpuDetails');
            const memoryElement = document.getElementById('gpuMemory');
            const cudaElement = document.getElementById('cudaVersion');

            if (gpuStatus.available) {
                statusElement.textContent = 'Доступно';
                statusElement.style.color = '#27ae60';

                if (gpuStatus.info) {
                    // Показываем детали GPU
                    detailsElement.style.display = 'flex';
                    memoryElement.style.display = 'flex';
                    cudaElement.style.display = 'flex';

                    document.getElementById('gpuModel').textContent = gpuStatus.info.name || 'Неизвестно';
                    document.getElementById('gpuVram').textContent = gpuStatus.info.memory || 'Неизвестно';
                    document.getElementById('gpuCuda').textContent = gpuStatus.info.cudaVersion || 'Неизвестно';
                }
            } else {
                statusElement.textContent = 'Недоступно';
                statusElement.style.color = '#e74c3c';

                // Скрываем детали
                detailsElement.style.display = 'none';
                memoryElement.style.display = 'none';
                cudaElement.style.display = 'none';
            }
        }

        // Форматирование времени работы
        function formatUptime(seconds) {
            const days = Math.floor(seconds / 86400);
            const hours = Math.floor((seconds % 86400) / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);

            if (days > 0) {
                return `${days}д ${hours}ч ${minutes}м`;
            } else if (hours > 0) {
                return `${hours}ч ${minutes}м`;
            } else {
                return `${minutes}м`;
            }
        }

        // Добавление лога
        function addLog(level, message) {
            const logsContainer = document.getElementById('logsContainer');
            const timestamp = new Date().toLocaleTimeString();

            const logEntry = document.createElement('div');
            logEntry.className = `log-entry log-${level}`;
            logEntry.innerHTML = `<strong>[${timestamp}] [${level.toUpperCase()}]</strong> ${message}`;

            logsContainer.appendChild(logEntry);

            if (autoScroll) {
                logsContainer.scrollTop = logsContainer.scrollHeight;
            }
        }

        // Очистка логов
        function clearLogs() {
            document.getElementById('logsContainer').innerHTML = '';
            addLog('info', 'Логи очищены');
        }

        // Переключение авто-прокрутки
        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            const btn = document.getElementById('autoScrollBtn');
            btn.innerHTML = autoScroll ?
                '<i class="fas fa-arrow-down"></i> Авто-прокрутка' :
                '<i class="fas fa-pause"></i> Авто-прокрутка';
        }

        // Обновление очереди задач
        function updateTaskQueue(task) {
            const taskQueue = document.getElementById('taskQueue');

            let taskItem = document.getElementById(`task-${task.id}`);
            if (!taskItem) {
                taskItem = document.createElement('div');
                taskItem.className = 'task-item';
                taskItem.id = `task-${task.id}`;
                taskQueue.appendChild(taskItem);
            }

            taskItem.innerHTML = `
                <span>${task.name}</span>
                <span class="task-status task-${task.status}">${getTaskStatusText(task.status)}</span>
            `;
        }

        // Получение текста статуса задачи
        function getTaskStatusText(status) {
            const statusMap = {
                'pending': 'Ожидание',
                'running': 'Выполняется',
                'completed': 'Завершено',
                'failed': 'Ошибка'
            };
            return statusMap[status] || status;
        }

        // Запуск полного сканирования
        async function startFullScan() {
            try {
                addLog('info', 'Запуск полного сканирования npm пакетов...');

                const response = await fetch('/api/full-scan', {
                    method: 'POST'
                });

                if (response.ok) {
                    addLog('success', 'Полное сканирование запущено');
                } else {
                    throw new Error('Ошибка при запуске сканирования');
                }
            } catch (error) {
                addLog('error', 'Ошибка при запуске полного сканирования: ' + error.message);
            }
        }

        // Периодическое обновление статистики
        function startStatsUpdater() {
            setInterval(async () => {
                await loadSystemStats();
            }, 5000); // Обновление каждые 5 секунд
        }

        // Переменные для пагинации пакетов
        let currentPage = 1;
        let totalPages = 1;
        let currentSearch = '';

        // Загрузка пакетов из базы данных
        async function loadPackages(page = 1, search = '') {
            try {
                const response = await fetch(`/api/packages?page=${page}&limit=20&search=${encodeURIComponent(search)}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const data = await response.json();
                displayPackages(data.packages);
                updatePagination(data.pagination);

                // Обновляем статистику БД
                document.getElementById('dbTotalPackages').textContent = data.pagination.total.toLocaleString();

            } catch (error) {
                addLog('error', 'Ошибка загрузки пакетов: ' + error.message);
                document.getElementById('packagesTableBody').innerHTML = `
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px; color: #e74c3c;">
                            Ошибка загрузки пакетов: ${error.message}
                        </td>
                    </tr>
                `;
            }
        }

        // Отображение пакетов в таблице
        function displayPackages(packages) {
            const tbody = document.getElementById('packagesTableBody');

            if (packages.length === 0) {
                tbody.innerHTML = `
                    <tr>
                        <td colspan="5" style="text-align: center; padding: 20px; color: #7f8c8d;">
                            Пакеты не найдены
                        </td>
                    </tr>
                `;
                return;
            }

            tbody.innerHTML = packages.map(pkg => `
                <tr style="border-bottom: 1px solid #dee2e6;">
                    <td style="padding: 10px;">
                        <strong>${pkg.name}</strong>
                    </td>
                    <td style="padding: 10px;">
                        <span style="background: #e3f2fd; color: #1976d2; padding: 2px 6px; border-radius: 4px; font-size: 0.8em;">
                            ${pkg.latest_version || 'N/A'}
                        </span>
                    </td>
                    <td style="padding: 10px; max-width: 300px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">
                        ${pkg.description || 'Описание отсутствует'}
                    </td>
                    <td style="padding: 10px;">
                        <span style="background: #f3e5f5; color: #7b1fa2; padding: 2px 6px; border-radius: 4px; font-size: 0.8em;">
                            ${pkg.license || 'N/A'}
                        </span>
                    </td>
                    <td style="padding: 10px; text-align: center;">
                        <button class="btn" onclick="viewPackageDetails('${pkg.name}')" style="padding: 5px 10px; font-size: 0.8em;">
                            <i class="fas fa-eye"></i>
                            Детали
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        // Обновление пагинации
        function updatePagination(pagination) {
            currentPage = pagination.page;
            totalPages = pagination.pages;

            document.getElementById('paginationInfo').textContent =
                `Страница ${pagination.page} из ${pagination.pages} (всего: ${pagination.total.toLocaleString()})`;

            document.getElementById('prevPageBtn').disabled = pagination.page <= 1;
            document.getElementById('nextPageBtn').disabled = pagination.page >= pagination.pages;
        }

        // Поиск пакетов
        function searchPackages() {
            const searchInput = document.getElementById('packageSearch');
            currentSearch = searchInput.value.trim();
            currentPage = 1;
            loadPackages(currentPage, currentSearch);
            addLog('info', `Поиск пакетов: "${currentSearch}"`);
        }

        // Обновление списка пакетов
        function refreshPackages() {
            addLog('info', 'Обновление списка пакетов...');
            loadPackages(currentPage, currentSearch);
        }

        // Предыдущая страница
        function previousPage() {
            if (currentPage > 1) {
                currentPage--;
                loadPackages(currentPage, currentSearch);
            }
        }

        // Следующая страница
        function nextPage() {
            if (currentPage < totalPages) {
                currentPage++;
                loadPackages(currentPage, currentSearch);
            }
        }

        // Просмотр деталей пакета
        async function viewPackageDetails(packageName) {
            try {
                document.getElementById('packageModal').style.display = 'block';
                document.getElementById('modalPackageName').textContent = `Детали пакета: ${packageName}`;
                document.getElementById('modalPackageContent').innerHTML = 'Загрузка...';

                const response = await fetch(`/api/packages/${encodeURIComponent(packageName)}`);
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const packageData = await response.json();
                displayPackageDetails(packageData);

            } catch (error) {
                document.getElementById('modalPackageContent').innerHTML = `
                    <div style="color: #e74c3c; text-align: center; padding: 20px;">
                        Ошибка загрузки деталей пакета: ${error.message}
                    </div>
                `;
            }
        }

        // Отображение деталей пакета
        function displayPackageDetails(pkg) {
            const versionsHtml = pkg.versions && pkg.versions.length > 0 ?
                pkg.versions.slice(0, 10).map(v => `
                    <div style="margin: 5px 0; padding: 8px; background: #f8f9fa; border-radius: 4px;">
                        <strong>${v.version}</strong>
                        <span style="color: #6c757d; font-size: 0.9em; margin-left: 10px;">
                            ${new Date(v.published_at).toLocaleDateString()}
                        </span>
                        <div style="margin-top: 5px; font-size: 0.8em;">
                            Зависимостей: ${Object.keys(v.dependencies || {}).length}
                        </div>
                    </div>
                `).join('') : '<p>Версии не найдены</p>';

            document.getElementById('modalPackageContent').innerHTML = `
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <h4>Основная информация</h4>
                        <div class="metric">
                            <span class="metric-label">Название</span>
                            <span class="metric-value">${pkg.name}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Последняя версия</span>
                            <span class="metric-value">${pkg.latest_version || 'N/A'}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Лицензия</span>
                            <span class="metric-value">${pkg.license || 'N/A'}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Создан</span>
                            <span class="metric-value">${new Date(pkg.created_at).toLocaleDateString()}</span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Обновлен</span>
                            <span class="metric-value">${new Date(pkg.updated_at).toLocaleDateString()}</span>
                        </div>
                    </div>
                    <div>
                        <h4>Ссылки</h4>
                        <div class="metric">
                            <span class="metric-label">Репозиторий</span>
                            <span class="metric-value">
                                ${pkg.repository ?
                                    `<a href="${pkg.repository}" target="_blank" style="color: #3498db;">Открыть</a>` :
                                    'N/A'
                                }
                            </span>
                        </div>
                        <div class="metric">
                            <span class="metric-label">Домашняя страница</span>
                            <span class="metric-value">
                                ${pkg.homepage ?
                                    `<a href="${pkg.homepage}" target="_blank" style="color: #3498db;">Открыть</a>` :
                                    'N/A'
                                }
                            </span>
                        </div>
                    </div>
                </div>

                <div style="margin-bottom: 20px;">
                    <h4>Описание</h4>
                    <p style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin: 10px 0;">
                        ${pkg.description || 'Описание отсутствует'}
                    </p>
                </div>

                <div>
                    <h4>Версии (последние 10)</h4>
                    <div style="max-height: 300px; overflow-y: auto;">
                        ${versionsHtml}
                    </div>
                </div>

                <div style="margin-top: 20px; text-align: right;">
                    <button class="btn btn-warning" onclick="editPackage('${pkg.name}')">
                        <i class="fas fa-edit"></i>
                        Редактировать
                    </button>
                </div>
            `;
        }

        // Закрытие модального окна
        function closePackageModal() {
            document.getElementById('packageModal').style.display = 'none';
        }

        // Редактирование пакета (заглушка)
        function editPackage(packageName) {
            addLog('info', `Редактирование пакета ${packageName} (функция в разработке)`);
            closePackageModal();
        }

        // ========== ФУНКЦИИ СБОРА ДАННЫХ ==========

        // Запуск сбора пакетов
        async function startPackageCollection() {
            try {
                addLog('info', 'Запуск автоматического сбора популярных пакетов...');

                const response = await fetch('/api/collect/auto', {
                    method: 'POST'
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('success', `Запущен сбор ${result.count} пакетов`);
                    showTaskProgress('Сбор популярных пакетов', 0);
                } else {
                    throw new Error('Ошибка запуска сбора');
                }
            } catch (error) {
                addLog('error', 'Ошибка при запуске сбора: ' + error.message);
            }
        }

        // Обновление существующих пакетов
        async function updateExistingPackages() {
            try {
                addLog('info', 'Запуск обновления существующих пакетов...');

                const response = await fetch('/api/collect/update', {
                    method: 'POST'
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('success', `Запущено обновление ${result.count} пакетов`);
                    showTaskProgress('Обновление пакетов', 0);
                } else {
                    throw new Error('Ошибка запуска обновления');
                }
            } catch (error) {
                addLog('error', 'Ошибка при обновлении: ' + error.message);
            }
        }

        // Сбор популярных пакетов
        async function collectPopularPackages() {
            try {
                addLog('info', 'Запуск сбора топ-1000 популярных пакетов...');

                const response = await fetch('/api/collect/popular', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ limit: 1000 })
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('success', `Запущен сбор ${result.count} популярных пакетов`);
                    showTaskProgress('Сбор популярных пакетов', 0);
                } else {
                    throw new Error('Ошибка запуска сбора популярных пакетов');
                }
            } catch (error) {
                addLog('error', 'Ошибка при сборе популярных пакетов: ' + error.message);
            }
        }

        // Сбор по категориям
        async function collectByCategory() {
            const categories = [
                'framework', 'library', 'utility', 'build-tool', 'testing',
                'react', 'vue', 'angular', 'node', 'webpack', 'babel'
            ];

            try {
                addLog('info', 'Запуск сбора пакетов по категориям...');

                const response = await fetch('/api/collect/categories', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ categories: categories })
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('success', `Запущен сбор пакетов по ${categories.length} категориям`);
                    showTaskProgress('Сбор по категориям', 0);
                } else {
                    throw new Error('Ошибка запуска сбора по категориям');
                }
            } catch (error) {
                addLog('error', 'Ошибка при сборе по категориям: ' + error.message);
            }
        }

        // Добавление конкретного пакета
        async function addSpecificPackage() {
            const packageName = document.getElementById('packageNameInput').value.trim();

            if (!packageName) {
                addLog('warning', 'Введите название пакета');
                return;
            }

            try {
                addLog('info', `Добавление пакета: ${packageName}`);

                const response = await fetch('/api/collect/package', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ packageName: packageName })
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('success', `Пакет ${packageName} добавлен в очередь`);
                    document.getElementById('packageNameInput').value = '';

                    // Обновляем список пакетов
                    refreshPackages();
                } else {
                    const error = await response.json();
                    throw new Error(error.error || 'Ошибка добавления пакета');
                }
            } catch (error) {
                addLog('error', `Ошибка добавления пакета ${packageName}: ${error.message}`);
            }
        }

        // Добавление списка пакетов
        async function addPackageList() {
            const packageList = document.getElementById('packageListInput').value.trim();

            if (!packageList) {
                addLog('warning', 'Введите список пакетов');
                return;
            }

            const packages = packageList.split('\n')
                .map(pkg => pkg.trim())
                .filter(pkg => pkg.length > 0);

            if (packages.length === 0) {
                addLog('warning', 'Список пакетов пуст');
                return;
            }

            try {
                addLog('info', `Добавление ${packages.length} пакетов...`);

                const response = await fetch('/api/collect/packages', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ packages: packages })
                });

                if (response.ok) {
                    const result = await response.json();
                    addLog('success', `${packages.length} пакетов добавлено в очередь`);
                    document.getElementById('packageListInput').value = '';
                    showTaskProgress('Добавление пакетов', 0);

                    // Обновляем список пакетов
                    refreshPackages();
                } else {
                    const error = await response.json();
                    throw new Error(error.error || 'Ошибка добавления пакетов');
                }
            } catch (error) {
                addLog('error', `Ошибка добавления пакетов: ${error.message}`);
            }
        }

        // Остановка всех задач
        async function stopAllTasks() {
            try {
                addLog('warning', 'Остановка всех задач...');

                const response = await fetch('/api/tasks/stop', {
                    method: 'POST'
                });

                if (response.ok) {
                    addLog('success', 'Все задачи остановлены');
                    hideTaskProgress();
                } else {
                    throw new Error('Ошибка остановки задач');
                }
            } catch (error) {
                addLog('error', 'Ошибка при остановке задач: ' + error.message);
            }
        }

        // Показать прогресс задачи
        function showTaskProgress(taskName, progress) {
            const progressDiv = document.getElementById('currentTaskProgress');
            const nameSpan = document.getElementById('currentTaskName');
            const percentSpan = document.getElementById('currentTaskPercent');
            const progressBar = document.getElementById('currentTaskProgressBar');
            const detailsSpan = document.getElementById('currentTaskDetails');

            progressDiv.style.display = 'block';
            nameSpan.textContent = taskName;
            percentSpan.textContent = Math.round(progress) + '%';
            progressBar.style.width = progress + '%';
            detailsSpan.textContent = 'Выполняется...';
        }

        // Обновить прогресс задачи
        function updateTaskProgress(progress, details) {
            const percentSpan = document.getElementById('currentTaskPercent');
            const progressBar = document.getElementById('currentTaskProgressBar');
            const detailsSpan = document.getElementById('currentTaskDetails');

            if (percentSpan) {
                percentSpan.textContent = Math.round(progress) + '%';
                progressBar.style.width = progress + '%';
                if (details) {
                    detailsSpan.textContent = details;
                }
            }
        }

        // Скрыть прогресс задачи
        function hideTaskProgress() {
            const progressDiv = document.getElementById('currentTaskProgress');
            progressDiv.style.display = 'none';
        }

        // Обработка сообщений WebSocket для прогресса
        function handleWebSocketMessage(data) {
            switch(data.type) {
                case 'stats':
                    updateSystemStats(data.stats);
                    break;
                case 'log':
                    addLog(data.level, data.message);
                    break;
                case 'task':
                    updateTaskQueue(data.task);
                    break;
                case 'progress':
                    updateTaskProgress(data.progress, data.details);
                    break;
                case 'task_complete':
                    hideTaskProgress();
                    addLog('success', `Задача завершена: ${data.taskName}`);
                    refreshPackages(); // Обновляем список пакетов
                    break;
                case 'analysis':
                    displayAnalysisResults(data.results);
                    break;
            }
        }

        // Добавляем обработчик Enter для поиска и ввода пакетов
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('packageSearch').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    searchPackages();
                }
            });

            document.getElementById('packageNameInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    addSpecificPackage();
                }
            });

            // Загружаем пакеты при инициализации
            loadPackages();
        });
    </script>
</body>
</html>
