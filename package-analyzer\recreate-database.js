// Скрипт для полного пересоздания базы данных с правильной схемой
const fs = require('fs');
const path = require('path');
const Database = require('./db/database');

async function recreateDatabase() {
  console.log('🔧 Полное пересоздание базы данных...');
  
  const dbPath = path.join(__dirname, 'db', 'packages.db');
  const backupPath = path.join(__dirname, 'db', 'packages_old.db');
  
  try {
    // Создаем резервную копию существующей БД
    if (fs.existsSync(dbPath)) {
      console.log('📦 Создание резервной копии базы данных...');
      fs.copyFileSync(dbPath, backupPath);
      console.log('✅ Резервная копия создана: packages_old.db');
      
      // Удаляем старую БД
      fs.unlinkSync(dbPath);
      console.log('🗑️ Старая база данных удалена');
    }
    
    // Создаем новую базу данных с правильной схемой
    console.log('🏗️ Создание новой базы данных с правильной схемой...');
    const db = new Database();
    await db.initialize();
    
    console.log('✅ База данных успешно создана с правильной схемой!');
    
    // Проверяем структуру таблицы
    console.log('🔍 Проверка структуры таблицы packages...');
    
    db.db.all("PRAGMA table_info(packages)", (err, columns) => {
      if (err) {
        console.error('❌ Ошибка проверки структуры:', err);
        return;
      }
      
      console.log('📊 Структура таблицы packages:');
      console.log('   Колонки:');
      columns.forEach((col, index) => {
        const nullable = col.notnull ? 'NOT NULL' : 'NULL';
        const defaultVal = col.dflt_value ? ` DEFAULT ${col.dflt_value}` : '';
        console.log(`   ${index + 1}. ${col.name}: ${col.type} ${nullable}${defaultVal}`);
      });
      
      console.log(`\n📈 Всего колонок: ${columns.length}`);
      
      // Проверяем наличие ключевых колонок
      const requiredColumns = ['name', 'author', 'maintainers', 'contributors', 'weekly_downloads', 'github_stars'];
      const existingColumns = columns.map(col => col.name);
      
      console.log('\n🔍 Проверка ключевых колонок:');
      requiredColumns.forEach(col => {
        const exists = existingColumns.includes(col);
        console.log(`   ${exists ? '✅' : '❌'} ${col}: ${exists ? 'найдена' : 'отсутствует'}`);
      });
      
      console.log('\n🎉 База данных полностью пересоздана!');
      console.log('📝 Теперь можно безопасно собирать пакеты без ошибок схемы.');
      
      // Закрываем соединение
      db.db.close((err) => {
        if (err) {
          console.error('Ошибка закрытия БД:', err);
        } else {
          console.log('✅ Соединение с БД закрыто');
        }
        process.exit(0);
      });
    });
    
  } catch (error) {
    console.error('❌ Ошибка пересоздания базы данных:', error.message);
    
    // Восстанавливаем из резервной копии при ошибке
    if (fs.existsSync(backupPath)) {
      console.log('🔄 Восстановление из резервной копии...');
      fs.copyFileSync(backupPath, dbPath);
      console.log('✅ База данных восстановлена из резервной копии');
    }
    
    process.exit(1);
  }
}

// Запускаем пересоздание
if (require.main === module) {
  recreateDatabase().catch(error => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
}

module.exports = { recreateDatabase };
