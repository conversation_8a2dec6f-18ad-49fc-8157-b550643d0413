#!/usr/bin/env node

// Простой скрипт для запуска сервера
console.log('🚀 Запуск Package Analyzer Server...');

try {
  // Устанавливаем рабочую директорию
  process.chdir(__dirname);
  console.log('📁 Рабочая директория:', process.cwd());
  
  // Проверяем наличие необходимых файлов
  const fs = require('fs');
  const path = require('path');
  
  const requiredFiles = ['server.js', 'index.js', 'gpu-accelerator.js'];
  for (const file of requiredFiles) {
    if (!fs.existsSync(path.join(__dirname, file))) {
      console.error(`❌ Отсутствует файл: ${file}`);
      process.exit(1);
    }
  }
  
  console.log('✅ Все необходимые файлы найдены');
  
  // Запускаем сервер
  require('./server.js');
  
} catch (error) {
  console.error('❌ Ошибка запуска сервера:', error.message);
  console.error('Stack trace:', error.stack);
  process.exit(1);
}
