const Database = require('../db/database');

async function addSampleData() {
  const db = new Database();
  
  try {
    await db.initialize();
    console.log('📊 Добавляем тестовые данные в базу...');
    
    const samplePackages = [
      {
        name: 'react',
        description: 'A JavaScript library for building user interfaces',
        repository: 'https://github.com/facebook/react',
        homepage: 'https://reactjs.org/',
        license: 'MIT',
        latest_version: '18.2.0',
        versions: ['18.2.0', '18.1.0', '18.0.0', '17.0.2']
      },
      {
        name: 'express',
        description: 'Fast, unopinionated, minimalist web framework for node.',
        repository: 'https://github.com/expressjs/express',
        homepage: 'http://expressjs.com/',
        license: 'MIT',
        latest_version: '4.18.2',
        versions: ['4.18.2', '4.18.1', '4.18.0', '4.17.3']
      },
      {
        name: 'lodash',
        description: 'Lodash modular utilities.',
        repository: 'https://github.com/lodash/lodash',
        homepage: 'https://lodash.com/',
        license: 'MIT',
        latest_version: '4.17.21',
        versions: ['4.17.21', '4.17.20', '4.17.19', '4.17.18']
      },
      {
        name: 'axios',
        description: 'Promise based HTTP client for the browser and node.js',
        repository: 'https://github.com/axios/axios',
        homepage: 'https://axios-http.com',
        license: 'MIT',
        latest_version: '1.6.2',
        versions: ['1.6.2', '1.6.1', '1.6.0', '1.5.1']
      },
      {
        name: 'vue',
        description: 'The progressive JavaScript framework',
        repository: 'https://github.com/vuejs/core',
        homepage: 'https://vuejs.org/',
        license: 'MIT',
        latest_version: '3.3.8',
        versions: ['3.3.8', '3.3.7', '3.3.6', '3.3.5']
      },
      {
        name: 'webpack',
        description: 'Packs CommonJs/AMD modules for the browser.',
        repository: 'https://github.com/webpack/webpack',
        homepage: 'https://webpack.js.org',
        license: 'MIT',
        latest_version: '5.89.0',
        versions: ['5.89.0', '5.88.2', '5.88.1', '5.88.0']
      },
      {
        name: 'typescript',
        description: 'TypeScript is a language for application scale JavaScript development',
        repository: 'https://github.com/Microsoft/TypeScript',
        homepage: 'https://www.typescriptlang.org/',
        license: 'Apache-2.0',
        latest_version: '5.3.2',
        versions: ['5.3.2', '5.3.1', '5.2.2', '5.2.1']
      },
      {
        name: 'moment',
        description: 'Parse, validate, manipulate, and display dates',
        repository: 'https://github.com/moment/moment',
        homepage: 'http://momentjs.com',
        license: 'MIT',
        latest_version: '2.29.4',
        versions: ['2.29.4', '2.29.3', '2.29.2', '2.29.1']
      },
      {
        name: 'jquery',
        description: 'JavaScript library for DOM operations',
        repository: 'https://github.com/jquery/jquery',
        homepage: 'https://jquery.com',
        license: 'MIT',
        latest_version: '3.7.1',
        versions: ['3.7.1', '3.7.0', '3.6.4', '3.6.3']
      },
      {
        name: 'bootstrap',
        description: 'The most popular front-end framework for developing responsive, mobile first projects on the web.',
        repository: 'https://github.com/twbs/bootstrap',
        homepage: 'https://getbootstrap.com/',
        license: 'MIT',
        latest_version: '5.3.2',
        versions: ['5.3.2', '5.3.1', '5.3.0', '5.2.3']
      }
    ];
    
    for (const pkg of samplePackages) {
      console.log(`📦 Добавляем пакет: ${pkg.name}`);
      
      // Добавляем основную информацию о пакете
      await db.updatePackageInfo(pkg.name, {
        description: pkg.description,
        repository: pkg.repository,
        homepage: pkg.homepage,
        license: pkg.license,
        latest_version: pkg.latest_version
      });
      
      console.log(`✅ Пакет ${pkg.name} добавлен`);
    }
    
    console.log('🎉 Все тестовые данные добавлены успешно!');
    
  } catch (error) {
    console.error('❌ Ошибка при добавлении тестовых данных:', error);
  } finally {
    db.close();
  }
}

// Запускаем только если файл вызван напрямую
if (require.main === module) {
  addSampleData();
}

module.exports = { addSampleData };
