const axios = require('axios');
const Database = require('../db/database');
const path = require('path');
const { Worker, isMainThread, workerData, parentPort } = require('worker_threads');
const fs = require('fs');

const BATCH_SIZE = 1000;
const WORKER_COUNT = 8;
const RETRY_LIMIT = 3;
const API_DELAY = 100; // ms между API вызовами
const REGISTRY_URL = 'https://registry.npmjs.org/-/all';
const PACKAGES_FILE = path.join(__dirname, '../data/all-packages.json');

// Получение списка всех пакетов из npm registry
async function fetchAllPackageNames() {
  console.log('Получение списка всех пакетов из npm registry...');
  
  try {
    // Проверяем, есть ли уже сохраненный список пакетов
    if (fs.existsSync(PACKAGES_FILE)) {
      console.log('Используем сохраненный список пакетов');
      const packagesData = JSON.parse(fs.readFileSync(PACKAGES_FILE, 'utf8'));
      return Object.keys(packagesData).filter(name => name.charAt(0) !== '_');
    }
    
    // Если нет, получаем с npm registry
    console.log('Загрузка списка всех пакетов (это может занять некоторое время)...');
    const response = await axios.get(REGISTRY_URL, { timeout: 60000 });
    const packagesData = response.data;
    
    // Сохраняем для будущего использования
    if (!fs.existsSync(path.dirname(PACKAGES_FILE))) {
      fs.mkdirSync(path.dirname(PACKAGES_FILE), { recursive: true });
    }
    fs.writeFileSync(PACKAGES_FILE, JSON.stringify(packagesData));
    
    // Фильтруем служебные записи (начинающиеся с '_')
    return Object.keys(packagesData).filter(name => name.charAt(0) !== '_');
  } catch (error) {
    console.error('Ошибка при получении списка пакетов:', error.message);
    throw error;
  }
}

// Разделение пакетов на батчи для обработки
function splitIntoBatches(packages, batchSize) {
  const batches = [];
  for (let i = 0; i < packages.length; i += batchSize) {
    batches.push(packages.slice(i, i + batchSize));
  }
  return batches;
}

// Основная функция для запуска сканирования
async function startFullScan() {
  if (!isMainThread) {
    return processBatch(workerData.packages, workerData.workerId);
  }

  try {
    const allPackages = await fetchAllPackageNames();
    console.log(`Найдено ${allPackages.length} пакетов`);
    
    const batches = splitIntoBatches(allPackages, BATCH_SIZE);
    console.log(`Разделено на ${batches.length} батчей по ${BATCH_SIZE} пакетов`);
    
    let completedBatches = 0;
    let processedPackages = 0;
    
    // Запускаем обработку батчей с ограничением на количество одновременных воркеров
    for (let i = 0; i < batches.length; i += WORKER_COUNT) {
      const currentBatches = batches.slice(i, i + WORKER_COUNT);
      const workers = currentBatches.map((batch, index) => {
        const worker = new Worker(__filename, {
          workerData: {
            packages: batch,
            workerId: i + index
          }
        });
        
        worker.on('message', (message) => {
          processedPackages += message.processed;
          console.log(`Прогресс: ${processedPackages}/${allPackages.length} пакетов (${(processedPackages / allPackages.length * 100).toFixed(2)}%)`);
        });
        
        worker.on('error', (err) => {
          console.error(`Ошибка в воркере ${i + index}:`, err);
        });
        
        worker.on('exit', (code) => {
          completedBatches++;
          console.log(`Воркер ${i + index} завершил работу с кодом ${code}`);
          console.log(`Завершено батчей: ${completedBatches}/${batches.length}`);
          
          if (completedBatches === batches.length) {
            console.log('Сканирование завершено!');
          }
        });
        
        return worker;
      });
      
      // Ждем завершения текущей группы воркеров
      await Promise.all(workers.map(worker => {
        return new Promise((resolve) => {
          worker.on('exit', resolve);
        });
      }));
    }
  } catch (error) {
    console.error('Ошибка при сканировании:', error);
  }
}

async function fetchPackageInfo(packageName, attempt = 1) {
  try {
    const response = await axios.get(`https://registry.npmjs.org/${packageName}`, {
      timeout: 5000
    });
    return {
      name: packageName,
      description: response.data.description,
      repository: response.data.repository?.url,
      homepage: response.data.homepage,
      license: response.data.license,
      versions: Object.keys(response.data.versions),
      latest: response.data['dist-tags']?.latest,
      dependencies: response.data.versions?.[response.data['dist-tags']?.latest]?.dependencies || {}
    };
  } catch (error) {
    if (attempt < RETRY_LIMIT) {
      await new Promise(resolve => setTimeout(resolve, API_DELAY * attempt));
      return fetchPackageInfo(packageName, attempt + 1);
    }
    throw error;
  }
}

// Функция для обработки батча пакетов в воркере
async function processBatch(packageNames, workerId) {
  const db = new Database();
  let processed = 0;
  
  for (const packageName of packageNames) {
    try {
      const packageInfo = await fetchPackageInfo(packageName);
      if (packageInfo) {
        await db.updatePackageInfo(packageInfo);
        processed++;
        
        if (processed % 100 === 0) {
          if (parentPort) {
            parentPort.postMessage({ processed: 100 });
          }
          console.log(`[Воркер ${workerId}] Обработано ${processed}/${packageNames.length}`);
        }
      }
    } catch (error) {
      console.error(`[Воркер ${workerId}] Ошибка при обработке ${packageName}:`, error.message);
    }
    
    // Задержка между запросами для предотвращения блокировки
    await new Promise(resolve => setTimeout(resolve, API_DELAY));
  }
  
  // Отправляем оставшиеся обработанные пакеты
  if (parentPort && processed % 100 !== 0) {
    parentPort.postMessage({ processed: processed % 100 });
  }
  
  db.close();
  return processed;
}

// Запускаем сканирование, если это главный поток
if (isMainThread) {
  startFullScan().catch(console.error);
}

// ... (остальной код остается без изменений)
