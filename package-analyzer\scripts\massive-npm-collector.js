// Массовый сборщик всех npm пакетов
const axios = require('axios');
const Database = require('../db/database');
const EnhancedPackageCollector = require('../lib/enhanced-package-collector');

class MassiveNpmCollector {
  constructor() {
    this.db = new Database();
    this.enhancedCollector = new EnhancedPackageCollector(this.db);
    
    this.stats = {
      totalPackages: 0,
      processedPackages: 0,
      failedPackages: 0,
      remainingPackages: 0,
      progress: 0,
      startTime: null,
      isRunning: false,
      shouldStop: false
    };
    
    this.batchSize = 50; // Обрабатываем по 50 пакетов за раз
    this.delayBetweenBatches = 5000; // 5 секунд между батчами
    this.maxRetries = 3;
  }

  // Получение статистики
  getStats() {
    return { ...this.stats };
  }

  // Остановка сбора
  stop() {
    this.stats.shouldStop = true;
    console.log('🛑 Получен сигнал остановки массового сбора');
  }

  // Запуск массового сбора всех npm пакетов
  async startMassiveCollection() {
    try {
      this.stats.isRunning = true;
      this.stats.startTime = Date.now();
      this.stats.shouldStop = false;
      
      console.log('🌍 Начинаем массовый сбор ВСЕХ npm пакетов...');
      
      // 1. Получаем список всех пакетов из npm registry
      console.log('📋 Получение списка всех пакетов из npm registry...');
      const allPackages = await this.getAllNpmPackages();
      
      this.stats.totalPackages = allPackages.length;
      this.stats.remainingPackages = allPackages.length;
      
      console.log(`📦 Найдено ${this.stats.totalPackages.toLocaleString()} пакетов для сбора`);
      
      // 2. Обрабатываем пакеты батчами
      await this.processBatches(allPackages);
      
      this.stats.isRunning = false;
      console.log('🎉 Массовый сбор завершен!');
      
    } catch (error) {
      this.stats.isRunning = false;
      console.error('❌ Ошибка массового сбора:', error);
      throw error;
    }
  }

  // Получение списка всех npm пакетов
  async getAllNpmPackages() {
    try {
      console.log('🔍 Запрос к npm registry для получения всех пакетов...');
      
      // Используем npm registry API для получения всех пакетов
      const response = await axios.get('https://registry.npmjs.org/-/all', {
        timeout: 60000, // 60 секунд таймаут
        headers: {
          'User-Agent': 'Package-Analyzer-Massive-Collector/1.0.0'
        }
      });
      
      const allPackagesData = response.data;
      const packageNames = Object.keys(allPackagesData).filter(name => {
        // Фильтруем служебные записи
        return !name.startsWith('_') && name !== 'readme';
      });
      
      console.log(`✅ Получено ${packageNames.length.toLocaleString()} пакетов из registry`);
      return packageNames;
      
    } catch (error) {
      console.error('❌ Ошибка получения списка пакетов:', error.message);
      
      // Fallback: используем популярные пакеты
      console.log('🔄 Переходим к сбору популярных пакетов...');
      return await this.getPopularPackages();
    }
  }

  // Fallback: получение популярных пакетов
  async getPopularPackages() {
    try {
      const popularPackages = [
        'react', 'vue', 'angular', 'lodash', 'express', 'axios', 'webpack', 
        'babel-core', 'typescript', 'eslint', 'prettier', 'jest', 'moment',
        'jquery', 'bootstrap', 'socket.io', 'mongoose', 'sequelize', 'redis',
        'passport', 'bcrypt', 'jsonwebtoken', 'cors', 'helmet', 'morgan',
        'nodemon', 'concurrently', 'cross-env', 'dotenv', 'chalk', 'commander',
        'inquirer', 'yargs', 'fs-extra', 'glob', 'rimraf', 'mkdirp',
        'uuid', 'validator', 'joi', 'ajv', 'cheerio', 'puppeteer',
        'selenium-webdriver', 'mocha', 'chai', 'sinon', 'supertest',
        'request', 'node-fetch', 'got', 'form-data', 'multer',
        'sharp', 'jimp', 'canvas', 'pdf2pic', 'html-pdf'
      ];
      
      // Добавляем еще пакеты через поиск
      const searchTerms = ['react-', 'vue-', 'angular-', 'node-', 'webpack-', 'babel-'];
      
      for (const term of searchTerms) {
        try {
          const searchResponse = await axios.get(`https://registry.npmjs.org/-/v1/search?text=${term}&size=100`);
          const searchResults = searchResponse.data.objects.map(obj => obj.package.name);
          popularPackages.push(...searchResults);
        } catch (searchError) {
          console.warn(`⚠️ Ошибка поиска по термину ${term}:`, searchError.message);
        }
      }
      
      // Удаляем дубликаты
      const uniquePackages = [...new Set(popularPackages)];
      console.log(`📦 Fallback: будем собирать ${uniquePackages.length} популярных пакетов`);
      
      return uniquePackages;
      
    } catch (error) {
      console.error('❌ Ошибка получения популярных пакетов:', error.message);
      throw error;
    }
  }

  // Обработка пакетов батчами
  async processBatches(allPackages) {
    const totalBatches = Math.ceil(allPackages.length / this.batchSize);
    
    console.log(`🔄 Начинаем обработку ${totalBatches} батчей по ${this.batchSize} пакетов`);
    
    for (let i = 0; i < totalBatches; i++) {
      if (this.stats.shouldStop) {
        console.log('🛑 Остановка по запросу пользователя');
        break;
      }
      
      const startIndex = i * this.batchSize;
      const endIndex = Math.min(startIndex + this.batchSize, allPackages.length);
      const batch = allPackages.slice(startIndex, endIndex);
      
      console.log(`📦 Обработка батча ${i + 1}/${totalBatches} (пакеты ${startIndex + 1}-${endIndex})`);
      
      // Обрабатываем батч
      await this.processBatch(batch);
      
      // Обновляем статистику
      this.updateProgress();
      
      // Пауза между батчами (кроме последнего)
      if (i < totalBatches - 1 && !this.stats.shouldStop) {
        console.log(`⏳ Пауза ${this.delayBetweenBatches / 1000} секунд перед следующим батчем...`);
        await this.sleep(this.delayBetweenBatches);
      }
    }
  }

  // Обработка одного батча пакетов
  async processBatch(packageNames) {
    const promises = packageNames.map(packageName => this.processPackage(packageName));
    
    // Ждем завершения всех пакетов в батче
    const results = await Promise.allSettled(promises);
    
    // Подсчитываем результаты
    results.forEach((result, index) => {
      if (result.status === 'fulfilled') {
        this.stats.processedPackages++;
        console.log(`✅ ${packageNames[index]} - успешно обработан`);
      } else {
        this.stats.failedPackages++;
        console.log(`❌ ${packageNames[index]} - ошибка: ${result.reason?.message || 'Unknown error'}`);
      }
    });
  }

  // Обработка одного пакета
  async processPackage(packageName) {
    let retries = 0;
    
    while (retries < this.maxRetries) {
      try {
        // Проверяем, есть ли пакет уже в БД
        const existingPackage = await this.db.getPackageDetails(packageName);
        
        if (existingPackage && existingPackage.updated_at) {
          const lastUpdate = new Date(existingPackage.updated_at);
          const daysSinceUpdate = (Date.now() - lastUpdate.getTime()) / (1000 * 60 * 60 * 24);
          
          // Если пакет обновлялся менее 7 дней назад, пропускаем
          if (daysSinceUpdate < 7) {
            console.log(`⏭️ ${packageName} - пропускаем (обновлен ${daysSinceUpdate.toFixed(1)} дней назад)`);
            return;
          }
        }
        
        // Собираем пакет с полной информацией
        await this.enhancedCollector.collectPackageWithAudit(packageName);
        return; // Успешно обработан
        
      } catch (error) {
        retries++;
        console.warn(`⚠️ ${packageName} - попытка ${retries}/${this.maxRetries} неудачна: ${error.message}`);
        
        if (retries < this.maxRetries) {
          await this.sleep(1000 * retries); // Экспоненциальная задержка
        }
      }
    }
    
    throw new Error(`Не удалось обработать ${packageName} после ${this.maxRetries} попыток`);
  }

  // Обновление прогресса
  updateProgress() {
    this.stats.remainingPackages = this.stats.totalPackages - this.stats.processedPackages - this.stats.failedPackages;
    this.stats.progress = this.stats.totalPackages > 0 ? 
      ((this.stats.processedPackages + this.stats.failedPackages) / this.stats.totalPackages) * 100 : 0;
  }

  // Утилита для задержки
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Закрытие соединений
  async close() {
    if (this.db) {
      this.db.close();
    }
  }
}

module.exports = MassiveNpmCollector;
