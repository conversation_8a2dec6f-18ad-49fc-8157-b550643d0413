// Масштабный сборщик ВСЕХ npm пакетов с полным анализом зависимостей
const axios = require('axios');
const fs = require('fs');
const path = require('path');
const Database = require('../db/database');

class MassiveNpmCollector {
  constructor() {
    this.db = new Database();
    this.packagesFile = path.join(__dirname, '../data/all-npm-packages.json');
    this.progressFile = path.join(__dirname, '../data/collection-progress.json');
    this.batchSize = 50; // Пакетов в одной партии
    this.delayBetweenBatches = 2000; // 2 секунды между партиями
    this.maxRetries = 3;
    this.totalPackages = 0;
    this.processedPackages = 0;
    this.failedPackages = [];
    this.startTime = Date.now();
  }

  // Получение полного списка всех npm пакетов
  async getAllNpmPackages() {
    console.log('🌍 Получение полного списка всех npm пакетов...');
    
    try {
      // Проверяем кэшированный список
      if (fs.existsSync(this.packagesFile)) {
        const stats = fs.statSync(this.packagesFile);
        const ageHours = (Date.now() - stats.mtime.getTime()) / (1000 * 60 * 60);
        
        if (ageHours < 24) { // Используем кэш если младше 24 часов
          console.log('📦 Используем кэшированный список пакетов');
          const data = JSON.parse(fs.readFileSync(this.packagesFile, 'utf8'));
          return Object.keys(data).filter(name => !name.startsWith('_'));
        }
      }

      console.log('🔄 Загрузка свежего списка всех npm пакетов...');
      
      // Получаем полный список с npm registry
      const response = await axios.get('https://replicate.npmjs.com/_all_docs', {
        timeout: 120000, // 2 минуты
        params: {
          include_docs: false
        }
      });

      const allPackages = response.data.rows
        .map(row => row.id)
        .filter(name => !name.startsWith('_')); // Исключаем служебные документы

      console.log(`📊 Найдено ${allPackages.length.toLocaleString()} npm пакетов`);

      // Сохраняем список для кэширования
      const dataDir = path.dirname(this.packagesFile);
      if (!fs.existsSync(dataDir)) {
        fs.mkdirSync(dataDir, { recursive: true });
      }

      const packageData = {};
      allPackages.forEach(name => {
        packageData[name] = { cached: true };
      });

      fs.writeFileSync(this.packagesFile, JSON.stringify(packageData, null, 2));
      console.log('💾 Список пакетов сохранен в кэш');

      return allPackages;

    } catch (error) {
      console.error('❌ Ошибка получения списка пакетов:', error.message);
      
      // Fallback: используем кэшированный список если есть
      if (fs.existsSync(this.packagesFile)) {
        console.log('🔄 Используем старый кэшированный список');
        const data = JSON.parse(fs.readFileSync(this.packagesFile, 'utf8'));
        return Object.keys(data).filter(name => !name.startsWith('_'));
      }
      
      throw error;
    }
  }

  // Получение детальной информации о пакете
  async getPackageDetails(packageName, retryCount = 0) {
    try {
      const response = await axios.get(`https://registry.npmjs.org/${packageName}`, {
        timeout: 15000
      });

      const data = response.data;
      
      // Извлекаем максимум информации
      const packageInfo = {
        name: packageName,
        description: data.description || '',
        repository: data.repository?.url || data.repository || '',
        homepage: data.homepage || '',
        license: typeof data.license === 'string' ? data.license : data.license?.type || '',
        latest_version: data['dist-tags']?.latest || '',
        author: typeof data.author === 'string' ? data.author : data.author?.name || '',
        
        // Расширенная информация
        maintainers: data.maintainers || [],
        contributors: data.contributors || [],
        keywords: data.keywords || [],
        readme: data.readme || '',
        
        // Ссылки
        bugs_url: data.bugs?.url || data.bugs || '',
        npm_url: `https://www.npmjs.com/package/${packageName}`,
        
        // Техническая информация
        engines: data.engines || {},
        os_compatibility: data.os || [],
        cpu_compatibility: data.cpu || [],
        funding: data.funding || {},
        
        // Временные метки
        first_release_date: data.time?.created || '',
        last_release_date: data.time?.modified || '',
        
        // Дополнительные поля
        users: data.users ? Object.keys(data.users).length : 0,
        dist_tags: data['dist-tags'] || {},
        
        versions: []
      };

      // Собираем информацию о всех версиях
      const versionKeys = Object.keys(data.versions || {});
      packageInfo.version_count = versionKeys.length;

      let totalDeps = 0;
      let totalDevDeps = 0;
      let totalPeerDeps = 0;
      let totalOptionalDeps = 0;

      // Обрабатываем все версии для полного анализа зависимостей
      for (const versionKey of versionKeys) {
        const versionData = data.versions[versionKey];
        
        const deps = versionData.dependencies || {};
        const devDeps = versionData.devDependencies || {};
        const peerDeps = versionData.peerDependencies || {};
        const optionalDeps = versionData.optionalDependencies || {};
        
        totalDeps += Object.keys(deps).length;
        totalDevDeps += Object.keys(devDeps).length;
        totalPeerDeps += Object.keys(peerDeps).length;
        totalOptionalDeps += Object.keys(optionalDeps).length;
        
        // Получаем размеры из последней версии
        if (versionKey === packageInfo.latest_version && versionData.dist) {
          packageInfo.unpacked_size = versionData.dist.unpackedSize || 0;
          packageInfo.file_count = versionData.dist.fileCount || 0;
        }
        
        packageInfo.versions.push({
          version: versionKey,
          dependencies: deps,
          devDependencies: devDeps,
          peerDependencies: peerDeps,
          optionalDependencies: optionalDeps,
          published_at: data.time?.[versionKey] || new Date().toISOString(),
          deprecated: versionData.deprecated || false,
          engines: versionData.engines || {},
          os: versionData.os || [],
          cpu: versionData.cpu || [],
          main: versionData.main || '',
          module: versionData.module || '',
          types: versionData.types || versionData.typings || '',
          scripts: versionData.scripts || {}
        });
      }
      
      // Вычисляем средние значения зависимостей
      packageInfo.dependency_count = Math.round(totalDeps / Math.max(versionKeys.length, 1));
      packageInfo.dev_dependency_count = Math.round(totalDevDeps / Math.max(versionKeys.length, 1));
      packageInfo.peer_dependency_count = Math.round(totalPeerDeps / Math.max(versionKeys.length, 1));
      packageInfo.optional_dependency_count = Math.round(totalOptionalDeps / Math.max(versionKeys.length, 1));

      return packageInfo;

    } catch (error) {
      if (retryCount < this.maxRetries) {
        console.log(`⚠️ Повтор для ${packageName} (попытка ${retryCount + 1}/${this.maxRetries})`);
        await new Promise(resolve => setTimeout(resolve, 1000 * (retryCount + 1)));
        return this.getPackageDetails(packageName, retryCount + 1);
      }
      
      console.error(`❌ Ошибка получения данных для ${packageName}:`, error.message);
      this.failedPackages.push({ name: packageName, error: error.message });
      return null;
    }
  }

  // Получение расширенных метрик (загрузки, GitHub)
  async getExtendedMetrics(packageInfo) {
    const promises = [];

    // Статистика загрузок
    promises.push(
      this.getDownloadStats(packageInfo.name).catch(error => {
        console.log(`⚠️ Ошибка загрузок для ${packageInfo.name}: ${error.message}`);
        return {};
      })
    );

    // GitHub метрики
    if (packageInfo.repository && packageInfo.repository.includes('github.com')) {
      promises.push(
        this.getGitHubMetrics(packageInfo.repository, packageInfo.name).catch(error => {
          console.log(`⚠️ Ошибка GitHub для ${packageInfo.name}: ${error.message}`);
          return {};
        })
      );
    } else {
      promises.push(Promise.resolve({}));
    }

    const [downloadStats, githubStats] = await Promise.allSettled(promises);

    // Объединяем метрики
    Object.assign(packageInfo, 
      downloadStats.status === 'fulfilled' ? downloadStats.value : {},
      githubStats.status === 'fulfilled' ? githubStats.value : {}
    );

    return packageInfo;
  }

  // Получение статистики загрузок
  async getDownloadStats(packageName) {
    const downloadPromises = [
      axios.get(`https://api.npmjs.org/downloads/point/last-week/${packageName}`, { timeout: 5000 }),
      axios.get(`https://api.npmjs.org/downloads/point/last-month/${packageName}`, { timeout: 5000 }),
      axios.get(`https://api.npmjs.org/downloads/point/last-year/${packageName}`, { timeout: 5000 })
    ];

    const results = await Promise.allSettled(downloadPromises);
    const stats = {};

    if (results[0].status === 'fulfilled' && results[0].value.data?.downloads) {
      stats.weekly_downloads = results[0].value.data.downloads;
      stats.download_count = results[0].value.data.downloads;
    }

    if (results[1].status === 'fulfilled' && results[1].value.data?.downloads) {
      stats.monthly_downloads = results[1].value.data.downloads;
    }

    if (results[2].status === 'fulfilled' && results[2].value.data?.downloads) {
      stats.yearly_downloads = results[2].value.data.downloads;
    }

    return stats;
  }

  // Получение GitHub метрик
  async getGitHubMetrics(repoUrl, packageName) {
    const repoMatch = repoUrl.match(/github\.com\/([^\/]+)\/([^\/]+)/);
    if (!repoMatch) return {};

    const [, owner, repo] = repoMatch;
    const cleanRepo = repo.replace(/\.git$/, '');

    try {
      const response = await axios.get(`https://api.github.com/repos/${owner}/${cleanRepo}`, {
        timeout: 10000,
        headers: { 'User-Agent': 'Package-Analyzer/1.0' }
      });

      const data = response.data;
      return {
        github_stars: data.stargazers_count || 0,
        github_forks: data.forks_count || 0,
        github_issues: data.open_issues_count || 0,
        github_watchers: data.watchers_count || 0,
        last_commit_date: data.pushed_at || ''
      };
    } catch (error) {
      return {};
    }
  }

  // Вычисление оценок качества
  calculateQualityScores(packageInfo) {
    // Проверяем TypeScript
    const latestVersion = packageInfo.versions[packageInfo.versions.length - 1];
    if (latestVersion) {
      packageInfo.has_typescript = !!(
        latestVersion.types || 
        latestVersion.typings ||
        (latestVersion.devDependencies && latestVersion.devDependencies.typescript) ||
        (latestVersion.dependencies && latestVersion.dependencies.typescript) ||
        (packageInfo.keywords && packageInfo.keywords.includes('typescript'))
      );

      // Проверяем тесты
      const testFrameworks = ['jest', 'mocha', 'jasmine', 'ava', 'tape', 'vitest'];
      packageInfo.has_tests = testFrameworks.some(framework =>
        Object.keys(latestVersion.devDependencies || {}).some(dep => dep.includes(framework))
      );
    }

    // Вычисляем оценки
    packageInfo.popularity_score = Math.min((packageInfo.weekly_downloads || 0) / 100000, 1);
    packageInfo.quality_score = (
      (packageInfo.has_typescript ? 0.2 : 0) +
      (packageInfo.has_tests ? 0.3 : 0) +
      (packageInfo.readme ? 0.2 : 0) +
      (packageInfo.license ? 0.1 : 0) +
      (packageInfo.repository ? 0.2 : 0)
    );

    // Оценка поддержки
    if (packageInfo.last_commit_date) {
      const daysSinceLastCommit = (Date.now() - new Date(packageInfo.last_commit_date)) / (1000 * 60 * 60 * 24);
      packageInfo.maintenance_score = Math.max(1 - (daysSinceLastCommit / 365), 0);
    }

    // Частота релизов
    if (packageInfo.versions.length > 1) {
      const firstDate = new Date(packageInfo.first_release_date);
      const lastDate = new Date(packageInfo.last_release_date);
      const monthsDiff = (lastDate - firstDate) / (1000 * 60 * 60 * 24 * 30);
      packageInfo.release_frequency = monthsDiff > 0 ? packageInfo.versions.length / monthsDiff : 0;
    }

    return packageInfo;
  }

  // Сохранение прогресса
  saveProgress() {
    const progress = {
      totalPackages: this.totalPackages,
      processedPackages: this.processedPackages,
      failedPackages: this.failedPackages.length,
      startTime: this.startTime,
      lastUpdate: Date.now(),
      failedList: this.failedPackages.slice(-100) // Последние 100 ошибок
    };

    fs.writeFileSync(this.progressFile, JSON.stringify(progress, null, 2));
  }

  // Загрузка прогресса
  loadProgress() {
    if (fs.existsSync(this.progressFile)) {
      const progress = JSON.parse(fs.readFileSync(this.progressFile, 'utf8'));
      this.processedPackages = progress.processedPackages || 0;
      this.failedPackages = progress.failedList || [];
      console.log(`📊 Загружен прогресс: ${this.processedPackages} пакетов обработано`);
    }
  }

  // Основная функция сбора
  async startMassiveCollection() {
    console.log('🚀 Запуск масштабного сбора ВСЕХ npm пакетов...');
    
    try {
      // Получаем список всех пакетов
      const allPackages = await this.getAllNpmPackages();
      this.totalPackages = allPackages.length;
      
      console.log(`📦 Всего пакетов для обработки: ${this.totalPackages.toLocaleString()}`);
      
      // Загружаем прогресс
      this.loadProgress();
      
      // Определяем с какого пакета продолжить
      const startIndex = this.processedPackages;
      const remainingPackages = allPackages.slice(startIndex);
      
      console.log(`🔄 Продолжаем с пакета ${startIndex + 1}, осталось: ${remainingPackages.length.toLocaleString()}`);
      
      // Обрабатываем пакеты партиями
      for (let i = 0; i < remainingPackages.length; i += this.batchSize) {
        const batch = remainingPackages.slice(i, i + this.batchSize);
        
        console.log(`\n📦 Обработка партии ${Math.floor(i / this.batchSize) + 1}: пакеты ${startIndex + i + 1}-${startIndex + i + batch.length}`);
        
        await this.processBatch(batch);
        
        // Сохраняем прогресс
        this.saveProgress();
        
        // Показываем статистику
        this.showProgress();
        
        // Пауза между партиями
        if (i + this.batchSize < remainingPackages.length) {
          console.log(`⏸️ Пауза ${this.delayBetweenBatches}мс между партиями...`);
          await new Promise(resolve => setTimeout(resolve, this.delayBetweenBatches));
        }
      }
      
      console.log('\n🎉 Масштабный сбор завершен!');
      this.showFinalStats();
      
    } catch (error) {
      console.error('💥 Критическая ошибка:', error);
      this.saveProgress();
    }
  }

  // Обработка партии пакетов
  async processBatch(batch) {
    const promises = batch.map(async (packageName) => {
      try {
        // Проверяем, не обработан ли уже пакет
        const existing = await this.db.getPackageDetails(packageName);
        if (existing && existing.versions && existing.versions.length > 0) {
          console.log(`⏭️ Пропускаем ${packageName} (уже в БД)`);
          this.processedPackages++;
          return;
        }

        // Получаем детальную информацию
        let packageInfo = await this.getPackageDetails(packageName);
        if (!packageInfo) return;

        // Получаем расширенные метрики
        packageInfo = await this.getExtendedMetrics(packageInfo);

        // Вычисляем оценки качества
        packageInfo = this.calculateQualityScores(packageInfo);

        // Сохраняем в базу данных
        await this.db.updatePackageInfo(packageName, packageInfo);

        console.log(`✅ ${packageName} (${packageInfo.versions.length} версий, ${packageInfo.dependency_count} зависимостей)`);
        this.processedPackages++;

      } catch (error) {
        console.error(`❌ Ошибка обработки ${packageName}:`, error.message);
        this.failedPackages.push({ name: packageName, error: error.message });
      }
    });

    await Promise.allSettled(promises);
  }

  // Показать прогресс
  showProgress() {
    const elapsed = Date.now() - this.startTime;
    const rate = this.processedPackages / (elapsed / 1000 / 60); // пакетов в минуту
    const remaining = this.totalPackages - this.processedPackages;
    const eta = remaining / rate; // минут до завершения

    console.log(`\n📊 ПРОГРЕСС:`);
    console.log(`   ✅ Обработано: ${this.processedPackages.toLocaleString()}/${this.totalPackages.toLocaleString()} (${((this.processedPackages / this.totalPackages) * 100).toFixed(2)}%)`);
    console.log(`   ❌ Ошибок: ${this.failedPackages.length.toLocaleString()}`);
    console.log(`   ⚡ Скорость: ${rate.toFixed(1)} пакетов/мин`);
    console.log(`   ⏱️ ETA: ${eta.toFixed(0)} минут`);
  }

  // Финальная статистика
  showFinalStats() {
    const elapsed = (Date.now() - this.startTime) / 1000 / 60; // минуты
    const rate = this.processedPackages / elapsed;

    console.log(`\n🎯 ФИНАЛЬНАЯ СТАТИСТИКА:`);
    console.log(`   📦 Всего пакетов: ${this.totalPackages.toLocaleString()}`);
    console.log(`   ✅ Успешно обработано: ${this.processedPackages.toLocaleString()}`);
    console.log(`   ❌ Ошибок: ${this.failedPackages.length.toLocaleString()}`);
    console.log(`   ⏱️ Время выполнения: ${elapsed.toFixed(1)} минут`);
    console.log(`   ⚡ Средняя скорость: ${rate.toFixed(1)} пакетов/мин`);
    console.log(`   📊 Успешность: ${((this.processedPackages / this.totalPackages) * 100).toFixed(2)}%`);
  }

  // Закрытие соединений
  async close() {
    await this.db.close();
  }
}

module.exports = MassiveNpmCollector;

// Запуск если файл выполняется напрямую
if (require.main === module) {
  const collector = new MassiveNpmCollector();
  
  collector.startMassiveCollection()
    .then(() => {
      console.log('🎉 Сбор завершен успешно!');
      process.exit(0);
    })
    .catch(error => {
      console.error('💥 Критическая ошибка:', error);
      process.exit(1);
    })
    .finally(() => {
      collector.close();
    });
}
