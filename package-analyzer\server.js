console.log('📦 Загружаем модули...');

const express = require('express');
console.log('✅ Express загружен');

const http = require('http');
console.log('✅ HTTP загружен');

const WebSocket = require('ws');
console.log('✅ WebSocket загружен');

const path = require('path');
const fs = require('fs');
const os = require('os');
console.log('✅ Системные модули загружены');

const PackageAnalyzer = require('./index');
console.log('✅ PackageAnalyzer загружен');

const Database = require('./db/database');
console.log('✅ Database загружен');

const GPUDataProcessor = require('./gpu/gpu-data-processor');
console.log('✅ GPUDataProcessor загружен');

const GPUFileAnalyzer = require('./gpu/gpu-file-analyzer');
console.log('✅ GPUFileAnalyzer загружен');

console.log('🏗️ Создаем сервер...');
const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });
console.log('✅ Сервер создан');

const analyzer = new PackageAnalyzer();
const db = new Database();
const gpuDataProcessor = new GPUDataProcessor();
const gpuFileAnalyzer = new GPUFileAnalyzer();

// Статистика системы
let systemStats = {
  startTime: Date.now(),
  totalPackages: 0,
  todayAnalyses: 0,
  conflictsFound: 0,
  conflictsResolved: 0,
  cpuUsage: 0,
  memoryUsage: 0,
  analysisSpeed: 0
};

// Очередь задач
let taskQueue = [];
let taskIdCounter = 1;

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.static(path.join(__dirname, 'public')));

// Инициализация базы данных
async function initializeDatabase() {
  try {
    await db.initialize();
    console.log('База данных инициализирована');
    broadcastLog('success', 'База данных инициализирована');
  } catch (error) {
    console.error('Ошибка инициализации базы данных:', error);
    broadcastLog('error', 'Ошибка инициализации базы данных: ' + error.message);
  }
}

// WebSocket соединения
const clients = new Set();

wss.on('connection', (ws) => {
  clients.add(ws);
  console.log('Новое WebSocket соединение');

  // Отправляем текущую статистику новому клиенту
  ws.send(JSON.stringify({
    type: 'stats',
    stats: systemStats
  }));

  ws.on('close', () => {
    clients.delete(ws);
    console.log('WebSocket соединение закрыто');
  });

  ws.on('error', (error) => {
    console.error('WebSocket ошибка:', error);
    clients.delete(ws);
  });
});

// Функция для отправки сообщений всем клиентам
function broadcast(data) {
  const message = JSON.stringify(data);
  clients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(message);
    }
  });
}

// Функция для отправки логов
function broadcastLog(level, message) {
  broadcast({
    type: 'log',
    level: level,
    message: message
  });
}

// Функция для обновления статистики
function broadcastStats() {
  broadcast({
    type: 'stats',
    stats: systemStats
  });
}

// API Endpoints
app.get('/api/package/:name', async (req, res) => {
  try {
    const packageInfo = await analyzer.getPackageInfo(req.params.name);
    res.json(packageInfo);
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
});

app.post('/api/analyze', async (req, res) => {
  try {
    let analysis;

    if (req.body.content) {
      // Анализ содержимого файла
      const tempFilePath = path.join(__dirname, 'temp', `package-${Date.now()}.json`);

      // Создаем временную директорию если её нет
      const tempDir = path.dirname(tempFilePath);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // Записываем временный файл
      fs.writeFileSync(tempFilePath, req.body.content);

      try {
        analysis = await analyzer.analyzePackage(tempFilePath);
        systemStats.todayAnalyses++;

        if (analysis.potentialIssues && analysis.potentialIssues.length > 0) {
          systemStats.conflictsFound += analysis.potentialIssues.length;
        }

        broadcastLog('info', `Анализ завершен для ${req.body.filePath || 'файла'}`);
        broadcastStats();

      } finally {
        // Удаляем временный файл
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath);
        }
      }
    } else {
      analysis = await analyzer.analyzePackage(req.body.filePath);
    }

    res.json(analysis);
  } catch (err) {
    broadcastLog('error', 'Ошибка анализа: ' + err.message);
    res.status(500).json({ error: err.message });
  }
});

// Получение статистики системы
app.get('/api/stats', async (req, res) => {
  const uptime = Math.floor((Date.now() - systemStats.startTime) / 1000);

  // Получаем статистику из базы данных
  const dbStats = await getDatabaseStats();

  res.json({
    ...systemStats,
    ...dbStats,
    uptime: uptime,
    serverStatus: 'online',
    dbStatus: 'connected',
    gpuStatus: analyzer.gpuAccelerator.getGPUStatus()
  });
});

// Получение всех пакетов из базы данных
app.get('/api/packages', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const search = req.query.search || '';
    const offset = (page - 1) * limit;

    const result = await db.getPackagesList(search, limit, offset);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Получение детальной информации о пакете
app.get('/api/packages/:name', async (req, res) => {
  try {
    const packageName = req.params.name;
    const packageInfo = await db.getPackageDetails(packageName);

    if (!packageInfo) {
      return res.status(404).json({ error: 'Пакет не найден' });
    }

    res.json(packageInfo);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Обновление информации о пакете
app.put('/api/packages/:name', async (req, res) => {
  try {
    const packageName = req.params.name;
    const updateData = req.body;

    await updatePackageInDB(packageName, updateData);
    broadcastLog('success', `Пакет ${packageName} обновлен`);

    res.json({ success: true });
  } catch (error) {
    broadcastLog('error', `Ошибка обновления пакета ${req.params.name}: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// Запуск полного сканирования
app.post('/api/full-scan', async (req, res) => {
  try {
    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: 'Полное сканирование npm пакетов',
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем сканирование в фоне
    setImmediate(async () => {
      try {
        task.status = 'running';
        broadcastTaskUpdate(task);
        broadcastLog('info', 'Начато полное сканирование npm пакетов');

        const fullScan = require('./scripts/full-scan');
        await fullScan.startFullScan();

        task.status = 'completed';
        task.endTime = Date.now();
        broadcastTaskUpdate(task);
        broadcastLog('success', 'Полное сканирование завершено успешно');

      } catch (error) {
        task.status = 'failed';
        task.error = error.message;
        task.endTime = Date.now();
        broadcastTaskUpdate(task);
        broadcastLog('error', 'Ошибка при полном сканировании: ' + error.message);
      }
    });

    res.json({ success: true, taskId: taskId });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Получение очереди задач
app.get('/api/tasks', (req, res) => {
  res.json(taskQueue);
});

// GPU-ускоренный анализ пакета с использованием базы данных
app.post('/api/analyze', async (req, res) => {
  try {
    const { filePath, content } = req.body;

    // Парсим package.json
    let packageJson;
    try {
      packageJson = JSON.parse(content);
    } catch (parseError) {
      return res.status(400).json({ error: 'Некорректный JSON файл' });
    }

    broadcastLog('info', `🚀 Начинаем GPU-ускоренный анализ файла: ${filePath}`);

    // Получаем пакеты из базы данных для GPU анализа
    const allPackages = await db.getPackagesList('', 1000, 0);
    const databasePackages = allPackages.packages;

    // 🎮 GPU анализ package.json
    broadcastLog('info', '🎮 Запуск GPU анализа зависимостей');
    const gpuAnalysisResult = await gpuFileAnalyzer.analyzePackageJson(packageJson, databasePackages);

    // Анализируем зависимости с использованием базы данных (CPU fallback)
    const dbAnalysisResult = await db.analyzeDependencies(
      packageJson.dependencies || {},
      packageJson.devDependencies || {}
    );

    // 🎮 GPU поиск циклических зависимостей
    const dependencyGraph = {};
    Object.keys(packageJson.dependencies || {}).forEach(dep => {
      const dbPackage = databasePackages.find(p => p.name === dep);
      if (dbPackage && dbPackage.versions && dbPackage.versions[0]) {
        dependencyGraph[dep] = Object.keys(dbPackage.versions[0].dependencies || {});
      }
    });

    broadcastLog('info', '🎮 Запуск GPU поиска циклических зависимостей');
    const gpuCycleResult = await gpuDataProcessor.detectCyclicDependencies(dependencyGraph);

    // 🎮 GPU анализ совместимости версий
    broadcastLog('info', '🎮 Запуск GPU анализа совместимости версий');
    const gpuCompatibilityResult = await gpuDataProcessor.analyzeVersionCompatibility(
      packageJson.dependencies || {},
      databasePackages
    );

    // Дополнительный анализ с помощью анализатора
    const analyzerResult = await analyzer.analyzePackage(filePath, content);

    // Объединяем результаты
    const combinedResult = {
      name: packageJson.name,
      version: packageJson.version,
      description: packageJson.description,
      filePath: filePath,

      // Статистика зависимостей
      dependencies: {
        total: dbAnalysisResult.totalDependencies,
        known: dbAnalysisResult.knownPackages.length,
        unknown: dbAnalysisResult.unknownPackages.length,
        production: Object.keys(packageJson.dependencies || {}).length,
        development: Object.keys(packageJson.devDependencies || {}).length
      },

      // Детальная информация о пакетах
      knownPackages: dbAnalysisResult.knownPackages,
      unknownPackages: dbAnalysisResult.unknownPackages,

      // GPU результаты
      gpuAnalysis: {
        dependencies: gpuAnalysisResult.dependencies || [],
        conflicts: gpuAnalysisResult.conflicts || [],
        cyclicDependencies: gpuCycleResult.cycles || [],
        compatibility: gpuCompatibilityResult
      },

      // Проблемы и рекомендации
      potentialIssues: dbAnalysisResult.potentialIssues,
      recommendations: dbAnalysisResult.recommendations,

      // Анализ от анализатора
      conflicts: analyzerResult.conflicts || [],
      cyclicDependencies: analyzerResult.cyclicDependencies || [],

      // Сводка анализа
      analysis: {
        ...dbAnalysisResult.analysis,
        totalIssues: dbAnalysisResult.potentialIssues.length + (analyzerResult.conflicts?.length || 0),
        riskLevel: calculateRiskLevel(dbAnalysisResult, analyzerResult),
        gpuAccelerated: true,
        processingMetrics: {
          gpuAnalysisTime: gpuAnalysisResult.metrics?.processingTime || 0,
          gpuCycleTime: gpuCycleResult.metrics?.processingTime || 0,
          gpuCompatibilityTime: gpuCompatibilityResult.metrics?.processingTime || 0
        }
      }
    };

    // Обновляем статистику
    systemStats.todayAnalyses++;
    if (combinedResult.conflicts.length > 0) {
      systemStats.conflictsFound += combinedResult.conflicts.length;
    }

    // Обновляем скорость анализа на основе GPU метрик
    const totalGpuTime = (gpuAnalysisResult.metrics?.processingTime || 0) +
                        (gpuCycleResult.metrics?.processingTime || 0) +
                        (gpuCompatibilityResult.metrics?.processingTime || 0);

    if (totalGpuTime > 0) {
      systemStats.analysisSpeed = 1000 / totalGpuTime; // анализов в секунду
    }

    broadcastStats();

    const gpuInfo = totalGpuTime > 0 ?
      `с GPU ускорением (${totalGpuTime}мс)` :
      'с CPU fallback';

    broadcastLog('success', `🎮 Анализ завершен для ${filePath} ${gpuInfo}. Найдено ${combinedResult.analysis.totalIssues} проблем`);

    res.json(combinedResult);
  } catch (err) {
    broadcastLog('error', 'Ошибка анализа: ' + err.message);
    res.status(500).json({ error: err.message });
  }
});

// Функция для расчета уровня риска
function calculateRiskLevel(dbAnalysis, analyzerResult) {
  let riskScore = 0;

  // Уязвимости безопасности
  riskScore += dbAnalysis.analysis.securityIssues * 3;

  // Неизвестные пакеты
  riskScore += dbAnalysis.analysis.unknownPackages * 2;

  // Конфликты версий
  riskScore += (analyzerResult.conflicts?.length || 0) * 2;

  // Циклические зависимости
  riskScore += (analyzerResult.cyclicDependencies?.length || 0) * 1;

  if (riskScore === 0) return 'low';
  if (riskScore <= 5) return 'medium';
  return 'high';
}

// Разрешение конфликтов
app.post('/api/resolve', async (req, res) => {
  try {
    const result = await analyzer.resolveConflicts(req.body.filePath);

    if (result.resolved) {
      systemStats.conflictsResolved++;
      broadcastStats();
      broadcastLog('success', 'Конфликты разрешены для ' + req.body.filePath);
    }

    res.json(result);
  } catch (err) {
    broadcastLog('error', 'Ошибка разрешения конфликтов: ' + err.message);
    res.status(500).json({ error: err.message });
  }
});

// ========== API ENDPOINTS ДЛЯ СБОРА ДАННЫХ ==========

// Автоматический сбор популярных пакетов
app.post('/api/collect/auto', async (req, res) => {
  try {
    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: 'Автоматический сбор пакетов',
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем сбор в фоне
    setImmediate(async () => {
      await runPackageCollection(task, 'auto');
    });

    res.json({ success: true, taskId: taskId, count: 100 });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Сбор конкретного пакета
app.post('/api/collect/package', async (req, res) => {
  try {
    const { packageName } = req.body;

    if (!packageName) {
      return res.status(400).json({ error: 'Название пакета обязательно' });
    }

    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: `Сбор пакета: ${packageName}`,
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем сбор в фоне
    setImmediate(async () => {
      await runSinglePackageCollection(task, packageName);
    });

    res.json({ success: true, taskId: taskId, packageName: packageName });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Сбор списка пакетов
app.post('/api/collect/packages', async (req, res) => {
  try {
    const { packages } = req.body;

    if (!packages || !Array.isArray(packages)) {
      return res.status(400).json({ error: 'Список пакетов обязателен' });
    }

    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: `Сбор ${packages.length} пакетов`,
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем сбор в фоне
    setImmediate(async () => {
      await runMultiplePackageCollection(task, packages);
    });

    res.json({ success: true, taskId: taskId, count: packages.length });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Сбор популярных пакетов
app.post('/api/collect/popular', async (req, res) => {
  try {
    const { limit = 1000 } = req.body;

    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: `Сбор топ-${limit} популярных пакетов`,
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем сбор в фоне
    setImmediate(async () => {
      await runPopularPackageCollection(task, limit);
    });

    res.json({ success: true, taskId: taskId, count: limit });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Сбор по категориям
app.post('/api/collect/categories', async (req, res) => {
  try {
    const { categories } = req.body;

    if (!categories || !Array.isArray(categories)) {
      return res.status(400).json({ error: 'Список категорий обязателен' });
    }

    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: `Сбор по ${categories.length} категориям`,
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем сбор в фоне
    setImmediate(async () => {
      await runCategoryCollection(task, categories);
    });

    res.json({ success: true, taskId: taskId, categories: categories.length });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Обновление существующих пакетов
app.post('/api/collect/update', async (req, res) => {
  try {
    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: 'Обновление существующих пакетов',
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем обновление в фоне
    setImmediate(async () => {
      await runPackageUpdate(task);
    });

    res.json({ success: true, taskId: taskId });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Сбор отдельного пакета с GPU ускорением
app.post('/api/collect-package', async (req, res) => {
  try {
    const { packageName } = req.body;

    if (!packageName) {
      return res.status(400).json({ error: 'Не указано имя пакета' });
    }

    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: `GPU сбор пакета: ${packageName}`,
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем сбор в фоне
    setImmediate(async () => {
      await runSinglePackageCollection(task, packageName);
    });

    res.json({
      success: true,
      taskId: taskId,
      packageName: packageName,
      gpuAccelerated: true
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Остановка всех задач
app.post('/api/tasks/stop', async (req, res) => {
  try {
    // Помечаем все активные задачи как остановленные
    taskQueue.forEach(task => {
      if (task.status === 'running' || task.status === 'pending') {
        task.status = 'stopped';
        task.endTime = Date.now();
        broadcastTaskUpdate(task);
      }
    });

    broadcastLog('warning', 'Все задачи остановлены пользователем');
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Функция для отправки обновлений задач
function broadcastTaskUpdate(task) {
  broadcast({
    type: 'task',
    task: task
  });
}

// Мониторинг системных ресурсов
function updateSystemMetrics() {
  const memUsage = process.memoryUsage();
  const totalMem = os.totalmem();
  const freeMem = os.freemem();

  systemStats.memoryUsage = ((totalMem - freeMem) / totalMem) * 100;

  // Простая эмуляция CPU usage (в реальном приложении используйте библиотеку типа pidusage)
  systemStats.cpuUsage = Math.random() * 20 + 10; // 10-30%

  // Обновляем скорость анализа (пакетов в секунду)
  systemStats.analysisSpeed = Math.random() * 5 + 1; // 1-6 пак/сек

  broadcastStats();
}

// Функции для работы с базой данных
async function getDatabaseStats() {
  return new Promise((resolve, reject) => {
    db.db.get('SELECT COUNT(*) as total FROM packages', (err, row) => {
      if (err) {
        console.error('Ошибка получения статистики пакетов:', err);
        resolve({ totalPackages: 0 });
      } else {
        resolve({ totalPackages: row.total });
      }
    });
  });
}

async function getPackagesFromDB(search, limit, offset) {
  return new Promise((resolve, reject) => {
    let query = 'SELECT * FROM packages';
    let params = [];

    if (search) {
      query += ' WHERE name LIKE ? OR description LIKE ?';
      params = [`%${search}%`, `%${search}%`];
    }

    query += ' ORDER BY name LIMIT ? OFFSET ?';
    params.push(limit, offset);

    db.db.all(query, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

async function getPackagesCount(search) {
  return new Promise((resolve, reject) => {
    let query = 'SELECT COUNT(*) as total FROM packages';
    let params = [];

    if (search) {
      query += ' WHERE name LIKE ? OR description LIKE ?';
      params = [`%${search}%`, `%${search}%`];
    }

    db.db.get(query, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row.total);
      }
    });
  });
}

async function getPackageDetails(packageName) {
  return new Promise((resolve, reject) => {
    // Получаем основную информацию о пакете
    db.db.get('SELECT * FROM packages WHERE name = ?', [packageName], (err, packageRow) => {
      if (err) {
        reject(err);
        return;
      }

      if (!packageRow) {
        resolve(null);
        return;
      }

      // Получаем все версии пакета
      db.db.all('SELECT * FROM versions WHERE package_name = ? ORDER BY version DESC',
        [packageName], (err, versionRows) => {
        if (err) {
          reject(err);
        } else {
          resolve({
            ...packageRow,
            versions: versionRows.map(v => ({
              version: v.version,
              dependencies: JSON.parse(v.dependencies || '{}'),
              published_at: v.published_at
            }))
          });
        }
      });
    });
  });
}

async function updatePackageInDB(packageName, updateData) {
  return new Promise((resolve, reject) => {
    const fields = [];
    const values = [];

    if (updateData.description !== undefined) {
      fields.push('description = ?');
      values.push(updateData.description);
    }
    if (updateData.repository !== undefined) {
      fields.push('repository = ?');
      values.push(updateData.repository);
    }
    if (updateData.homepage !== undefined) {
      fields.push('homepage = ?');
      values.push(updateData.homepage);
    }
    if (updateData.license !== undefined) {
      fields.push('license = ?');
      values.push(updateData.license);
    }
    if (updateData.latest_version !== undefined) {
      fields.push('latest_version = ?');
      values.push(updateData.latest_version);
    }

    fields.push('updated_at = datetime("now")');
    values.push(packageName);

    const query = `UPDATE packages SET ${fields.join(', ')} WHERE name = ?`;

    db.db.run(query, values, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve(this.changes);
      }
    });
  });
}

// ========== ФУНКЦИИ ВЫПОЛНЕНИЯ ЗАДАЧ СБОРА ==========

// Сбор одного пакета с GPU ускорением
async function runSinglePackageCollection(task, packageName) {
  try {
    task.status = 'running';
    broadcastTaskUpdate(task);
    broadcastLog('info', `🚀 GPU-ускоренный сбор пакета: ${packageName}`);

    // Получаем информацию о пакете из npm registry
    const axios = require('axios');
    const response = await axios.get(`https://registry.npmjs.org/${packageName}`, {
      timeout: 15000
    });

    const packageData = response.data;

    // Извлекаем детальную информацию
    const packageInfo = {
      description: packageData.description,
      repository: packageData.repository?.url || packageData.repository,
      homepage: packageData.homepage,
      license: typeof packageData.license === 'string' ? packageData.license : packageData.license?.type,
      latest_version: packageData['dist-tags']?.latest,
      author: typeof packageData.author === 'string' ? packageData.author : packageData.author?.name,
      keywords: packageData.keywords || [],
      versions: []
    };

    // Собираем информацию о версиях (последние 40 для GPU обработки)
    const versionKeys = Object.keys(packageData.versions || {}).slice(-40);

    for (const versionKey of versionKeys) {
      const versionData = packageData.versions[versionKey];
      packageInfo.versions.push({
        version: versionKey,
        dependencies: versionData.dependencies || {},
        devDependencies: versionData.devDependencies || {},
        peerDependencies: versionData.peerDependencies || {},
        optionalDependencies: versionData.optionalDependencies || {},
        published_at: packageData.time?.[versionKey] || new Date().toISOString(),
        deprecated: versionData.deprecated || false
      });
    }

    // 🎮 GPU обработка версий пакета
    broadcastLog('info', `🎮 Запуск GPU обработки ${packageInfo.versions.length} версий`);
    const gpuResult = await gpuDataProcessor.processPackageVersions({
      name: packageName,
      versions: packageInfo.versions
    });

    if (gpuResult.processedVersions) {
      packageInfo.versions = gpuResult.processedVersions;
      broadcastLog('success', `🎮 GPU обработка завершена за ${gpuResult.metrics.processingTime}мс`);
    }

    // Получаем статистику загрузок (если доступно)
    try {
      const downloadsResponse = await axios.get(`https://api.npmjs.org/downloads/point/last-week/${packageName}`, {
        timeout: 5000
      });
      if (downloadsResponse.data && downloadsResponse.data.downloads) {
        packageInfo.download_count = downloadsResponse.data.downloads;
      }
    } catch (downloadError) {
      // Игнорируем ошибки получения статистики загрузок
      broadcastLog('warning', `Не удалось получить статистику загрузок для ${packageName}`);
    }

    // Сохраняем в базу данных
    await db.updatePackageInfo(packageName, packageInfo);

    task.status = 'completed';
    task.endTime = Date.now();
    broadcastTaskUpdate(task);

    const processingInfo = gpuResult.metrics.gpuAccelerated ?
      `с GPU ускорением за ${gpuResult.metrics.processingTime}мс` :
      'с CPU fallback';

    broadcastLog('success', `Пакет ${packageName} успешно добавлен в базу данных с ${packageInfo.versions.length} версиями ${processingInfo}`);

    // Обновляем статистику
    systemStats.totalPackages++;
    systemStats.analysisSpeed = gpuResult.metrics.gpuAccelerated ?
      1000 / gpuResult.metrics.averageTimePerVersion : systemStats.analysisSpeed;
    broadcastStats();

    broadcast({
      type: 'task_complete',
      taskName: task.name,
      gpuAccelerated: gpuResult.metrics.gpuAccelerated
    });

  } catch (error) {
    task.status = 'failed';
    task.error = error.message;
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('error', `Ошибка сбора пакета ${packageName}: ${error.message}`);
  }
}

// Сбор нескольких пакетов
async function runMultiplePackageCollection(task, packages) {
  try {
    task.status = 'running';
    broadcastTaskUpdate(task);
    broadcastLog('info', `Начинаем сбор ${packages.length} пакетов`);

    let completed = 0;
    const total = packages.length;

    for (const packageName of packages) {
      try {
        // Проверяем, не остановлена ли задача
        if (task.status === 'stopped') {
          break;
        }

        broadcastLog('info', `Обрабатываем пакет: ${packageName} (${completed + 1}/${total})`);

        // Получаем информацию о пакете
        const axios = require('axios');
        const response = await axios.get(`https://registry.npmjs.org/${packageName}`, {
          timeout: 10000
        });

        const packageData = response.data;

        // Сохраняем в базу данных
        await db.updatePackageInfo(packageName, {
          description: packageData.description,
          repository: packageData.repository?.url,
          homepage: packageData.homepage,
          license: packageData.license,
          latest_version: packageData['dist-tags']?.latest,
          versions: Object.keys(packageData.versions || {})
        });

        completed++;

        // Отправляем прогресс
        const progress = (completed / total) * 100;
        broadcast({
          type: 'progress',
          progress: progress,
          details: `Обработано ${completed} из ${total} пакетов`
        });

        // Небольшая задержка чтобы не перегружать npm registry
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        broadcastLog('warning', `Ошибка обработки пакета ${packageName}: ${error.message}`);
        completed++;
      }
    }

    task.status = task.status === 'stopped' ? 'stopped' : 'completed';
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('success', `Завершен сбор пакетов. Обработано: ${completed}/${total}`);

    // Обновляем статистику
    const stats = await getDatabaseStats();
    systemStats.totalPackages = stats.totalPackages;
    broadcastStats();

    broadcast({
      type: 'task_complete',
      taskName: task.name
    });

  } catch (error) {
    task.status = 'failed';
    task.error = error.message;
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('error', `Ошибка сбора пакетов: ${error.message}`);
  }
}

// Сбор популярных пакетов
async function runPopularPackageCollection(task, limit) {
  try {
    task.status = 'running';
    broadcastTaskUpdate(task);
    broadcastLog('info', `Начинаем сбор топ-${limit} популярных пакетов`);

    // Список популярных пакетов (в реальном приложении можно получать из API)
    const popularPackages = [
      'react', 'vue', 'angular', 'express', 'lodash', 'axios', 'webpack', 'babel-core',
      'typescript', 'eslint', 'prettier', 'jest', 'mocha', 'chai', 'sinon', 'moment',
      'jquery', 'bootstrap', 'material-ui', 'ant-design', 'styled-components', 'emotion',
      'redux', 'mobx', 'rxjs', 'socket.io', 'passport', 'bcrypt', 'jsonwebtoken',
      'mongoose', 'sequelize', 'prisma', 'typeorm', 'knex', 'pg', 'mysql2', 'redis',
      'nodemon', 'pm2', 'forever', 'cross-env', 'dotenv', 'config', 'yargs', 'commander',
      'chalk', 'inquirer', 'ora', 'progress', 'debug', 'winston', 'morgan', 'helmet',
      'cors', 'body-parser', 'multer', 'sharp', 'jimp', 'pdf2pic', 'puppeteer', 'playwright'
    ];

    const packagesToCollect = popularPackages.slice(0, Math.min(limit, popularPackages.length));
    await runMultiplePackageCollection(task, packagesToCollect);

  } catch (error) {
    task.status = 'failed';
    task.error = error.message;
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('error', `Ошибка сбора популярных пакетов: ${error.message}`);
  }
}

// Сбор по категориям
async function runCategoryCollection(task, categories) {
  try {
    task.status = 'running';
    broadcastTaskUpdate(task);
    broadcastLog('info', `Начинаем сбор пакетов по категориям: ${categories.join(', ')}`);

    // Пакеты по категориям
    const categoryPackages = {
      'framework': ['react', 'vue', 'angular', 'svelte', 'ember'],
      'library': ['lodash', 'underscore', 'ramda', 'immutable'],
      'utility': ['moment', 'date-fns', 'uuid', 'validator', 'sanitize-html'],
      'build-tool': ['webpack', 'rollup', 'parcel', 'vite', 'esbuild'],
      'testing': ['jest', 'mocha', 'jasmine', 'karma', 'cypress'],
      'react': ['react-dom', 'react-router', 'react-redux', 'react-hook-form'],
      'vue': ['vue-router', 'vuex', 'nuxt', 'quasar'],
      'angular': ['@angular/core', '@angular/common', '@angular/router'],
      'node': ['express', 'koa', 'fastify', 'hapi'],
      'webpack': ['webpack-cli', 'webpack-dev-server', 'html-webpack-plugin'],
      'babel': ['@babel/core', '@babel/preset-env', '@babel/preset-react']
    };

    let allPackages = [];
    categories.forEach(category => {
      if (categoryPackages[category]) {
        allPackages = allPackages.concat(categoryPackages[category]);
      }
    });

    // Удаляем дубликаты
    allPackages = [...new Set(allPackages)];

    await runMultiplePackageCollection(task, allPackages);

  } catch (error) {
    task.status = 'failed';
    task.error = error.message;
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('error', `Ошибка сбора по категориям: ${error.message}`);
  }
}

// Обновление существующих пакетов
async function runPackageUpdate(task) {
  try {
    task.status = 'running';
    broadcastTaskUpdate(task);
    broadcastLog('info', 'Начинаем обновление существующих пакетов');

    // Получаем список всех пакетов из базы данных
    const packages = await getPackagesFromDB('', 1000, 0);
    const packageNames = packages.map(pkg => pkg.name);

    await runMultiplePackageCollection(task, packageNames);

  } catch (error) {
    task.status = 'failed';
    task.error = error.message;
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('error', `Ошибка обновления пакетов: ${error.message}`);
  }
}

// Автоматический сбор
async function runPackageCollection(task, mode) {
  try {
    task.status = 'running';
    broadcastTaskUpdate(task);
    broadcastLog('info', 'Начинаем автоматический сбор пакетов');

    // Автоматически собираем популярные пакеты
    await runPopularPackageCollection(task, 100);

  } catch (error) {
    task.status = 'failed';
    task.error = error.message;
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('error', `Ошибка автоматического сбора: ${error.message}`);
  }
}

// Загрузка статистики из базы данных
async function loadDatabaseStats() {
  try {
    const stats = await getDatabaseStats();
    systemStats.totalPackages = stats.totalPackages;
    console.log(`📊 Загружено статистики: ${stats.totalPackages} пакетов в БД`);
  } catch (error) {
    console.error('Ошибка загрузки статистики из БД:', error);
  }
}

// Запуск сервера
const PORT = process.env.PORT || 3000;

async function startServer() {
  try {
    console.log('🔧 Инициализация базы данных...');
    await initializeDatabase();
    console.log('📊 Загрузка статистики...');
    await loadDatabaseStats();

    // Serve frontend - добавляем в конце после всех API маршрутов
    app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'public', 'index.html'));
    });

    server.listen(PORT, () => {
      console.log(`🌐 Сервер запущен на порту ${PORT}`);
      console.log(`🎛️ Панель управления: http://localhost:${PORT}`);
      broadcastLog('success', `Сервер запущен на порту ${PORT}`);
    });

    // Запускаем периодическое обновление метрик
    setInterval(updateSystemMetrics, 5000);

    // Добавляем начальную задачу
    const initTask = {
      id: taskIdCounter++,
      name: 'Инициализация системы',
      status: 'completed',
      startTime: systemStats.startTime,
      endTime: Date.now()
    };
    taskQueue.push(initTask);

  } catch (error) {
    console.error('Ошибка запуска сервера:', error);
    process.exit(1);
  }
}

// Обработка завершения процесса
process.on('SIGINT', () => {
  console.log('Получен сигнал SIGINT, завершаем работу...');
  broadcastLog('warning', 'Сервер завершает работу...');

  // Закрываем WebSocket соединения
  wss.clients.forEach(client => {
    client.close();
  });

  // Закрываем базу данных
  db.close();

  server.close(() => {
    console.log('Сервер остановлен');
    process.exit(0);
  });
});

startServer();
