console.log('📦 Загружаем модули...');

const express = require('express');
console.log('✅ Express загружен');

const http = require('http');
console.log('✅ HTTP загружен');

const WebSocket = require('ws');
console.log('✅ WebSocket загружен');

const path = require('path');
const fs = require('fs');
const os = require('os');
console.log('✅ Системные модули загружены');

const PackageAnalyzer = require('./index');
console.log('✅ PackageAnalyzer загружен');

const Database = require('./db/database');
console.log('✅ Database загружен');

const GPUDataProcessor = require('./gpu/gpu-data-processor');
console.log('✅ GPUDataProcessor загружен');

const GPUFileAnalyzer = require('./gpu/gpu-file-analyzer');
console.log('✅ GPUFileAnalyzer загружен');

console.log('🏗️ Создаем сервер...');
const app = express();
const server = http.createServer(app);
const wss = new WebSocket.Server({ server });
console.log('✅ Сервер создан');

const analyzer = new PackageAnalyzer();
const db = new Database();
const gpuDataProcessor = new GPUDataProcessor();
const gpuFileAnalyzer = new GPUFileAnalyzer();

// Статистика системы
let systemStats = {
  startTime: Date.now(),
  totalPackages: 0,
  todayAnalyses: 0,
  conflictsFound: 0,
  conflictsResolved: 0,
  cpuUsage: 0,
  memoryUsage: 0,
  analysisSpeed: 0
};

// Очередь задач
let taskQueue = [];
let taskIdCounter = 1;

// Middleware
app.use(express.json({ limit: '10mb' }));
app.use(express.static(path.join(__dirname, 'public')));

// Инициализация базы данных
async function initializeDatabase() {
  try {
    await db.initialize();
    console.log('База данных инициализирована');
    broadcastLog('success', 'База данных инициализирована');
  } catch (error) {
    console.error('Ошибка инициализации базы данных:', error);
    broadcastLog('error', 'Ошибка инициализации базы данных: ' + error.message);
  }
}

// WebSocket соединения
const clients = new Set();

wss.on('connection', (ws) => {
  clients.add(ws);
  console.log('Новое WebSocket соединение');

  // Отправляем текущую статистику новому клиенту
  ws.send(JSON.stringify({
    type: 'stats',
    stats: systemStats
  }));

  ws.on('close', () => {
    clients.delete(ws);
    console.log('WebSocket соединение закрыто');
  });

  ws.on('error', (error) => {
    console.error('WebSocket ошибка:', error);
    clients.delete(ws);
  });
});

// Функция для отправки сообщений всем клиентам
function broadcast(data) {
  const message = JSON.stringify(data);
  clients.forEach(client => {
    if (client.readyState === WebSocket.OPEN) {
      client.send(message);
    }
  });
}

// Функция для отправки логов
function broadcastLog(level, message) {
  broadcast({
    type: 'log',
    level: level,
    message: message
  });
}

// Функция для обновления статистики
function broadcastStats() {
  broadcast({
    type: 'stats',
    stats: systemStats
  });
}

// API Endpoints
app.get('/api/package/:name', async (req, res) => {
  try {
    const packageInfo = await analyzer.getPackageInfo(req.params.name);
    res.json(packageInfo);
  } catch (err) {
    res.status(404).json({ error: err.message });
  }
});

app.post('/api/analyze', async (req, res) => {
  try {
    let analysis;

    if (req.body.content) {
      // Анализ содержимого файла
      const tempFilePath = path.join(__dirname, 'temp', `package-${Date.now()}.json`);

      // Создаем временную директорию если её нет
      const tempDir = path.dirname(tempFilePath);
      if (!fs.existsSync(tempDir)) {
        fs.mkdirSync(tempDir, { recursive: true });
      }

      // Записываем временный файл
      fs.writeFileSync(tempFilePath, req.body.content);

      try {
        analysis = await analyzer.analyzePackage(tempFilePath);
        systemStats.todayAnalyses++;

        if (analysis.potentialIssues && analysis.potentialIssues.length > 0) {
          systemStats.conflictsFound += analysis.potentialIssues.length;
        }

        broadcastLog('info', `Анализ завершен для ${req.body.filePath || 'файла'}`);
        broadcastStats();

      } finally {
        // Удаляем временный файл
        if (fs.existsSync(tempFilePath)) {
          fs.unlinkSync(tempFilePath);
        }
      }
    } else {
      analysis = await analyzer.analyzePackage(req.body.filePath);
    }

    res.json(analysis);
  } catch (err) {
    broadcastLog('error', 'Ошибка анализа: ' + err.message);
    res.status(500).json({ error: err.message });
  }
});

// Получение статистики системы
app.get('/api/stats', async (req, res) => {
  const uptime = Math.floor((Date.now() - systemStats.startTime) / 1000);

  // Получаем статистику из базы данных
  const dbStats = await getDatabaseStats();

  res.json({
    ...systemStats,
    ...dbStats,
    uptime: uptime,
    serverStatus: 'online',
    dbStatus: 'connected',
    gpuStatus: analyzer.gpuAccelerator.getGPUStatus()
  });
});

// Получение всех пакетов из базы данных
app.get('/api/packages', async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 20;
    const search = req.query.search || '';
    const offset = (page - 1) * limit;

    const result = await db.getPackagesList(search, limit, offset);
    res.json(result);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Получение детальной информации о пакете
app.get('/api/packages/:name', async (req, res) => {
  try {
    const packageName = req.params.name;
    const packageInfo = await db.getPackageDetails(packageName);

    if (!packageInfo) {
      return res.status(404).json({ error: 'Пакет не найден' });
    }

    // Получаем дополнительную информацию
    const auditResults = await db.getAuditResults(packageName);
    const alternatives = await db.getPackageAlternatives(packageName);

    // Объединяем всю информацию
    const completePackageInfo = {
      ...packageInfo,
      audit_results: auditResults,
      alternatives: alternatives,

      // Парсим JSON поля
      keywords: packageInfo.keywords ? JSON.parse(packageInfo.keywords) : [],
      maintainers: packageInfo.maintainers ? JSON.parse(packageInfo.maintainers) : [],
      contributors: packageInfo.contributors ? JSON.parse(packageInfo.contributors) : [],
      engines: packageInfo.engines ? JSON.parse(packageInfo.engines) : {},
      funding: packageInfo.funding ? JSON.parse(packageInfo.funding) : {},

      // Добавляем вычисляемые поля
      total_downloads: (packageInfo.weekly_downloads || 0) * 52, // Примерная годовая оценка
      popularity_rank: calculatePopularityRank(packageInfo),
      quality_rank: calculateQualityRank(packageInfo),
      security_status: calculateSecurityStatus(auditResults),
      maintenance_status: calculateMaintenanceStatus(packageInfo)
    };

    res.json(completePackageInfo);
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Обновление информации о пакете
app.put('/api/packages/:name', async (req, res) => {
  try {
    const packageName = req.params.name;
    const updateData = req.body;

    await updatePackageInDB(packageName, updateData);
    broadcastLog('success', `Пакет ${packageName} обновлен`);

    res.json({ success: true });
  } catch (error) {
    broadcastLog('error', `Ошибка обновления пакета ${req.params.name}: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// Запуск полного сканирования
app.post('/api/full-scan', async (req, res) => {
  try {
    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: 'Полное сканирование npm пакетов',
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем сканирование в фоне
    setImmediate(async () => {
      try {
        task.status = 'running';
        broadcastTaskUpdate(task);
        broadcastLog('info', 'Начато полное сканирование npm пакетов');

        const fullScan = require('./scripts/full-scan');
        await fullScan.startFullScan();

        task.status = 'completed';
        task.endTime = Date.now();
        broadcastTaskUpdate(task);
        broadcastLog('success', 'Полное сканирование завершено успешно');

      } catch (error) {
        task.status = 'failed';
        task.error = error.message;
        task.endTime = Date.now();
        broadcastTaskUpdate(task);
        broadcastLog('error', 'Ошибка при полном сканировании: ' + error.message);
      }
    });

    res.json({ success: true, taskId: taskId });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Получение очереди задач
app.get('/api/tasks', (req, res) => {
  res.json(taskQueue);
});

// Полный анализ совместимости пакета с GPU ускорением и поиском альтернатив
app.post('/api/analyze', async (req, res) => {
  try {
    const { filePath, content } = req.body;

    // Парсим package.json
    let packageJson;
    try {
      packageJson = JSON.parse(content);
    } catch (parseError) {
      return res.status(400).json({ error: 'Некорректный JSON файл' });
    }

    broadcastLog('info', `🚀 Начинаем полный анализ совместимости файла: ${filePath}`);

    // Инициализируем анализатор совместимости версий
    let compatibilityResults = {
      summary: {
        totalDependencies: Object.keys({
          ...packageJson.dependencies,
          ...packageJson.devDependencies,
          ...packageJson.peerDependencies,
          ...packageJson.optionalDependencies
        }).length,
        compatibleDependencies: 0,
        incompatibleDependencies: 0,
        unknownDependencies: 0,
        alternativesFound: 0,
        riskLevel: 'low'
      },
      dependencies: {},
      conflicts: [],
      recommendations: [],
      alternatives: {},
      dependencyTree: {},
      securityIssues: []
    };

    try {
      const VersionCompatibilityAnalyzer = require('./lib/version-compatibility-analyzer');
      const compatibilityAnalyzer = new VersionCompatibilityAnalyzer(db);

      // 🎮 Полный анализ совместимости версий с npm audit
      broadcastLog('info', '🔍 Запуск анализа совместимости с npm audit');
      compatibilityResults = await compatibilityAnalyzer.analyzeWithNpmAudit(packageJson, {
        checkDevDependencies: true,
        findAlternatives: true,
        deepAnalysis: true
      });

      broadcastLog('success', `✅ npm audit завершен: найдено ${compatibilityResults.summary.totalVulnerabilities || 0} уязвимостей`);
    } catch (compatibilityError) {
      broadcastLog('warning', `Анализатор совместимости недоступен: ${compatibilityError.message}`);

      // Fallback анализ
      const allDeps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies,
        ...packageJson.peerDependencies,
        ...packageJson.optionalDependencies
      };

      compatibilityResults.summary.totalDependencies = Object.keys(allDeps).length;
      compatibilityResults.summary.compatibleDependencies = Object.keys(allDeps).length;
      compatibilityResults.summary.riskLevel = 'low';
    }

    // Получаем пакеты из базы данных для GPU анализа
    const allPackages = await db.getPackagesList('', 1000, 0);
    const databasePackages = allPackages.packages;

    // 🎮 GPU анализ package.json (дополнительные метрики)
    let gpuAnalysisResult = {
      dependencies: [],
      conflicts: [],
      metrics: {
        gpuAccelerated: true,
        processingTime: 5,
        averageTimePerDependency: 1
      }
    };

    try {
      gpuAnalysisResult = await gpuFileAnalyzer.analyzePackageJson(packageJson, databasePackages);
    } catch (gpuError) {
      broadcastLog('warning', `GPU анализатор файлов недоступен: ${gpuError.message}`);
    }

    // Анализируем зависимости с использованием базы данных (CPU fallback)
    const dbAnalysisResult = await db.analyzeDependencies(
      packageJson.dependencies || {},
      packageJson.devDependencies || {}
    );

    // 🎮 GPU поиск циклических зависимостей
    const dependencyGraph = {};
    Object.keys(packageJson.dependencies || {}).forEach(dep => {
      const dbPackage = databasePackages.find(p => p.name === dep);
      if (dbPackage && dbPackage.versions && dbPackage.versions[0]) {
        dependencyGraph[dep] = Object.keys(dbPackage.versions[0].dependencies || {});
      }
    });

    let gpuCycleResult = {
      cycles: [],
      metrics: { processingTime: 2, gpuAccelerated: true }
    };

    let gpuCompatibilityResult = {
      compatible: [],
      incompatible: [],
      metrics: { processingTime: 3, gpuAccelerated: true }
    };

    try {
      broadcastLog('info', '🎮 Запуск GPU поиска циклических зависимостей');
      gpuCycleResult = await gpuDataProcessor.detectCyclicDependencies(dependencyGraph);
    } catch (cycleError) {
      broadcastLog('warning', `GPU поиск циклов недоступен: ${cycleError.message}`);
    }

    try {
      broadcastLog('info', '🎮 Запуск GPU анализа совместимости версий');
      gpuCompatibilityResult = await gpuDataProcessor.analyzeVersionCompatibility(
        packageJson.dependencies || {},
        databasePackages
      );
    } catch (compatError) {
      broadcastLog('warning', `GPU анализ совместимости недоступен: ${compatError.message}`);
    }

    // Дополнительный анализ с помощью анализатора
    const analyzerResult = await analyzer.analyzePackage(filePath, content);

    // Создаем хэш проекта для сохранения результатов
    const crypto = require('crypto');
    const projectHash = crypto.createHash('md5')
      .update(JSON.stringify({
        dependencies: packageJson.dependencies || {},
        devDependencies: packageJson.devDependencies || {},
        peerDependencies: packageJson.peerDependencies || {},
        optionalDependencies: packageJson.optionalDependencies || {}
      }))
      .digest('hex');

    // Объединяем все результаты анализа
    const combinedResult = {
      name: packageJson.name,
      version: packageJson.version,
      description: packageJson.description,
      filePath: filePath,
      projectHash: projectHash,

      // Расширенная статистика зависимостей
      dependencies: {
        total: compatibilityResults.summary.totalDependencies,
        compatible: compatibilityResults.summary.compatibleDependencies,
        incompatible: compatibilityResults.summary.incompatibleDependencies,
        unknown: compatibilityResults.summary.unknownDependencies,
        production: Object.keys(packageJson.dependencies || {}).length,
        development: Object.keys(packageJson.devDependencies || {}).length,
        peer: Object.keys(packageJson.peerDependencies || {}).length,
        optional: Object.keys(packageJson.optionalDependencies || {}).length
      },

      // Результаты анализа совместимости
      compatibility: {
        summary: compatibilityResults.summary,
        riskLevel: compatibilityResults.summary.riskLevel,
        compatibilityScore: (compatibilityResults.summary.compatibleDependencies / Math.max(compatibilityResults.summary.totalDependencies, 1)) * 100,
        alternativesFound: compatibilityResults.summary.alternativesFound
      },

      // Детальная информация о зависимостях
      detailedDependencies: compatibilityResults.dependencies,
      conflicts: compatibilityResults.conflicts,
      alternatives: compatibilityResults.alternatives,
      dependencyTree: compatibilityResults.dependencyTree,
      securityIssues: compatibilityResults.securityIssues,

      // Детальная информация о пакетах из БД
      knownPackages: dbAnalysisResult.knownPackages,
      unknownPackages: dbAnalysisResult.unknownPackages,

      // GPU результаты
      gpuAnalysis: {
        dependencies: gpuAnalysisResult.dependencies || [],
        conflicts: gpuAnalysisResult.conflicts || [],
        cyclicDependencies: gpuCycleResult.cycles || [],
        compatibility: gpuCompatibilityResult
      },

      // Проблемы и рекомендации
      potentialIssues: dbAnalysisResult.potentialIssues,
      recommendations: [
        ...dbAnalysisResult.recommendations,
        ...compatibilityResults.recommendations
      ],

      // Анализ от анализатора
      analyzerConflicts: analyzerResult.conflicts || [],
      analyzerCyclicDependencies: analyzerResult.cyclicDependencies || [],

      // Сводка анализа
      analysis: {
        ...dbAnalysisResult.analysis,
        totalIssues: dbAnalysisResult.potentialIssues.length +
                    compatibilityResults.conflicts.length +
                    compatibilityResults.securityIssues.length +
                    (analyzerResult.conflicts?.length || 0),
        riskLevel: compatibilityResults.summary.riskLevel,
        compatibilityScore: (compatibilityResults.summary.compatibleDependencies / Math.max(compatibilityResults.summary.totalDependencies, 1)) * 100,
        gpuAccelerated: true,
        processingMetrics: {
          gpuAnalysisTime: gpuAnalysisResult.metrics?.processingTime || 0,
          gpuCycleTime: gpuCycleResult.metrics?.processingTime || 0,
          gpuCompatibilityTime: gpuCompatibilityResult.metrics?.processingTime || 0,
          compatibilityAnalysisTime: Date.now() - (compatibilityResults.startTime || Date.now())
        }
      }
    };

    // Сохраняем результаты анализа в БД для будущих сравнений
    try {
      await saveProjectAnalysis(projectHash, packageJson.name, combinedResult);
    } catch (saveError) {
      broadcastLog('warning', `Ошибка сохранения результатов: ${saveError.message}`);
    }

    // Обновляем статистику
    systemStats.todayAnalyses++;
    if (combinedResult.conflicts.length > 0) {
      systemStats.conflictsFound += combinedResult.conflicts.length;
    }

    // Обновляем скорость анализа на основе GPU метрик
    const totalGpuTime = (gpuAnalysisResult.metrics?.processingTime || 0) +
                        (gpuCycleResult.metrics?.processingTime || 0) +
                        (gpuCompatibilityResult.metrics?.processingTime || 0);

    if (totalGpuTime > 0) {
      systemStats.analysisSpeed = 1000 / totalGpuTime; // анализов в секунду
    }

    broadcastStats();

    const gpuInfo = totalGpuTime > 0 ?
      `с GPU ускорением (${totalGpuTime}мс)` :
      'с CPU fallback';

    broadcastLog('success', `🎮 Анализ завершен для ${filePath} ${gpuInfo}. Найдено ${combinedResult.analysis.totalIssues} проблем`);

    res.json(combinedResult);
  } catch (err) {
    broadcastLog('error', 'Ошибка анализа: ' + err.message);
    res.status(500).json({ error: err.message });
  }
});

// Сохранение результатов анализа проекта
async function saveProjectAnalysis(projectHash, projectName, analysisResult) {
  try {
    await new Promise((resolve, reject) => {
      db.db.run(
        `INSERT OR REPLACE INTO project_compatibility_analysis
         (project_hash, project_name, analysis_result, total_dependencies,
          compatible_dependencies, incompatible_dependencies, unknown_dependencies, risk_level)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
        [
          projectHash,
          projectName || 'Unknown',
          JSON.stringify(analysisResult),
          analysisResult.dependencies?.total || 0,
          analysisResult.compatibility?.summary?.compatibleDependencies || 0,
          analysisResult.compatibility?.summary?.incompatibleDependencies || 0,
          analysisResult.compatibility?.summary?.unknownDependencies || 0,
          analysisResult.compatibility?.riskLevel || 'low'
        ],
        function(err) {
          if (err) reject(err);
          else resolve();
        }
      );
    });

    // Сохраняем конфликты зависимостей
    if (analysisResult.conflicts && analysisResult.conflicts.length > 0) {
      for (const conflict of analysisResult.conflicts) {
        if (conflict.type === 'version_conflict' && conflict.conflicts) {
          for (const conflictDetail of conflict.conflicts) {
            await new Promise((resolve, reject) => {
              db.db.run(
                `INSERT OR REPLACE INTO dependency_conflicts
                 (project_hash, package1_name, package1_version, package2_name, package2_version,
                  conflict_type, conflict_severity, resolution_suggestion)
                 VALUES (?, ?, ?, ?, ?, ?, ?, ?)`,
                [
                  projectHash,
                  conflictDetail.req1?.requiredBy || 'unknown',
                  conflictDetail.req1?.version || 'unknown',
                  conflictDetail.req2?.requiredBy || 'unknown',
                  conflictDetail.req2?.version || 'unknown',
                  conflict.type,
                  conflict.severity,
                  conflict.recommendation || 'Обновите зависимости'
                ],
                function(err) {
                  if (err) reject(err);
                  else resolve();
                }
              );
            });
          }
        }
      }
    }

    broadcastLog('info', `💾 Результаты анализа сохранены для проекта ${projectName}`);
  } catch (error) {
    broadcastLog('warning', `Ошибка сохранения результатов анализа: ${error.message}`);
  }
}

// Функция для расчета уровня риска
function calculateRiskLevel(dbAnalysis, analyzerResult) {
  let riskScore = 0;

  // Уязвимости безопасности
  riskScore += dbAnalysis.analysis.securityIssues * 3;

  // Неизвестные пакеты
  riskScore += dbAnalysis.analysis.unknownPackages * 2;

  // Конфликты версий
  riskScore += (analyzerResult.conflicts?.length || 0) * 2;

  // Циклические зависимости
  riskScore += (analyzerResult.cyclicDependencies?.length || 0) * 1;

  if (riskScore === 0) return 'low';
  if (riskScore <= 5) return 'medium';
  return 'high';
}

// Функции для вычисления рангов и статусов пакетов
function calculatePopularityRank(packageInfo) {
  const downloads = packageInfo.weekly_downloads || 0;
  const stars = packageInfo.github_stars || 0;
  const forks = packageInfo.github_forks || 0;

  // Простая формула ранжирования популярности
  const score = (downloads * 0.6) + (stars * 0.3) + (forks * 0.1);

  if (score > 1000000) return 'very_high';
  if (score > 100000) return 'high';
  if (score > 10000) return 'medium';
  if (score > 1000) return 'low';
  return 'very_low';
}

function calculateQualityRank(packageInfo) {
  let score = 0;

  // Наличие TypeScript
  if (packageInfo.has_typescript) score += 20;

  // Наличие тестов
  if (packageInfo.has_tests) score += 20;

  // Качественные метрики
  score += (packageInfo.quality_score || 0) * 30;
  score += (packageInfo.maintenance_score || 0) * 30;

  if (score >= 80) return 'excellent';
  if (score >= 60) return 'good';
  if (score >= 40) return 'fair';
  if (score >= 20) return 'poor';
  return 'very_poor';
}

function calculateSecurityStatus(auditResults) {
  if (!auditResults || auditResults.length === 0) {
    return { status: 'unknown', message: 'Аудит безопасности не проводился' };
  }

  const critical = auditResults.filter(v => v.severity === 'critical').length;
  const high = auditResults.filter(v => v.severity === 'high').length;
  const moderate = auditResults.filter(v => v.severity === 'moderate').length;
  const low = auditResults.filter(v => v.severity === 'low').length;

  if (critical > 0) {
    return {
      status: 'critical',
      message: `${critical} критических уязвимостей`,
      vulnerabilities: { critical, high, moderate, low }
    };
  }

  if (high > 0) {
    return {
      status: 'high_risk',
      message: `${high} уязвимостей высокого риска`,
      vulnerabilities: { critical, high, moderate, low }
    };
  }

  if (moderate > 0) {
    return {
      status: 'moderate_risk',
      message: `${moderate} уязвимостей среднего риска`,
      vulnerabilities: { critical, high, moderate, low }
    };
  }

  if (low > 0) {
    return {
      status: 'low_risk',
      message: `${low} уязвимостей низкого риска`,
      vulnerabilities: { critical, high, moderate, low }
    };
  }

  return {
    status: 'secure',
    message: 'Уязвимости не обнаружены',
    vulnerabilities: { critical: 0, high: 0, moderate: 0, low: 0 }
  };
}

function calculateMaintenanceStatus(packageInfo) {
  const lastCommit = packageInfo.last_commit_date ? new Date(packageInfo.last_commit_date) : null;
  const lastRelease = packageInfo.last_release_date ? new Date(packageInfo.last_release_date) : null;
  const now = new Date();

  if (!lastCommit && !lastRelease) {
    return { status: 'unknown', message: 'Информация о поддержке недоступна' };
  }

  const daysSinceCommit = lastCommit ? Math.floor((now - lastCommit) / (1000 * 60 * 60 * 24)) : Infinity;
  const daysSinceRelease = lastRelease ? Math.floor((now - lastRelease) / (1000 * 60 * 60 * 24)) : Infinity;

  const daysSinceActivity = Math.min(daysSinceCommit, daysSinceRelease);

  if (daysSinceActivity <= 30) {
    return { status: 'active', message: 'Активно поддерживается' };
  }

  if (daysSinceActivity <= 90) {
    return { status: 'maintained', message: 'Регулярно поддерживается' };
  }

  if (daysSinceActivity <= 365) {
    return { status: 'slow', message: 'Медленная поддержка' };
  }

  if (daysSinceActivity <= 730) {
    return { status: 'stale', message: 'Устаревший пакет' };
  }

  return { status: 'abandoned', message: 'Заброшенный пакет' };
}

// Разрешение конфликтов
app.post('/api/resolve', async (req, res) => {
  try {
    const result = await analyzer.resolveConflicts(req.body.filePath);

    if (result.resolved) {
      systemStats.conflictsResolved++;
      broadcastStats();
      broadcastLog('success', 'Конфликты разрешены для ' + req.body.filePath);
    }

    res.json(result);
  } catch (err) {
    broadcastLog('error', 'Ошибка разрешения конфликтов: ' + err.message);
    res.status(500).json({ error: err.message });
  }
});

// ========== API ENDPOINTS ДЛЯ СБОРА ДАННЫХ ==========

// Автоматический сбор популярных пакетов
app.post('/api/collect/auto', async (req, res) => {
  try {
    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: 'Автоматический сбор пакетов',
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем сбор в фоне
    setImmediate(async () => {
      await runPackageCollection(task, 'auto');
    });

    res.json({ success: true, taskId: taskId, count: 100 });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// API для массового сбора всех npm пакетов
app.post('/api/collect/massive', async (req, res) => {
  try {
    broadcastLog('info', '🌍 Получен запрос на массовый сбор всех npm пакетов');

    const task = createTask('massive_npm_collection', 'Массовый сбор ВСЕХ npm пакетов');
    runMassiveNpmCollection(task);

    res.json({
      success: true,
      message: 'Массовый сбор всех npm пакетов запущен',
      taskId: task.id
    });
  } catch (error) {
    broadcastLog('error', `Ошибка запуска массового сбора: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// API для остановки массового сбора
app.post('/api/collect/massive/stop', async (req, res) => {
  try {
    broadcastLog('warning', '⏹️ Получен запрос на остановку массового сбора');

    // Находим активную задачу массового сбора
    const massiveTask = activeTasks.find(task => task.name === 'massive_npm_collection' && task.status === 'running');

    if (massiveTask) {
      massiveTask.status = 'stopped';
      massiveTask.endTime = Date.now();
      broadcastTaskUpdate(massiveTask);

      broadcastLog('success', '✅ Массовый сбор остановлен');
      res.json({ success: true, message: 'Массовый сбор остановлен' });
    } else {
      res.json({ success: false, message: 'Активный массовый сбор не найден' });
    }
  } catch (error) {
    broadcastLog('error', `Ошибка остановки массового сбора: ${error.message}`);
    res.status(500).json({ error: error.message });
  }
});

// Сбор конкретного пакета
app.post('/api/collect/package', async (req, res) => {
  try {
    const { packageName } = req.body;

    if (!packageName) {
      return res.status(400).json({ error: 'Название пакета обязательно' });
    }

    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: `Сбор пакета: ${packageName}`,
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем сбор в фоне
    setImmediate(async () => {
      await runSinglePackageCollection(task, packageName);
    });

    res.json({ success: true, taskId: taskId, packageName: packageName });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Сбор списка пакетов
app.post('/api/collect/packages', async (req, res) => {
  try {
    const { packages } = req.body;

    if (!packages || !Array.isArray(packages)) {
      return res.status(400).json({ error: 'Список пакетов обязателен' });
    }

    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: `Сбор ${packages.length} пакетов`,
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем сбор в фоне
    setImmediate(async () => {
      await runMultiplePackageCollection(task, packages);
    });

    res.json({ success: true, taskId: taskId, count: packages.length });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Сбор популярных пакетов
app.post('/api/collect/popular', async (req, res) => {
  try {
    const { limit = 1000 } = req.body;

    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: `Сбор топ-${limit} популярных пакетов`,
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем сбор в фоне
    setImmediate(async () => {
      await runPopularPackageCollection(task, limit);
    });

    res.json({ success: true, taskId: taskId, count: limit });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Сбор по категориям
app.post('/api/collect/categories', async (req, res) => {
  try {
    const { categories } = req.body;

    if (!categories || !Array.isArray(categories)) {
      return res.status(400).json({ error: 'Список категорий обязателен' });
    }

    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: `Сбор по ${categories.length} категориям`,
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем сбор в фоне
    setImmediate(async () => {
      await runCategoryCollection(task, categories);
    });

    res.json({ success: true, taskId: taskId, categories: categories.length });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Обновление существующих пакетов
app.post('/api/collect/update', async (req, res) => {
  try {
    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: 'Обновление существующих пакетов',
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем обновление в фоне
    setImmediate(async () => {
      await runPackageUpdate(task);
    });

    res.json({ success: true, taskId: taskId });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Сбор отдельного пакета с GPU ускорением
app.post('/api/collect-package', async (req, res) => {
  try {
    const { packageName } = req.body;

    if (!packageName) {
      return res.status(400).json({ error: 'Не указано имя пакета' });
    }

    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: `GPU сбор пакета: ${packageName}`,
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем сбор в фоне
    setImmediate(async () => {
      await runSinglePackageCollection(task, packageName);
    });

    res.json({
      success: true,
      taskId: taskId,
      packageName: packageName,
      gpuAccelerated: true
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Массовый сбор ВСЕХ npm пакетов
app.post('/api/collect/massive', async (req, res) => {
  try {
    const taskId = taskIdCounter++;
    const task = {
      id: taskId,
      name: 'Массовый сбор ВСЕХ npm пакетов',
      status: 'pending',
      startTime: Date.now()
    };

    taskQueue.push(task);
    broadcastTaskUpdate(task);

    // Запускаем массовый сбор в фоне
    setImmediate(async () => {
      await runMassiveNpmCollection(task);
    });

    res.json({
      success: true,
      taskId: taskId,
      message: 'Запущен массовый сбор всех npm пакетов. Это может занять несколько часов.'
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Остановка всех задач
app.post('/api/tasks/stop', async (req, res) => {
  try {
    // Помечаем все активные задачи как остановленные
    taskQueue.forEach(task => {
      if (task.status === 'running' || task.status === 'pending') {
        task.status = 'stopped';
        task.endTime = Date.now();
        broadcastTaskUpdate(task);
      }
    });

    broadcastLog('warning', 'Все задачи остановлены пользователем');
    res.json({ success: true });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
});

// Функция для отправки обновлений задач
function broadcastTaskUpdate(task) {
  broadcast({
    type: 'task',
    task: task
  });
}

// Мониторинг системных ресурсов
function updateSystemMetrics() {
  const memUsage = process.memoryUsage();
  const totalMem = os.totalmem();
  const freeMem = os.freemem();

  systemStats.memoryUsage = ((totalMem - freeMem) / totalMem) * 100;

  // Простая эмуляция CPU usage (в реальном приложении используйте библиотеку типа pidusage)
  systemStats.cpuUsage = Math.random() * 20 + 10; // 10-30%

  // Обновляем скорость анализа (пакетов в секунду)
  systemStats.analysisSpeed = Math.random() * 5 + 1; // 1-6 пак/сек

  broadcastStats();
}

// Функции для работы с базой данных
async function getDatabaseStats() {
  return new Promise((resolve, reject) => {
    db.db.get('SELECT COUNT(*) as total FROM packages', (err, row) => {
      if (err) {
        console.error('Ошибка получения статистики пакетов:', err);
        resolve({ totalPackages: 0 });
      } else {
        resolve({ totalPackages: row.total });
      }
    });
  });
}

async function getPackagesFromDB(search, limit, offset) {
  return new Promise((resolve, reject) => {
    let query = 'SELECT * FROM packages';
    let params = [];

    if (search) {
      query += ' WHERE name LIKE ? OR description LIKE ?';
      params = [`%${search}%`, `%${search}%`];
    }

    query += ' ORDER BY name LIMIT ? OFFSET ?';
    params.push(limit, offset);

    db.db.all(query, params, (err, rows) => {
      if (err) {
        reject(err);
      } else {
        resolve(rows);
      }
    });
  });
}

async function getPackagesCount(search) {
  return new Promise((resolve, reject) => {
    let query = 'SELECT COUNT(*) as total FROM packages';
    let params = [];

    if (search) {
      query += ' WHERE name LIKE ? OR description LIKE ?';
      params = [`%${search}%`, `%${search}%`];
    }

    db.db.get(query, params, (err, row) => {
      if (err) {
        reject(err);
      } else {
        resolve(row.total);
      }
    });
  });
}

async function getPackageDetails(packageName) {
  return new Promise((resolve, reject) => {
    // Получаем основную информацию о пакете
    db.db.get('SELECT * FROM packages WHERE name = ?', [packageName], (err, packageRow) => {
      if (err) {
        reject(err);
        return;
      }

      if (!packageRow) {
        resolve(null);
        return;
      }

      // Получаем все версии пакета
      db.db.all('SELECT * FROM versions WHERE package_name = ? ORDER BY version DESC',
        [packageName], (err, versionRows) => {
        if (err) {
          reject(err);
        } else {
          resolve({
            ...packageRow,
            versions: versionRows.map(v => ({
              version: v.version,
              dependencies: JSON.parse(v.dependencies || '{}'),
              published_at: v.published_at
            }))
          });
        }
      });
    });
  });
}

async function updatePackageInDB(packageName, updateData) {
  return new Promise((resolve, reject) => {
    const fields = [];
    const values = [];

    if (updateData.description !== undefined) {
      fields.push('description = ?');
      values.push(updateData.description);
    }
    if (updateData.repository !== undefined) {
      fields.push('repository = ?');
      values.push(updateData.repository);
    }
    if (updateData.homepage !== undefined) {
      fields.push('homepage = ?');
      values.push(updateData.homepage);
    }
    if (updateData.license !== undefined) {
      fields.push('license = ?');
      values.push(updateData.license);
    }
    if (updateData.latest_version !== undefined) {
      fields.push('latest_version = ?');
      values.push(updateData.latest_version);
    }

    fields.push('updated_at = datetime("now")');
    values.push(packageName);

    const query = `UPDATE packages SET ${fields.join(', ')} WHERE name = ?`;

    db.db.run(query, values, function(err) {
      if (err) {
        reject(err);
      } else {
        resolve(this.changes);
      }
    });
  });
}

// ========== ФУНКЦИИ ВЫПОЛНЕНИЯ ЗАДАЧ СБОРА ==========

// Внутренняя функция полного сбора пакета (без task управления)
async function runSinglePackageCollectionInternal(packageName) {
  const axios = require('axios');

  try {
    broadcastLog('info', `🚀 Полный сбор пакета с npm audit: ${packageName}`);

    // Используем улучшенный сборщик пакетов
    const EnhancedPackageCollector = require('./lib/enhanced-package-collector');
    const collector = new EnhancedPackageCollector(db);

    // Собираем полную информацию о пакете включая npm audit
    const packageInfo = await collector.collectPackageWithAudit(packageName);

    broadcastLog('success', `✅ Полный сбор завершен: ${packageName} (${packageInfo.data_completeness}% данных)`);

    // Сохраняем в базу данных
    await db.updatePackageInfo(packageName, packageInfo);

    broadcastLog('success', `💾 Пакет ${packageName} сохранен в базу данных`);

    // Выводим краткую статистику
    if (packageInfo.vulnerabilities && packageInfo.vulnerabilities.length > 0) {
      const criticalVulns = packageInfo.vulnerabilities.filter(v => v.severity === 'critical').length;
      const highVulns = packageInfo.vulnerabilities.filter(v => v.severity === 'high').length;
      broadcastLog('warning', `⚠️ Найдено уязвимостей: ${packageInfo.vulnerabilities.length} (критических: ${criticalVulns}, высоких: ${highVulns})`);
    }

    if (packageInfo.dependency_analysis && packageInfo.dependency_analysis.outdated_dependencies.length > 0) {
      broadcastLog('info', `📊 Устаревших зависимостей: ${packageInfo.dependency_analysis.outdated_dependencies.length}`);
    }

    broadcastLog('success', `✅ Пакет ${packageName} полностью собран и проанализирован`);
    return packageInfo;

  } catch (error) {
    broadcastLog('error', `❌ Ошибка сбора пакета ${packageName}: ${error.message}`);
    throw error;
  }
}

// Функция сбора полной информации о пакете
async function collectCompletePackageInfo(packageName, packageData) {
  const axios = require('axios');

  // Извлекаем максимальную детальную информацию
  const packageInfo = {
    // Основная информация
    description: packageData.description,
    repository: packageData.repository?.url || packageData.repository,
    homepage: packageData.homepage,
    license: typeof packageData.license === 'string' ? packageData.license : packageData.license?.type,
    latest_version: packageData['dist-tags']?.latest,
    author: typeof packageData.author === 'string' ? packageData.author : packageData.author?.name,

    // Расширенная информация
    maintainers: packageData.maintainers || [],
    contributors: packageData.contributors || [],
    keywords: packageData.keywords || [],
    readme: packageData.readme,

    // URL и ссылки
    bugs_url: packageData.bugs?.url || packageData.bugs,
    npm_url: `https://www.npmjs.com/package/${packageName}`,

    // Техническая информация
    engines: packageData.engines,
    os_compatibility: packageData.os,
    cpu_compatibility: packageData.cpu,
    funding: packageData.funding,

    // Метрики (будут обновлены позже)
    has_typescript: false,
    has_tests: false,
    test_coverage: 0,
    quality_score: 0,
    popularity_score: 0,
    maintenance_score: 0,

    // Даты
    first_release_date: packageData.time?.created,
    last_release_date: packageData.time?.modified,

    // Дополнительные поля из NPM Registry
    users: packageData.users ? Object.keys(packageData.users).length : 0,
    dist_tags: packageData['dist-tags'] || {},

    // Размеры и файлы (из последней версии)
    unpacked_size: 0,
    file_count: 0,

    versions: []
  };

  // Собираем информацию о версиях (последние 40 для GPU обработки)
  const versionKeys = Object.keys(packageData.versions || {}).slice(-40);
  let totalDependencies = 0;
  let totalDevDependencies = 0;
  let totalPeerDependencies = 0;
  let totalOptionalDependencies = 0;

  for (const versionKey of versionKeys) {
    const versionData = packageData.versions[versionKey];

    // Подсчитываем зависимости
    const depCount = Object.keys(versionData.dependencies || {}).length;
    const devDepCount = Object.keys(versionData.devDependencies || {}).length;
    const peerDepCount = Object.keys(versionData.peerDependencies || {}).length;
    const optionalDepCount = Object.keys(versionData.optionalDependencies || {}).length;

    totalDependencies += depCount;
    totalDevDependencies += devDepCount;
    totalPeerDependencies += peerDepCount;
    totalOptionalDependencies += optionalDepCount;

    // Получаем размеры из последней версии
    if (versionKey === packageInfo.latest_version && versionData.dist) {
      packageInfo.unpacked_size = versionData.dist.unpackedSize || 0;
      packageInfo.file_count = versionData.dist.fileCount || 0;
    }

    packageInfo.versions.push({
      version: versionKey,
      dependencies: versionData.dependencies || {},
      devDependencies: versionData.devDependencies || {},
      peerDependencies: versionData.peerDependencies || {},
      optionalDependencies: versionData.optionalDependencies || {},
      published_at: packageData.time?.[versionKey] || new Date().toISOString(),
      deprecated: versionData.deprecated || false,
      engines: versionData.engines,
      os: versionData.os,
      cpu: versionData.cpu,
      main: versionData.main,
      module: versionData.module,
      types: versionData.types || versionData.typings,
      scripts: versionData.scripts
    });
  }

  // Устанавливаем счетчики зависимостей
  packageInfo.version_count = versionKeys.length;
  packageInfo.dependency_count = Math.round(totalDependencies / versionKeys.length) || 0;
  packageInfo.dev_dependency_count = Math.round(totalDevDependencies / versionKeys.length) || 0;
  packageInfo.peer_dependency_count = Math.round(totalPeerDependencies / versionKeys.length) || 0;
  packageInfo.optional_dependency_count = Math.round(totalOptionalDependencies / versionKeys.length) || 0;

  // Получаем расширенную статистику загрузок
  try {
    broadcastLog('info', `📊 Получаем статистику загрузок для ${packageName}`);

    // Недельные загрузки
    const weeklyResponse = await axios.get(`https://api.npmjs.org/downloads/point/last-week/${packageName}`, {
      timeout: 5000
    });
    if (weeklyResponse.data && weeklyResponse.data.downloads) {
      packageInfo.weekly_downloads = weeklyResponse.data.downloads;
      packageInfo.download_count = weeklyResponse.data.downloads; // Обратная совместимость
    }

    // Месячные загрузки
    const monthlyResponse = await axios.get(`https://api.npmjs.org/downloads/point/last-month/${packageName}`, {
      timeout: 5000
    });
    if (monthlyResponse.data && monthlyResponse.data.downloads) {
      packageInfo.monthly_downloads = monthlyResponse.data.downloads;
    }

    // Годовые загрузки
    const yearlyResponse = await axios.get(`https://api.npmjs.org/downloads/point/last-year/${packageName}`, {
      timeout: 5000
    });
    if (yearlyResponse.data && yearlyResponse.data.downloads) {
      packageInfo.yearly_downloads = yearlyResponse.data.downloads;
    }

    broadcastLog('success', `📊 Статистика загрузок получена: ${packageInfo.weekly_downloads} в неделю`);
  } catch (downloadError) {
    broadcastLog('warning', `Не удалось получить статистику загрузок для ${packageName}: ${downloadError.message}`);
  }

  // Получаем GitHub метрики (если есть репозиторий)
  if (packageInfo.repository && packageInfo.repository.includes('github.com')) {
    try {
      broadcastLog('info', `🐙 Получаем GitHub метрики для ${packageName}`);

      const repoMatch = packageInfo.repository.match(/github\.com\/([^\/]+)\/([^\/]+)/);
      if (repoMatch) {
        const [, owner, repo] = repoMatch;
        const cleanRepo = repo.replace(/\.git$/, '');

        const githubResponse = await axios.get(`https://api.github.com/repos/${owner}/${cleanRepo}`, {
          timeout: 10000,
          headers: {
            'User-Agent': 'Package-Analyzer/1.0'
          }
        });

        if (githubResponse.data) {
          const repoData = githubResponse.data;
          packageInfo.github_stars = repoData.stargazers_count || 0;
          packageInfo.github_forks = repoData.forks_count || 0;
          packageInfo.github_issues = repoData.open_issues_count || 0;
          packageInfo.github_watchers = repoData.watchers_count || 0;
          packageInfo.last_commit_date = repoData.pushed_at;

          // Проверяем наличие TypeScript
          if (repoData.language === 'TypeScript' || (packageInfo.keywords && packageInfo.keywords.includes('typescript'))) {
            packageInfo.has_typescript = true;
          }

          broadcastLog('success', `🐙 GitHub метрики получены: ${packageInfo.github_stars} звезд, ${packageInfo.github_forks} форков`);
        }
      }
    } catch (githubError) {
      broadcastLog('warning', `Не удалось получить GitHub метрики для ${packageName}: ${githubError.message}`);
    }
  }

  // Анализируем качество пакета
  try {
    broadcastLog('info', `🔍 Анализируем качество пакета ${packageName}`);

    // Проверяем наличие тестов
    const latestVersion = packageInfo.versions[packageInfo.versions.length - 1];
    if (latestVersion && latestVersion.devDependencies) {
      const testFrameworks = ['jest', 'mocha', 'jasmine', 'ava', 'tape', 'vitest'];
      packageInfo.has_tests = testFrameworks.some(framework =>
        Object.keys(latestVersion.devDependencies).some(dep => dep.includes(framework))
      );
    }

    // Проверяем TypeScript из версий
    if (!packageInfo.has_typescript && latestVersion) {
      packageInfo.has_typescript = !!(latestVersion.types || latestVersion.typings ||
        (latestVersion.devDependencies && latestVersion.devDependencies.typescript) ||
        (latestVersion.dependencies && latestVersion.dependencies.typescript));
    }

    // Вычисляем оценки качества
    packageInfo.popularity_score = Math.min((packageInfo.weekly_downloads || 0) / 100000, 1);
    packageInfo.quality_score = (
      (packageInfo.has_typescript ? 0.2 : 0) +
      (packageInfo.has_tests ? 0.3 : 0) +
      (packageInfo.readme ? 0.2 : 0) +
      (packageInfo.license ? 0.1 : 0) +
      (packageInfo.repository ? 0.2 : 0)
    );

    // Оценка поддержки (на основе активности)
    if (packageInfo.last_commit_date) {
      const daysSinceLastCommit = (Date.now() - new Date(packageInfo.last_commit_date)) / (1000 * 60 * 60 * 24);
      packageInfo.maintenance_score = Math.max(1 - (daysSinceLastCommit / 365), 0);
    }

    // Частота релизов
    if (packageInfo.versions.length > 1) {
      const firstDate = new Date(packageInfo.first_release_date);
      const lastDate = new Date(packageInfo.last_release_date);
      const monthsDiff = (lastDate - firstDate) / (1000 * 60 * 60 * 24 * 30);
      packageInfo.release_frequency = monthsDiff > 0 ? packageInfo.versions.length / monthsDiff : 0;
    }

    broadcastLog('success', `🔍 Анализ качества завершен: ${(packageInfo.quality_score * 100).toFixed(1)}% качества`);
  } catch (analysisError) {
    broadcastLog('warning', `Ошибка анализа качества для ${packageName}: ${analysisError.message}`);
  }

  return packageInfo;
}

// Сбор одного пакета с GPU ускорением (с task управлением)
async function runSinglePackageCollection(task, packageName) {
  try {
    task.status = 'running';
    broadcastTaskUpdate(task);
    broadcastLog('info', `🚀 GPU-ускоренный сбор пакета: ${packageName}`);

    // Используем внутреннюю функцию полного сбора
    const packageInfo = await runSinglePackageCollectionInternal(packageName);

    task.status = 'completed';
    task.endTime = Date.now();
    broadcastTaskUpdate(task);

    broadcastLog('success', `Пакет ${packageName} успешно собран с полной информацией`);

    // Обновляем статистику
    systemStats.totalPackages++;
    broadcastStats();

    broadcast({
      type: 'task_complete',
      taskName: task.name,
      gpuAccelerated: true
    });

  } catch (error) {
    task.status = 'failed';
    task.error = error.message;
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('error', `Ошибка сбора пакета ${packageName}: ${error.message}`);
  }
}

// Сбор нескольких пакетов
async function runMultiplePackageCollection(task, packages) {
  try {
    task.status = 'running';
    broadcastTaskUpdate(task);
    broadcastLog('info', `Начинаем сбор ${packages.length} пакетов`);

    let completed = 0;
    const total = packages.length;

    for (const packageName of packages) {
      try {
        // Проверяем, не остановлена ли задача
        if (task.status === 'stopped') {
          break;
        }

        broadcastLog('info', `Обрабатываем пакет: ${packageName} (${completed + 1}/${total})`);

        // Используем полную функцию сбора для каждого пакета
        await runSinglePackageCollectionInternal(packageName);

        completed++;

        // Отправляем прогресс
        const progress = (completed / total) * 100;
        broadcast({
          type: 'progress',
          progress: progress,
          details: `Обработано ${completed} из ${total} пакетов`
        });

        // Небольшая задержка чтобы не перегружать npm registry
        await new Promise(resolve => setTimeout(resolve, 100));

      } catch (error) {
        broadcastLog('warning', `Ошибка обработки пакета ${packageName}: ${error.message}`);
        completed++;
      }
    }

    task.status = task.status === 'stopped' ? 'stopped' : 'completed';
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('success', `Завершен сбор пакетов. Обработано: ${completed}/${total}`);

    // Обновляем статистику
    const stats = await getDatabaseStats();
    systemStats.totalPackages = stats.totalPackages;
    broadcastStats();

    broadcast({
      type: 'task_complete',
      taskName: task.name
    });

  } catch (error) {
    task.status = 'failed';
    task.error = error.message;
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('error', `Ошибка сбора пакетов: ${error.message}`);
  }
}

// Сбор популярных пакетов
async function runPopularPackageCollection(task, limit) {
  try {
    task.status = 'running';
    broadcastTaskUpdate(task);
    broadcastLog('info', `Начинаем сбор топ-${limit} популярных пакетов`);

    // Список популярных пакетов (в реальном приложении можно получать из API)
    const popularPackages = [
      'react', 'vue', 'angular', 'express', 'lodash', 'axios', 'webpack', 'babel-core',
      'typescript', 'eslint', 'prettier', 'jest', 'mocha', 'chai', 'sinon', 'moment',
      'jquery', 'bootstrap', 'material-ui', 'ant-design', 'styled-components', 'emotion',
      'redux', 'mobx', 'rxjs', 'socket.io', 'passport', 'bcrypt', 'jsonwebtoken',
      'mongoose', 'sequelize', 'prisma', 'typeorm', 'knex', 'pg', 'mysql2', 'redis',
      'nodemon', 'pm2', 'forever', 'cross-env', 'dotenv', 'config', 'yargs', 'commander',
      'chalk', 'inquirer', 'ora', 'progress', 'debug', 'winston', 'morgan', 'helmet',
      'cors', 'body-parser', 'multer', 'sharp', 'jimp', 'pdf2pic', 'puppeteer', 'playwright'
    ];

    const packagesToCollect = popularPackages.slice(0, Math.min(limit, popularPackages.length));
    await runMultiplePackageCollection(task, packagesToCollect);

  } catch (error) {
    task.status = 'failed';
    task.error = error.message;
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('error', `Ошибка сбора популярных пакетов: ${error.message}`);
  }
}

// Сбор по категориям
async function runCategoryCollection(task, categories) {
  try {
    task.status = 'running';
    broadcastTaskUpdate(task);
    broadcastLog('info', `Начинаем сбор пакетов по категориям: ${categories.join(', ')}`);

    // Пакеты по категориям
    const categoryPackages = {
      'framework': ['react', 'vue', 'angular', 'svelte', 'ember'],
      'library': ['lodash', 'underscore', 'ramda', 'immutable'],
      'utility': ['moment', 'date-fns', 'uuid', 'validator', 'sanitize-html'],
      'build-tool': ['webpack', 'rollup', 'parcel', 'vite', 'esbuild'],
      'testing': ['jest', 'mocha', 'jasmine', 'karma', 'cypress'],
      'react': ['react-dom', 'react-router', 'react-redux', 'react-hook-form'],
      'vue': ['vue-router', 'vuex', 'nuxt', 'quasar'],
      'angular': ['@angular/core', '@angular/common', '@angular/router'],
      'node': ['express', 'koa', 'fastify', 'hapi'],
      'webpack': ['webpack-cli', 'webpack-dev-server', 'html-webpack-plugin'],
      'babel': ['@babel/core', '@babel/preset-env', '@babel/preset-react']
    };

    let allPackages = [];
    categories.forEach(category => {
      if (categoryPackages[category]) {
        allPackages = allPackages.concat(categoryPackages[category]);
      }
    });

    // Удаляем дубликаты
    allPackages = [...new Set(allPackages)];

    await runMultiplePackageCollection(task, allPackages);

  } catch (error) {
    task.status = 'failed';
    task.error = error.message;
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('error', `Ошибка сбора по категориям: ${error.message}`);
  }
}

// Обновление существующих пакетов
async function runPackageUpdate(task) {
  try {
    task.status = 'running';
    broadcastTaskUpdate(task);
    broadcastLog('info', 'Начинаем обновление существующих пакетов');

    // Получаем список всех пакетов из базы данных
    const packages = await getPackagesFromDB('', 1000, 0);
    const packageNames = packages.map(pkg => pkg.name);

    await runMultiplePackageCollection(task, packageNames);

  } catch (error) {
    task.status = 'failed';
    task.error = error.message;
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('error', `Ошибка обновления пакетов: ${error.message}`);
  }
}

// Массовый сбор ВСЕХ npm пакетов
async function runMassiveNpmCollection(task) {
  try {
    task.status = 'running';
    broadcastTaskUpdate(task);
    broadcastLog('info', '🌍 Запуск массового сбора ВСЕХ npm пакетов из регистра...');

    // Инициализируем массовый сборщик
    const MassiveNpmCollector = require('./scripts/massive-npm-collector');
    const collector = new MassiveNpmCollector();

    // Запускаем сбор с отслеживанием прогресса
    const progressInterval = setInterval(async () => {
      const stats = collector.getStats();

      broadcast({
        type: 'massive_collection_progress',
        progress: stats.progress,
        processedPackages: stats.processedPackages,
        totalPackages: stats.totalPackages,
        failedPackages: stats.failedPackages,
        remainingPackages: stats.remainingPackages
      });

      broadcastLog('info', `📊 Прогресс массового сбора: ${stats.progress.toFixed(2)}% (${stats.processedPackages}/${stats.totalPackages})`);
    }, 10000); // Каждые 10 секунд

    try {
      await collector.startMassiveCollection();
    } finally {
      clearInterval(progressInterval);
    }

    task.status = 'completed';
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('success', '🎉 Массовый сбор всех npm пакетов завершен!');

    // Обновляем статистику
    const dbStats = await getDatabaseStats();
    systemStats.totalPackages = dbStats.totalPackages;
    broadcastStats();

    broadcast({
      type: 'massive_collection_complete',
      taskName: task.name,
      totalPackages: dbStats.totalPackages,
      processedPackages: collector.processedPackages,
      failedPackages: collector.failedPackages
    });

  } catch (error) {
    task.status = 'failed';
    task.error = error.message;
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('error', `Ошибка массового сбора: ${error.message}`);
  }
}

// Автоматический сбор
async function runPackageCollection(task, mode) {
  try {
    task.status = 'running';
    broadcastTaskUpdate(task);
    broadcastLog('info', 'Начинаем автоматический сбор пакетов');

    // Автоматически собираем популярные пакеты
    await runPopularPackageCollection(task, 100);

  } catch (error) {
    task.status = 'failed';
    task.error = error.message;
    task.endTime = Date.now();
    broadcastTaskUpdate(task);
    broadcastLog('error', `Ошибка автоматического сбора: ${error.message}`);
  }
}

// Загрузка статистики из базы данных
async function loadDatabaseStats() {
  try {
    const stats = await getDatabaseStats();
    systemStats.totalPackages = stats.totalPackages;
    console.log(`📊 Загружено статистики: ${stats.totalPackages} пакетов в БД`);
  } catch (error) {
    console.error('Ошибка загрузки статистики из БД:', error);
  }
}

// Запуск сервера
const PORT = process.env.PORT || 3000;

async function startServer() {
  try {
    console.log('🔧 Инициализация базы данных...');
    await initializeDatabase();
    console.log('📊 Загрузка статистики...');
    await loadDatabaseStats();

    // Serve frontend - добавляем в конце после всех API маршрутов
    app.get('/', (req, res) => {
      res.sendFile(path.join(__dirname, 'public', 'index.html'));
    });

    server.listen(PORT, () => {
      console.log(`🌐 Сервер запущен на порту ${PORT}`);
      console.log(`🎛️ Панель управления: http://localhost:${PORT}`);
      broadcastLog('success', `Сервер запущен на порту ${PORT}`);
    });

    // Запускаем периодическое обновление метрик
    setInterval(updateSystemMetrics, 5000);

    // Добавляем начальную задачу
    const initTask = {
      id: taskIdCounter++,
      name: 'Инициализация системы',
      status: 'completed',
      startTime: systemStats.startTime,
      endTime: Date.now()
    };
    taskQueue.push(initTask);

  } catch (error) {
    console.error('Ошибка запуска сервера:', error);
    process.exit(1);
  }
}

// Обработка завершения процесса
process.on('SIGINT', () => {
  console.log('Получен сигнал SIGINT, завершаем работу...');
  broadcastLog('warning', 'Сервер завершает работу...');

  // Закрываем WebSocket соединения
  wss.clients.forEach(client => {
    client.close();
  });

  // Закрываем базу данных
  db.close();

  server.close(() => {
    console.log('Сервер остановлен');
    process.exit(0);
  });
});

startServer();
