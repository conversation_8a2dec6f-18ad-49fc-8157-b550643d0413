const axios = require('axios');

async function testAddPackage() {
  try {
    console.log('🧪 Тестируем добавление пакета через API...');
    
    const response = await axios.post('http://localhost:3000/api/collect/package', {
      packageName: 'chalk'
    });
    
    console.log('✅ Ответ сервера:', response.data);
    
    // Ждем немного для завершения обработки
    setTimeout(async () => {
      try {
        const packagesResponse = await axios.get('http://localhost:3000/api/packages?search=chalk');
        console.log('📦 Найденные пакеты:', packagesResponse.data);
      } catch (error) {
        console.error('❌ Ошибка поиска пакета:', error.message);
      }
    }, 3000);
    
  } catch (error) {
    console.error('❌ Ошибка тестирования:', error.message);
  }
}

testAddPackage();
