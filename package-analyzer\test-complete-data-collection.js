// Тест полного сбора всей доступной информации о пакетах
const axios = require('axios');

async function testCompleteDataCollection() {
  console.log('🎯 Тестирование полного сбора всей доступной информации...');
  
  const testPackages = ['lodash', 'express', 'axios']; // Популярные пакеты с хорошими метриками
  const baseUrl = 'http://localhost:3000';
  
  try {
    console.log(`📦 Тестируем полный сбор для ${testPackages.length} пакетов...`);
    
    for (let i = 0; i < testPackages.length; i++) {
      const packageName = testPackages[i];
      console.log(`\n🚀 [${i + 1}/${testPackages.length}] Полный сбор пакета: ${packageName}`);
      
      try {
        // Запускаем сбор пакета
        const response = await axios.post(`${baseUrl}/api/collect-package`, {
          packageName: packageName
        });
        
        if (response.data.success) {
          console.log(`✅ Пакет ${packageName} добавлен в очередь (Task ID: ${response.data.taskId})`);
          
          // Ждем завершения обработки (увеличиваем время ожидания)
          console.log('⏳ Ожидаем завершения полного сбора...');
          await new Promise(resolve => setTimeout(resolve, 15000)); // 15 секунд
          
          // Получаем детальную информацию о пакете
          console.log('📊 Получаем полную информацию из БД...');
          const detailsResponse = await axios.get(`${baseUrl}/api/packages/${packageName}`);
          
          if (detailsResponse.data) {
            const pkg = detailsResponse.data;
            
            console.log(`\n📋 ПОЛНАЯ ИНФОРМАЦИЯ О ПАКЕТЕ ${packageName.toUpperCase()}:`);
            console.log('=' .repeat(60));
            
            // Проверяем полноту данных
            const completenessReport = analyzeDataCompleteness(pkg);
            
            console.log('\n📊 АНАЛИЗ ПОЛНОТЫ ДАННЫХ:');
            console.log(`📈 Общая полнота: ${completenessReport.overall.toFixed(1)}%`);
            console.log(`📦 Основная информация: ${completenessReport.basic.toFixed(1)}%`);
            console.log(`📊 Статистика загрузок: ${completenessReport.downloads.toFixed(1)}%`);
            console.log(`🐙 GitHub метрики: ${completenessReport.github.toFixed(1)}%`);
            console.log(`🎯 Оценки качества: ${completenessReport.quality.toFixed(1)}%`);
            console.log(`⚙️ Технические данные: ${completenessReport.technical.toFixed(1)}%`);
            console.log(`📅 Временные метки: ${completenessReport.dates.toFixed(1)}%`);
            
            // Детальная информация
            console.log('\n📦 ОСНОВНАЯ ИНФОРМАЦИЯ:');
            console.log(`   📝 Название: ${pkg.name}`);
            console.log(`   📄 Описание: ${pkg.description ? '✅ Есть' : '❌ Нет'}`);
            console.log(`   📦 Последняя версия: ${pkg.latest_version || 'N/A'}`);
            console.log(`   👤 Автор: ${pkg.author || 'N/A'}`);
            console.log(`   ⚖️ Лицензия: ${pkg.license || 'N/A'}`);
            
            // Команда разработки
            console.log('\n👥 КОМАНДА РАЗРАБОТКИ:');
            if (pkg.maintainers) {
              try {
                const maintainers = JSON.parse(pkg.maintainers);
                console.log(`   👨‍💻 Maintainers: ${maintainers.length} человек`);
                maintainers.slice(0, 3).forEach((m, idx) => {
                  const name = typeof m === 'string' ? m : m.name;
                  console.log(`     ${idx + 1}. ${name}`);
                });
              } catch (e) {
                console.log(`   👨‍💻 Maintainers: ${pkg.maintainers ? 'есть данные' : 'нет данных'}`);
              }
            } else {
              console.log(`   👨‍💻 Maintainers: нет данных`);
            }
            
            // Статистика загрузок
            console.log('\n📊 СТАТИСТИКА ЗАГРУЗОК:');
            console.log(`   📅 Неделя: ${(pkg.weekly_downloads || 0).toLocaleString()}`);
            console.log(`   📅 Месяц: ${(pkg.monthly_downloads || 0).toLocaleString()}`);
            console.log(`   📅 Год: ${(pkg.yearly_downloads || 0).toLocaleString()}`);
            
            // GitHub метрики
            console.log('\n🐙 GITHUB МЕТРИКИ:');
            console.log(`   ⭐ Звезды: ${(pkg.github_stars || 0).toLocaleString()}`);
            console.log(`   🍴 Форки: ${(pkg.github_forks || 0).toLocaleString()}`);
            console.log(`   🐛 Issues: ${(pkg.github_issues || 0).toLocaleString()}`);
            console.log(`   👀 Watchers: ${(pkg.github_watchers || 0).toLocaleString()}`);
            
            // Оценки качества
            console.log('\n🎯 ОЦЕНКИ КАЧЕСТВА:');
            console.log(`   🏆 Качество: ${((pkg.quality_score || 0) * 100).toFixed(1)}%`);
            console.log(`   📈 Популярность: ${((pkg.popularity_score || 0) * 100).toFixed(1)}%`);
            console.log(`   🔧 Поддержка: ${((pkg.maintenance_score || 0) * 100).toFixed(1)}%`);
            
            // Технические характеристики
            console.log('\n⚙️ ТЕХНИЧЕСКИЕ ХАРАКТЕРИСТИКИ:');
            console.log(`   📝 TypeScript: ${pkg.has_typescript ? '✅ Да' : '❌ Нет'}`);
            console.log(`   🧪 Тесты: ${pkg.has_tests ? '✅ Да' : '❌ Нет'}`);
            console.log(`   📊 Покрытие тестами: ${((pkg.test_coverage || 0) * 100).toFixed(1)}%`);
            console.log(`   📦 Версий: ${pkg.version_count || 0}`);
            console.log(`   🔗 Зависимостей: ${pkg.dependency_count || 0}`);
            console.log(`   📁 Файлов: ${pkg.file_count || 0}`);
            console.log(`   📦 Размер (unpacked): ${(pkg.unpacked_size || 0).toLocaleString()} байт`);
            
            // Временные метки
            console.log('\n📅 ВРЕМЕННЫЕ МЕТКИ:');
            console.log(`   🎂 Первый релиз: ${pkg.first_release_date ? new Date(pkg.first_release_date).toLocaleDateString() : 'N/A'}`);
            console.log(`   📅 Последний релиз: ${pkg.last_release_date ? new Date(pkg.last_release_date).toLocaleDateString() : 'N/A'}`);
            console.log(`   💻 Последний коммит: ${pkg.last_commit_date ? new Date(pkg.last_commit_date).toLocaleDateString() : 'N/A'}`);
            console.log(`   🔄 Частота релизов: ${(pkg.release_frequency || 0).toFixed(2)} релизов/месяц`);
            
            // Совместимость и дополнительные данные
            console.log('\n🔧 СОВМЕСТИМОСТЬ И ДОПОЛНИТЕЛЬНЫЕ ДАННЫЕ:');
            if (pkg.engines) {
              try {
                const engines = JSON.parse(pkg.engines);
                console.log(`   🚀 Engines: ${JSON.stringify(engines)}`);
              } catch (e) {
                console.log(`   🚀 Engines: ${pkg.engines}`);
              }
            } else {
              console.log(`   🚀 Engines: не указано`);
            }
            
            if (pkg.funding) {
              try {
                const funding = JSON.parse(pkg.funding);
                console.log(`   💳 Funding: ${JSON.stringify(funding)}`);
              } catch (e) {
                console.log(`   💳 Funding: ${pkg.funding}`);
              }
            } else {
              console.log(`   💳 Funding: не указано`);
            }
            
            // Версии
            if (pkg.versions && pkg.versions.length > 0) {
              console.log('\n📦 ПОСЛЕДНИЕ ВЕРСИИ:');
              pkg.versions.slice(0, 5).forEach((version, index) => {
                const depCount = Object.keys(version.dependencies || {}).length;
                const devDepCount = Object.keys(version.devDependencies || {}).length;
                const peerDepCount = Object.keys(version.peerDependencies || {}).length;
                const optionalDepCount = Object.keys(version.optionalDependencies || {}).length;
                
                console.log(`   ${index + 1}. ${version.version} (${new Date(version.published_at).toLocaleDateString()})`);
                console.log(`      🔗 ${depCount} зависимостей, ${devDepCount} dev, ${peerDepCount} peer, ${optionalDepCount} optional`);
                
                if (version.deprecated) {
                  console.log(`      ⚠️ DEPRECATED`);
                }
              });
            }
            
            // Ключевые слова
            if (pkg.keywords && pkg.keywords.length > 0) {
              console.log('\n🏷️ КЛЮЧЕВЫЕ СЛОВА:');
              console.log(`   ${pkg.keywords.slice(0, 10).join(', ')}`);
              if (pkg.keywords.length > 10) {
                console.log(`   ... и еще ${pkg.keywords.length - 10} ключевых слов`);
              }
            }
            
            // Итоговая оценка
            console.log('\n🎯 ИТОГОВАЯ ОЦЕНКА:');
            console.log(`📊 Полнота данных: ${completenessReport.overall.toFixed(1)}%`);
            console.log(`🎮 GPU обработка: активна`);
            console.log(`✅ Статус: ${completenessReport.overall > 70 ? 'отлично' : completenessReport.overall > 50 ? 'хорошо' : 'требует улучшения'}`);
            
          } else {
            console.log('❌ Не удалось получить детальную информацию о пакете');
          }
        } else {
          console.log('❌ Ошибка добавления пакета в очередь');
        }
        
        // Пауза между пакетами
        if (i < testPackages.length - 1) {
          console.log('\n⏸️ Пауза перед следующим пакетом...');
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
        
      } catch (error) {
        console.error(`❌ Ошибка обработки пакета ${packageName}:`, error.message);
      }
    }
    
    console.log('\n🎉 Тестирование полного сбора завершено!');
    
  } catch (error) {
    console.error('❌ Ошибка тестирования:', error.message);
  }
}

function analyzeDataCompleteness(pkg) {
  // Основная информация (6 полей)
  const basicFields = ['name', 'description', 'latest_version', 'author', 'license', 'repository'];
  const basicFilled = basicFields.filter(field => pkg[field] && pkg[field] !== 'N/A').length;
  const basicCompleteness = (basicFilled / basicFields.length) * 100;
  
  // Статистика загрузок (3 поля)
  const downloadFields = ['weekly_downloads', 'monthly_downloads', 'yearly_downloads'];
  const downloadFilled = downloadFields.filter(field => pkg[field] && pkg[field] > 0).length;
  const downloadCompleteness = (downloadFilled / downloadFields.length) * 100;
  
  // GitHub метрики (4 поля)
  const githubFields = ['github_stars', 'github_forks', 'github_issues', 'github_watchers'];
  const githubFilled = githubFields.filter(field => pkg[field] && pkg[field] > 0).length;
  const githubCompleteness = (githubFilled / githubFields.length) * 100;
  
  // Оценки качества (3 поля)
  const qualityFields = ['quality_score', 'popularity_score', 'maintenance_score'];
  const qualityFilled = qualityFields.filter(field => pkg[field] && pkg[field] > 0).length;
  const qualityCompleteness = (qualityFilled / qualityFields.length) * 100;
  
  // Технические данные (5 полей)
  const technicalFields = ['has_typescript', 'has_tests', 'version_count', 'dependency_count', 'file_count'];
  const technicalFilled = technicalFields.filter(field => 
    pkg[field] !== undefined && pkg[field] !== null && pkg[field] !== 0
  ).length;
  const technicalCompleteness = (technicalFilled / technicalFields.length) * 100;
  
  // Временные метки (3 поля)
  const dateFields = ['first_release_date', 'last_release_date', 'last_commit_date'];
  const dateFilled = dateFields.filter(field => pkg[field] && pkg[field] !== 'N/A').length;
  const dateCompleteness = (dateFilled / dateFields.length) * 100;
  
  // Общая полнота
  const overallCompleteness = (
    basicCompleteness + downloadCompleteness + githubCompleteness + 
    qualityCompleteness + technicalCompleteness + dateCompleteness
  ) / 6;
  
  return {
    overall: overallCompleteness,
    basic: basicCompleteness,
    downloads: downloadCompleteness,
    github: githubCompleteness,
    quality: qualityCompleteness,
    technical: technicalCompleteness,
    dates: dateCompleteness
  };
}

// Запускаем тест
if (require.main === module) {
  testCompleteDataCollection().then(() => {
    console.log('\n🎉 Тестирование полного сбора данных завершено!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
}

module.exports = { testCompleteDataCollection };
