// Тест комплексного сбора пакетов с полной информацией
const axios = require('axios');

async function testComprehensiveCollection() {
  console.log('🎯 Тестирование комплексного сбора пакетов...');
  
  const popularPackages = [
    'react',
    'vue', 
    'express',
    'lodash',
    'axios'
  ];
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    console.log(`📦 Собираем ${popularPackages.length} популярных пакетов с полной информацией...`);
    
    const startTime = Date.now();
    
    // Собираем пакеты последовательно для демонстрации полной информации
    for (let i = 0; i < popularPackages.length; i++) {
      const packageName = popularPackages[i];
      console.log(`\n🚀 [${i + 1}/${popularPackages.length}] Собираем пакет: ${packageName}`);
      
      try {
        const response = await axios.post(`${baseUrl}/api/collect-package`, {
          packageName: packageName
        });
        
        if (response.data.success) {
          console.log(`✅ Пакет ${packageName} добавлен в очередь (Task ID: ${response.data.taskId})`);
          
          // Ждем немного для завершения обработки
          await new Promise(resolve => setTimeout(resolve, 3000));
          
          // Получаем детальную информацию о пакете
          const detailsResponse = await axios.get(`${baseUrl}/api/packages/${packageName}`);
          
          if (detailsResponse.data) {
            const pkg = detailsResponse.data;
            console.log(`📊 Информация о ${packageName}:`);
            console.log(`   📝 Описание: ${pkg.description ? pkg.description.substring(0, 100) + '...' : 'N/A'}`);
            console.log(`   📦 Последняя версия: ${pkg.latest_version || 'N/A'}`);
            console.log(`   👤 Автор: ${pkg.author || 'N/A'}`);
            console.log(`   📊 Загрузки в неделю: ${(pkg.weekly_downloads || 0).toLocaleString()}`);
            console.log(`   ⭐ GitHub звезды: ${(pkg.github_stars || 0).toLocaleString()}`);
            console.log(`   🏆 Качество: ${((pkg.quality_score || 0) * 100).toFixed(1)}%`);
            console.log(`   📈 Популярность: ${((pkg.popularity_score || 0) * 100).toFixed(1)}%`);
            console.log(`   🔧 Поддержка: ${((pkg.maintenance_score || 0) * 100).toFixed(1)}%`);
            console.log(`   📝 TypeScript: ${pkg.has_typescript ? '✅' : '❌'}`);
            console.log(`   🧪 Тесты: ${pkg.has_tests ? '✅' : '❌'}`);
            console.log(`   📦 Версий: ${pkg.version_count || 0}`);
            console.log(`   🔗 Зависимостей: ${pkg.dependency_count || 0}`);
            
            if (pkg.versions && pkg.versions.length > 0) {
              console.log(`   📋 Последние версии: ${pkg.versions.slice(0, 3).map(v => v.version).join(', ')}`);
            }
          }
        } else {
          console.log(`❌ Ошибка добавления ${packageName}`);
        }
      } catch (error) {
        console.error(`❌ Ошибка обработки ${packageName}:`, error.message);
      }
    }
    
    const totalTime = Date.now() - startTime;
    
    console.log('\n🎯 Результаты комплексного сбора:');
    console.log(`⏱️  Общее время: ${totalTime}мс`);
    console.log(`⚡ Среднее время на пакет: ${Math.round(totalTime / popularPackages.length)}мс`);
    
    // Тестируем анализ package.json с обновленными данными
    console.log('\n🔍 Тестирование расширенного анализа package.json...');
    await testEnhancedAnalysis();
    
  } catch (error) {
    console.error('❌ Ошибка тестирования:', error.message);
  }
}

async function testEnhancedAnalysis() {
  const fs = require('fs');
  const path = require('path');
  
  try {
    // Создаем тестовый package.json с собранными пакетами
    const testPackageJson = {
      "name": "test-project",
      "version": "1.0.0",
      "description": "Тестовый проект для анализа",
      "dependencies": {
        "react": "^18.0.0",
        "express": "^4.18.0",
        "lodash": "^4.17.21",
        "axios": "^1.0.0"
      },
      "devDependencies": {
        "vue": "^3.0.0"
      }
    };
    
    console.log('📄 Анализируем тестовый package.json с расширенной информацией...');
    
    const startTime = Date.now();
    
    const response = await axios.post('http://localhost:3000/api/analyze', {
      filePath: 'test-enhanced-package.json',
      content: JSON.stringify(testPackageJson, null, 2)
    });
    
    const analysisTime = Date.now() - startTime;
    const result = response.data;
    
    console.log('\n🎯 Результаты расширенного анализа:');
    console.log(`⏱️  Время анализа: ${analysisTime}мс`);
    console.log(`🎮 GPU ускорение: ${result.analysis?.gpuAccelerated ? 'АКТИВНО' : 'НЕ АКТИВНО'}`);
    
    if (result.dependencies) {
      console.log(`📊 Всего зависимостей: ${result.dependencies.total || 0}`);
      console.log(`✅ Известных пакетов: ${result.dependencies.known || 0}`);
      console.log(`❓ Неизвестных пакетов: ${result.dependencies.unknown || 0}`);
    }
    
    if (result.analysis) {
      console.log(`⚠️  Уровень риска: ${result.analysis.riskLevel || 'unknown'}`);
      console.log(`🔒 Проблемы безопасности: ${result.analysis.securityIssues || 0}`);
      console.log(`📅 Устаревшие пакеты: ${result.analysis.outdatedPackages || 0}`);
      console.log(`🏆 Проблемы качества: ${result.analysis.qualityIssues || 0}`);
      console.log(`🔧 Проблемы поддержки: ${result.analysis.maintenanceIssues || 0}`);
      console.log(`📋 Всего проблем: ${result.analysis.totalIssues || 0}`);
    }
    
    // Показываем детальную информацию о зависимостях
    if (result.knownPackages && result.knownPackages.length > 0) {
      console.log('\n📦 Детальная информация о зависимостях:');
      result.knownPackages.forEach((pkg, index) => {
        console.log(`\n${index + 1}. ${pkg.name} (${pkg.dependency_type})`);
        console.log(`   📦 Запрошенная версия: ${pkg.requested_version}`);
        console.log(`   📦 Последняя версия: ${pkg.latest_version}`);
        console.log(`   📊 Загрузки в неделю: ${(pkg.weekly_downloads || 0).toLocaleString()}`);
        console.log(`   ⭐ GitHub звезды: ${(pkg.github_stars || 0).toLocaleString()}`);
        console.log(`   🏆 Качество: ${((pkg.quality_score || 0) * 100).toFixed(1)}%`);
        console.log(`   📈 Популярность: ${((pkg.popularity_score || 0) * 100).toFixed(1)}%`);
        console.log(`   🔧 Поддержка: ${((pkg.maintenance_score || 0) * 100).toFixed(1)}%`);
        console.log(`   📝 TypeScript: ${pkg.has_typescript ? '✅' : '❌'}`);
        console.log(`   🧪 Тесты: ${pkg.has_tests ? '✅' : '❌'}`);
        console.log(`   🔒 Уязвимости: ${pkg.vulnerabilities_count || 0}`);
      });
    }
    
    // Показываем проблемы и рекомендации
    if (result.potentialIssues && result.potentialIssues.length > 0) {
      console.log('\n⚠️  Обнаруженные проблемы:');
      result.potentialIssues.forEach((issue, index) => {
        const severityIcon = issue.severity === 'critical' ? '🔴' : 
                           issue.severity === 'high' ? '🟠' : 
                           issue.severity === 'medium' ? '🟡' : '🟢';
        console.log(`${index + 1}. ${severityIcon} ${issue.package}: ${issue.message}`);
        console.log(`   💡 Рекомендация: ${issue.recommendation}`);
      });
    }
    
    if (result.recommendations && result.recommendations.length > 0) {
      console.log('\n💡 Рекомендации по улучшению:');
      result.recommendations.forEach((rec, index) => {
        const priorityIcon = rec.priority === 'high' ? '🔴' : 
                           rec.priority === 'medium' ? '🟡' : '🟢';
        console.log(`${index + 1}. ${priorityIcon} ${rec.package}: ${rec.message}`);
        console.log(`   💡 ${rec.recommendation}`);
      });
    }
    
    console.log('\n✅ Расширенный анализ завершен успешно!');
    
  } catch (error) {
    console.error('❌ Ошибка расширенного анализа:', error.message);
  }
}

// Запускаем тест
if (require.main === module) {
  testComprehensiveCollection().then(() => {
    console.log('\n🎉 Комплексное тестирование завершено!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
}

module.exports = { testComprehensiveCollection, testEnhancedAnalysis };
