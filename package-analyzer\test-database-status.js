// Проверка состояния базы данных и полноты информации
const Database = require('./db/database');
const path = require('path');

async function checkDatabaseStatus() {
  console.log('🔍 Проверка состояния базы данных...');
  
  const db = new Database();
  await db.initialize();
  
  try {
    // Получаем общую статистику
    const packagesList = await db.getPackagesList('', 100, 0);
    console.log(`📊 Всего пакетов в БД: ${packagesList.pagination.total}`);
    
    if (packagesList.packages.length > 0) {
      console.log('\n📦 Анализ полноты данных:');
      
      let totalPackages = packagesList.packages.length;
      let withDescription = 0;
      let withRepository = 0;
      let withDownloads = 0;
      let withGitHubStars = 0;
      let withQualityScore = 0;
      let withVersions = 0;
      let withTypeScript = 0;
      let withTests = 0;
      
      // Анализируем каждый пакет
      for (const pkg of packagesList.packages.slice(0, 20)) {
        const details = await db.getPackageDetails(pkg.name);
        
        if (details) {
          if (details.description) withDescription++;
          if (details.repository) withRepository++;
          if (details.weekly_downloads > 0) withDownloads++;
          if (details.github_stars > 0) withGitHubStars++;
          if (details.quality_score > 0) withQualityScore++;
          if (details.versions && details.versions.length > 0) withVersions++;
          if (details.has_typescript) withTypeScript++;
          if (details.has_tests) withTests++;
          
          // Показываем детали первых 5 пакетов
          if (packagesList.packages.indexOf(pkg) < 5) {
            console.log(`\n📋 ${details.name}:`);
            console.log(`   📝 Описание: ${details.description ? '✅' : '❌'}`);
            console.log(`   🔗 Репозиторий: ${details.repository ? '✅' : '❌'}`);
            console.log(`   📊 Загрузки: ${details.weekly_downloads || 0} в неделю`);
            console.log(`   ⭐ GitHub звезды: ${details.github_stars || 0}`);
            console.log(`   🏆 Качество: ${((details.quality_score || 0) * 100).toFixed(1)}%`);
            console.log(`   📈 Популярность: ${((details.popularity_score || 0) * 100).toFixed(1)}%`);
            console.log(`   🔧 Поддержка: ${((details.maintenance_score || 0) * 100).toFixed(1)}%`);
            console.log(`   📝 TypeScript: ${details.has_typescript ? '✅' : '❌'}`);
            console.log(`   🧪 Тесты: ${details.has_tests ? '✅' : '❌'}`);
            console.log(`   📦 Версий: ${details.versions ? details.versions.length : 0}`);
            
            if (details.maintainers) {
              try {
                const maintainers = JSON.parse(details.maintainers);
                console.log(`   👥 Maintainers: ${maintainers.length}`);
              } catch (e) {
                console.log(`   👥 Maintainers: ${details.maintainers ? 'есть' : 'нет'}`);
              }
            }
          }
        }
      }
      
      const sampleSize = Math.min(20, totalPackages);
      console.log(`\n📈 Статистика полноты данных (выборка ${sampleSize} пакетов):`);
      console.log(`📝 Описание: ${((withDescription / sampleSize) * 100).toFixed(1)}%`);
      console.log(`🔗 Репозиторий: ${((withRepository / sampleSize) * 100).toFixed(1)}%`);
      console.log(`📊 Статистика загрузок: ${((withDownloads / sampleSize) * 100).toFixed(1)}%`);
      console.log(`⭐ GitHub метрики: ${((withGitHubStars / sampleSize) * 100).toFixed(1)}%`);
      console.log(`🏆 Оценка качества: ${((withQualityScore / sampleSize) * 100).toFixed(1)}%`);
      console.log(`📦 Информация о версиях: ${((withVersions / sampleSize) * 100).toFixed(1)}%`);
      console.log(`📝 TypeScript поддержка: ${((withTypeScript / sampleSize) * 100).toFixed(1)}%`);
      console.log(`🧪 Наличие тестов: ${((withTests / sampleSize) * 100).toFixed(1)}%`);
    }
    
    // Проверяем структуру таблиц
    console.log('\n🗄️ Проверка структуры таблиц:');
    await checkTableStructure(db);
    
    // Тестируем расширенный анализ
    console.log('\n🔍 Тестирование расширенного анализа зависимостей:');
    await testEnhancedDependencyAnalysis(db);
    
  } catch (error) {
    console.error('❌ Ошибка проверки БД:', error);
  } finally {
    db.close();
  }
}

async function checkTableStructure(db) {
  return new Promise((resolve, reject) => {
    // Проверяем структуру таблицы packages
    db.db.all("PRAGMA table_info(packages)", (err, columns) => {
      if (err) {
        console.error('❌ Ошибка получения структуры таблицы packages:', err);
        reject(err);
        return;
      }
      
      console.log(`📋 Таблица packages: ${columns.length} колонок`);
      
      const importantColumns = [
        'weekly_downloads', 'monthly_downloads', 'yearly_downloads',
        'github_stars', 'github_forks', 'github_issues',
        'quality_score', 'popularity_score', 'maintenance_score',
        'has_typescript', 'has_tests', 'maintainers'
      ];
      
      const existingColumns = columns.map(col => col.name);
      const missingColumns = importantColumns.filter(col => !existingColumns.includes(col));
      
      if (missingColumns.length === 0) {
        console.log('✅ Все необходимые колонки присутствуют');
      } else {
        console.log(`⚠️ Отсутствующие колонки: ${missingColumns.join(', ')}`);
      }
      
      // Проверяем другие таблицы
      db.db.all("SELECT name FROM sqlite_master WHERE type='table'", (err, tables) => {
        if (err) {
          reject(err);
          return;
        }
        
        const tableNames = tables.map(t => t.name);
        console.log(`🗂️ Всего таблиц: ${tableNames.length}`);
        console.log(`📋 Таблицы: ${tableNames.join(', ')}`);
        
        const requiredTables = ['packages', 'versions', 'dependencies', 'vulnerabilities', 'package_metrics'];
        const missingTables = requiredTables.filter(table => !tableNames.includes(table));
        
        if (missingTables.length === 0) {
          console.log('✅ Все необходимые таблицы присутствуют');
        } else {
          console.log(`⚠️ Отсутствующие таблицы: ${missingTables.join(', ')}`);
        }
        
        resolve();
      });
    });
  });
}

async function testEnhancedDependencyAnalysis(db) {
  try {
    const testDependencies = {
      'react': '^18.0.0',
      'express': '^4.18.0',
      'lodash': '^4.17.21'
    };
    
    const testDevDependencies = {
      'typescript': '^5.0.0',
      'jest': '^29.0.0'
    };
    
    console.log('📊 Анализируем тестовые зависимости...');
    
    const result = await db.analyzeDependencies(testDependencies, testDevDependencies);
    
    console.log(`📦 Всего зависимостей: ${result.totalDependencies}`);
    console.log(`✅ Известных пакетов: ${result.knownPackages.length}`);
    console.log(`❓ Неизвестных пакетов: ${result.unknownPackages.length}`);
    console.log(`⚠️ Проблем обнаружено: ${result.potentialIssues.length}`);
    console.log(`💡 Рекомендаций: ${result.recommendations.length}`);
    console.log(`🎯 Уровень риска: ${result.analysis.riskLevel}`);
    
    if (result.knownPackages.length > 0) {
      console.log('\n📋 Детали известных пакетов:');
      result.knownPackages.forEach((pkg, index) => {
        console.log(`${index + 1}. ${pkg.name} (${pkg.dependency_type})`);
        console.log(`   📦 ${pkg.requested_version} → ${pkg.latest_version}`);
        console.log(`   📊 ${(pkg.weekly_downloads || 0).toLocaleString()} загрузок/неделю`);
        console.log(`   ⭐ ${(pkg.github_stars || 0).toLocaleString()} звезд`);
        console.log(`   🏆 Качество: ${((pkg.quality_score || 0) * 100).toFixed(1)}%`);
        console.log(`   📝 TypeScript: ${pkg.has_typescript ? '✅' : '❌'}`);
        console.log(`   🧪 Тесты: ${pkg.has_tests ? '✅' : '❌'}`);
      });
    }
    
    if (result.potentialIssues.length > 0) {
      console.log('\n⚠️ Обнаруженные проблемы:');
      result.potentialIssues.forEach((issue, index) => {
        const severityIcon = issue.severity === 'critical' ? '🔴' : 
                           issue.severity === 'high' ? '🟠' : 
                           issue.severity === 'medium' ? '🟡' : '🟢';
        console.log(`${index + 1}. ${severityIcon} ${issue.package}: ${issue.message}`);
      });
    }
    
    if (result.recommendations.length > 0) {
      console.log('\n💡 Рекомендации:');
      result.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec.package}: ${rec.message}`);
      });
    }
    
  } catch (error) {
    console.error('❌ Ошибка тестирования анализа:', error);
  }
}

// Запускаем проверку
if (require.main === module) {
  checkDatabaseStatus().then(() => {
    console.log('\n✅ Проверка базы данных завершена!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
}

module.exports = { checkDatabaseStatus };
