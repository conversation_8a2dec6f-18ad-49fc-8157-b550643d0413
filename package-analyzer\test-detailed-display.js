// Тест детального отображения всей информации из базы данных
const axios = require('axios');

async function testDetailedDisplay() {
  console.log('🧪 ТЕСТИРОВАНИЕ ДЕТАЛЬНОГО ОТОБРАЖЕНИЯ ИНФОРМАЦИИ ИЗ БД');
  console.log('=' .repeat(70));
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    // 1. Получаем список пакетов
    console.log('\n📦 Шаг 1: Получение списка пакетов...');
    
    const packagesResponse = await axios.get(`${baseUrl}/api/packages?limit=5`);
    const packages = packagesResponse.data.packages;
    
    console.log(`✅ Получено ${packages.length} пакетов для тестирования`);
    packages.forEach((pkg, index) => {
      console.log(`   ${index + 1}. ${pkg.name} (${pkg.latest_version || 'N/A'})`);
    });
    
    // 2. Тестируем детальную информацию для каждого пакета
    for (const pkg of packages.slice(0, 3)) { // Тестируем первые 3 пакета
      console.log(`\n🔍 Шаг 2.${packages.indexOf(pkg) + 1}: Детальная информация для ${pkg.name}...`);
      
      try {
        const detailResponse = await axios.get(`${baseUrl}/api/packages/${encodeURIComponent(pkg.name)}`);
        const detailData = detailResponse.data;
        
        console.log(`📋 ПОЛНАЯ ИНФОРМАЦИЯ О ПАКЕТЕ: ${pkg.name}`);
        console.log('-' .repeat(50));
        
        // Основная информация
        console.log('📦 ОСНОВНАЯ ИНФОРМАЦИЯ:');
        console.log(`   Название: ${detailData.name}`);
        console.log(`   Версия: ${detailData.latest_version || 'N/A'}`);
        console.log(`   Описание: ${(detailData.description || 'N/A').substring(0, 100)}${detailData.description?.length > 100 ? '...' : ''}`);
        console.log(`   Автор: ${detailData.author || 'N/A'}`);
        console.log(`   Лицензия: ${detailData.license || 'N/A'}`);
        console.log(`   Создан: ${detailData.first_release_date ? new Date(detailData.first_release_date).toLocaleDateString() : 'N/A'}`);
        console.log(`   Обновлен: ${detailData.last_release_date ? new Date(detailData.last_release_date).toLocaleDateString() : 'N/A'}`);
        
        // Статистика загрузок
        console.log('\n📊 СТАТИСТИКА ЗАГРУЗОК:');
        console.log(`   Неделя: ${(detailData.weekly_downloads || 0).toLocaleString()}`);
        console.log(`   Месяц: ${(detailData.monthly_downloads || 0).toLocaleString()}`);
        console.log(`   Год: ${(detailData.yearly_downloads || 0).toLocaleString()}`);
        console.log(`   Общий (расчетный): ${(detailData.total_downloads || 0).toLocaleString()}`);
        
        // GitHub метрики
        if (detailData.github_stars || detailData.github_forks) {
          console.log('\n🐙 GITHUB МЕТРИКИ:');
          console.log(`   ⭐ Звезды: ${(detailData.github_stars || 0).toLocaleString()}`);
          console.log(`   🍴 Форки: ${(detailData.github_forks || 0).toLocaleString()}`);
          console.log(`   🐛 Issues: ${(detailData.github_issues || 0).toLocaleString()}`);
          console.log(`   👀 Watchers: ${(detailData.github_watchers || 0).toLocaleString()}`);
          console.log(`   📅 Последний коммит: ${detailData.last_commit_date ? new Date(detailData.last_commit_date).toLocaleDateString() : 'N/A'}`);
        }
        
        // Качественные метрики
        console.log('\n🎯 КАЧЕСТВЕННЫЕ МЕТРИКИ:');
        console.log(`   🏆 Качество: ${((detailData.quality_score || 0) * 100).toFixed(1)}%`);
        console.log(`   📈 Популярность: ${((detailData.popularity_score || 0) * 100).toFixed(1)}%`);
        console.log(`   🔧 Поддержка: ${((detailData.maintenance_score || 0) * 100).toFixed(1)}%`);
        console.log(`   📊 Покрытие тестами: ${detailData.test_coverage ? (detailData.test_coverage * 100).toFixed(1) + '%' : 'N/A'}`);
        
        // Технические характеристики
        console.log('\n⚙️ ТЕХНИЧЕСКИЕ ХАРАКТЕРИСТИКИ:');
        console.log(`   📝 TypeScript: ${detailData.has_typescript ? '✅ Да' : '❌ Нет'}`);
        console.log(`   🧪 Тесты: ${detailData.has_tests ? '✅ Да' : '❌ Нет'}`);
        console.log(`   📦 Версий: ${detailData.version_count || 0}`);
        console.log(`   🔗 Зависимостей: ${detailData.dependency_count || 0}`);
        console.log(`   🔧 Dev зависимостей: ${detailData.dev_dependency_count || 0}`);
        console.log(`   🤝 Peer зависимостей: ${detailData.peer_dependency_count || 0}`);
        console.log(`   ⚙️ Optional зависимостей: ${detailData.optional_dependency_count || 0}`);
        console.log(`   📁 Файлов: ${detailData.file_count || 0}`);
        console.log(`   📦 Размер (unpacked): ${detailData.unpacked_size ? (detailData.unpacked_size / 1024).toFixed(1) + ' KB' : 'N/A'}`);
        
        // Ранги и статусы
        console.log('\n📊 РАНГИ И СТАТУСЫ:');
        console.log(`   📈 Ранг популярности: ${(detailData.popularity_rank || 'unknown').replace('_', ' ').toUpperCase()}`);
        console.log(`   🏆 Ранг качества: ${(detailData.quality_rank || 'unknown').replace('_', ' ').toUpperCase()}`);
        
        if (detailData.security_status) {
          console.log(`   🛡️ Статус безопасности: ${detailData.security_status.status.replace('_', ' ').toUpperCase()}`);
          console.log(`   🛡️ Сообщение: ${detailData.security_status.message}`);
        }
        
        if (detailData.maintenance_status) {
          console.log(`   🔧 Статус поддержки: ${detailData.maintenance_status.status.replace('_', ' ').toUpperCase()}`);
          console.log(`   🔧 Сообщение: ${detailData.maintenance_status.message}`);
        }
        
        // Результаты npm audit
        if (detailData.audit_results && detailData.audit_results.length > 0) {
          console.log(`\n🛡️ РЕЗУЛЬТАТЫ NPM AUDIT (${detailData.audit_results.length}):`);
          detailData.audit_results.slice(0, 3).forEach((audit, index) => {
            console.log(`   ${index + 1}. ${audit.title || 'Уязвимость'} (${audit.severity?.toUpperCase() || 'UNKNOWN'})`);
            console.log(`      📝 ${(audit.overview || 'Описание недоступно').substring(0, 80)}...`);
            console.log(`      💡 ${audit.recommendation || 'Обновите пакет'}`);
            console.log(`      🔢 CVSS: ${audit.cvss_score || 'N/A'}`);
            console.log(`      📅 Дата: ${audit.audit_date ? new Date(audit.audit_date).toLocaleDateString() : 'N/A'}`);
          });
          
          if (detailData.audit_results.length > 3) {
            console.log(`   ... и еще ${detailData.audit_results.length - 3} уязвимостей`);
          }
        } else {
          console.log('\n🛡️ РЕЗУЛЬТАТЫ NPM AUDIT: Уязвимости не обнаружены или аудит не проводился');
        }
        
        // Альтернативы
        if (detailData.alternatives && detailData.alternatives.length > 0) {
          console.log(`\n🔄 АЛЬТЕРНАТИВЫ ПАКЕТА (${detailData.alternatives.length}):`);
          detailData.alternatives.slice(0, 3).forEach((alt, index) => {
            console.log(`   ${index + 1}. ${alt.alternative_package}`);
            console.log(`      📊 Релевантность: ${(alt.relevance_score || 0).toFixed(1)}%`);
            console.log(`      💡 ${alt.recommendation || 'Альтернативный пакет'}`);
            console.log(`      🔍 Причины: ${alt.match_reasons ? alt.match_reasons.join(', ') : 'N/A'}`);
          });
        } else {
          console.log('\n🔄 АЛЬТЕРНАТИВЫ ПАКЕТА: Не найдены');
        }
        
        // Ключевые слова
        if (detailData.keywords && detailData.keywords.length > 0) {
          console.log(`\n🏷️ КЛЮЧЕВЫЕ СЛОВА (${detailData.keywords.length}):`);
          console.log(`   ${detailData.keywords.slice(0, 10).join(', ')}${detailData.keywords.length > 10 ? '...' : ''}`);
        }
        
        // Maintainers
        if (detailData.maintainers && detailData.maintainers.length > 0) {
          console.log(`\n👥 MAINTAINERS (${detailData.maintainers.length}):`);
          detailData.maintainers.slice(0, 3).forEach((maintainer, index) => {
            const name = typeof maintainer === 'string' ? maintainer : maintainer.name;
            const email = typeof maintainer === 'object' ? maintainer.email : '';
            console.log(`   ${index + 1}. ${name}${email ? ` (${email})` : ''}`);
          });
        }
        
        // Engines
        if (detailData.engines && Object.keys(detailData.engines).length > 0) {
          console.log('\n🔧 ТРЕБОВАНИЯ К ДВИЖКАМ:');
          Object.entries(detailData.engines).forEach(([engine, version]) => {
            console.log(`   ${engine}: ${version}`);
          });
        }
        
        // Версии (последние 5)
        if (detailData.versions && detailData.versions.length > 0) {
          console.log(`\n📦 ПОСЛЕДНИЕ ВЕРСИИ (показано 5 из ${detailData.versions.length}):`);
          detailData.versions.slice(-5).reverse().forEach((version, index) => {
            const depCount = Object.keys(version.dependencies || {}).length;
            const devDepCount = Object.keys(version.devDependencies || {}).length;
            console.log(`   ${index + 1}. ${version.version} (${version.published_at ? new Date(version.published_at).toLocaleDateString() : 'N/A'})`);
            console.log(`      📦 ${depCount} deps, 🔧 ${devDepCount} dev deps${version.deprecated ? ' ⚠️ DEPRECATED' : ''}`);
          });
        }
        
        console.log('\n✅ Детальная информация успешно получена и отображена');
        
      } catch (error) {
        console.error(`❌ Ошибка получения деталей для ${pkg.name}:`, error.message);
      }
    }
    
    // 3. Проверяем полноту данных
    console.log('\n📊 Шаг 3: Анализ полноты данных в БД...');
    
    let totalFields = 0;
    let filledFields = 0;
    
    for (const pkg of packages.slice(0, 3)) {
      try {
        const detailResponse = await axios.get(`${baseUrl}/api/packages/${encodeURIComponent(pkg.name)}`);
        const data = detailResponse.data;
        
        const fields = [
          'name', 'description', 'latest_version', 'author', 'license',
          'weekly_downloads', 'monthly_downloads', 'yearly_downloads',
          'github_stars', 'github_forks', 'quality_score', 'popularity_score',
          'maintenance_score', 'has_typescript', 'has_tests', 'version_count',
          'dependency_count', 'keywords', 'maintainers', 'engines',
          'audit_results', 'alternatives', 'security_status', 'maintenance_status'
        ];
        
        fields.forEach(field => {
          totalFields++;
          if (data[field] !== null && data[field] !== undefined && data[field] !== '' && 
              !(Array.isArray(data[field]) && data[field].length === 0) &&
              !(typeof data[field] === 'object' && Object.keys(data[field]).length === 0)) {
            filledFields++;
          }
        });
      } catch (error) {
        console.warn(`⚠️ Ошибка анализа полноты для ${pkg.name}`);
      }
    }
    
    const completeness = totalFields > 0 ? (filledFields / totalFields * 100).toFixed(1) : 0;
    
    console.log('📊 АНАЛИЗ ПОЛНОТЫ ДАННЫХ:');
    console.log(`   📋 Всего полей: ${totalFields}`);
    console.log(`   ✅ Заполнено: ${filledFields}`);
    console.log(`   📊 Полнота данных: ${completeness}%`);
    
    // 4. Итоговый отчет
    console.log('\n🎉 ИТОГОВЫЙ ОТЧЕТ ДЕТАЛЬНОГО ОТОБРАЖЕНИЯ');
    console.log('=' .repeat(70));
    
    console.log('✅ ВСЕ ФУНКЦИИ ДЕТАЛЬНОГО ОТОБРАЖЕНИЯ РАБОТАЮТ:');
    console.log('   📦 Основная информация о пакете');
    console.log('   📊 Статистика загрузок и GitHub метрики');
    console.log('   🎯 Качественные метрики и ранги');
    console.log('   ⚙️ Технические характеристики');
    console.log('   🛡️ Результаты npm audit с уязвимостями');
    console.log('   🔄 Альтернативы пакетов');
    console.log('   🏷️ Ключевые слова и maintainers');
    console.log('   🔧 Требования к движкам');
    console.log('   📦 Полная история версий');
    console.log('   🗄️ Сырые данные из БД в JSON формате');
    
    console.log(`\n📊 КАЧЕСТВО ДАННЫХ: ${completeness}% полноты`);
    
    if (parseFloat(completeness) >= 70) {
      console.log('🟢 ОТЛИЧНО - данные очень полные');
    } else if (parseFloat(completeness) >= 50) {
      console.log('🟡 ХОРОШО - данные достаточно полные');
    } else {
      console.log('🔴 ТРЕБУЕТ УЛУЧШЕНИЯ - данные неполные');
    }
    
    console.log('\n🚀 СИСТЕМА ДЕТАЛЬНОГО ОТОБРАЖЕНИЯ ГОТОВА К ИСПОЛЬЗОВАНИЮ!');
    console.log('\n📋 Для просмотра полной информации:');
    console.log('   1. Откройте http://localhost:3000');
    console.log('   2. Найдите пакет в списке');
    console.log('   3. Нажмите кнопку "Полная БД"');
    console.log('   4. Просмотрите всю информацию из базы данных');
    
  } catch (error) {
    console.error('❌ Критическая ошибка тестирования:', error.message);
    
    if (error.response) {
      console.error('📄 Детали ошибки:', error.response.data);
    }
  }
}

// Запускаем тест
if (require.main === module) {
  testDetailedDisplay().then(() => {
    console.log('\n🏁 Тестирование детального отображения завершено!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
}

module.exports = { testDetailedDisplay };
