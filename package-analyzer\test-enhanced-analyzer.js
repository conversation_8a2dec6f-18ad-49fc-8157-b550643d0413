// Тест улучшенного анализатора файлов
const EnhancedFileAnalyzer = require('./lib/enhanced-file-analyzer');

async function testEnhancedAnalyzer() {
  console.log('🧪 Тестирование EnhancedFileAnalyzer...');
  
  const analyzer = new EnhancedFileAnalyzer();
  
  const testPackageJson = {
    "name": "test-security-project",
    "version": "1.0.0",
    "dependencies": {
      "moment": "^2.24.0",     // Устаревшая версия с уязвимостями
      "lodash": "^4.17.15",    // Старая версия с уязвимостями
      "express": "^4.16.0",    // Устаревшая версия
      "axios": "^0.19.0",      // Старая версия
      "react": "^16.8.0"       // Устаревшая версия
    },
    "devDependencies": {
      "jest": "^24.0.0",       // Устаревшая версия
      "eslint": "^6.0.0"       // Устаревшая версия
    }
  };
  
  try {
    console.log('📊 Запуск анализа...');
    
    const result = await analyzer.analyzeAndFixPackageJson(
      JSON.stringify(testPackageJson, null, 2)
    );
    
    console.log('\n✅ РЕЗУЛЬТАТЫ АНАЛИЗА:');
    console.log('📦 Проект:', result.name || 'test-security-project');
    
    if (result.fixedCode && result.originalCode) {
      console.log('\n🛠️ ИСПРАВЛЕННЫЙ КОД СГЕНЕРИРОВАН:');
      console.log('📝 Оригинальный код:', result.originalCode.length, 'символов');
      console.log('🔧 Исправленный код:', result.fixedCode.length, 'символов');
      
      console.log('\n📋 ОРИГИНАЛЬНЫЙ КОД:');
      console.log(result.originalCode);
      
      console.log('\n🛠️ ИСПРАВЛЕННЫЙ КОД:');
      console.log(result.fixedCode);
      
      if (result.fixStats) {
        console.log('\n📊 СТАТИСТИКА ИСПРАВЛЕНИЙ:');
        console.log('🔧 Всего исправлений:', result.fixStats.totalFixes || 0);
        console.log('🛡️ Безопасности:', result.fixStats.securityFixes || 0);
        console.log('🔄 Совместимости:', result.fixStats.compatibilityFixes || 0);
        console.log('📈 Обновлений:', result.fixStats.updateFixes || 0);
        console.log('🔄 Альтернатив:', result.fixStats.alternativeFixes || 0);
      }
      
      if (result.fixReport && result.fixReport.fixes) {
        console.log('\n📋 ОТЧЕТ ОБ ИСПРАВЛЕНИЯХ:');
        console.log('📝 Детальных исправлений:', result.fixReport.fixes.length);
        
        result.fixReport.fixes.forEach((fix, index) => {
          console.log(`\n${index + 1}. ${fix.package} (${fix.category.toUpperCase()})`);
          console.log(`   📝 ${fix.description}`);
          console.log(`   🔧 ${fix.reason}`);
          if (fix.originalVersion && fix.newVersion) {
            console.log(`   📦 ${fix.originalVersion} → ${fix.newVersion}`);
          }
          console.log(`   ⚠️ Влияние: ${fix.impact?.toUpperCase() || 'LOW'}`);
        });
      }
      
      if (result.vulnerabilities && result.vulnerabilities.length > 0) {
        console.log('\n🛡️ УЯЗВИМОСТИ:');
        result.vulnerabilities.forEach((vuln, index) => {
          console.log(`\n${index + 1}. ${vuln.package || vuln.name} (${vuln.severity?.toUpperCase() || 'UNKNOWN'})`);
          console.log(`   📝 ${vuln.title || 'Уязвимость'}`);
          console.log(`   🔧 Уязвимые версии: ${vuln.vulnerable_versions || 'N/A'}`);
          console.log(`   ✅ Исправлено в: ${vuln.patched_versions || vuln.fixedVersion || 'Не исправлено'}`);
          console.log(`   💡 ${vuln.recommendation || 'Обновите пакет'}`);
        });
      }
      
      if (result.enhancedRecommendations) {
        console.log('\n💡 УЛУЧШЕННЫЕ РЕКОМЕНДАЦИИ:');
        result.enhancedRecommendations.forEach((rec, index) => {
          const priority = rec.priority === 'critical' ? '🔴' : 
                         rec.priority === 'high' ? '🟠' : 
                         rec.priority === 'medium' ? '🟡' : '🟢';
          console.log(`\n${priority} ${rec.title} (${rec.priority.toUpperCase()})`);
          console.log(`   📝 ${rec.description}`);
          console.log(`   🎯 ${rec.action}`);
        });
      }
      
    } else {
      console.log('❌ Исправленный код НЕ сгенерирован');
      console.log('📋 Полный результат:', JSON.stringify(result, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Ошибка анализа:', error);
    console.error('📄 Стек ошибки:', error.stack);
  }
}

// Запускаем тест
if (require.main === module) {
  testEnhancedAnalyzer().then(() => {
    console.log('\n🏁 Тестирование завершено!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
}

module.exports = { testEnhancedAnalyzer };
