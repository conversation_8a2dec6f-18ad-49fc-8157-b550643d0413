// Тест улучшенной системы с npm audit и полным анализом зависимостей
const axios = require('axios');

async function testEnhancedSystem() {
  console.log('🧪 Тестирование улучшенной системы с npm audit...');
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    // 1. Тестируем сбор пакета с npm audit
    console.log('\n📦 Шаг 1: Тестирование сбора пакета с npm audit...');
    
    const response = await axios.post(`${baseUrl}/api/collect-package`, {
      packageName: 'react'
    });
    
    if (response.data.success) {
      console.log('✅ Пакет react добавлен в очередь для полного анализа');
      console.log(`📋 Task ID: ${response.data.taskId}`);
      
      // Ждем завершения сбора
      console.log('⏳ Ожидаем завершения полного сбора с npm audit...');
      await new Promise(resolve => setTimeout(resolve, 30000)); // 30 секунд
      
      // Проверяем результаты
      console.log('📊 Проверяем результаты сбора...');
      const packagesResponse = await axios.get(`${baseUrl}/api/packages?search=react&limit=1`);
      
      if (packagesResponse.data && packagesResponse.data.packages.length > 0) {
        const reactPackage = packagesResponse.data.packages[0];
        
        console.log('\n📋 РЕЗУЛЬТАТЫ ПОЛНОГО СБОРА:');
        console.log('=' .repeat(60));
        
        // Основная информация
        console.log(`📦 Пакет: ${reactPackage.name}`);
        console.log(`📝 Описание: ${reactPackage.description || 'N/A'}`);
        console.log(`🔗 Версия: ${reactPackage.latest_version || 'N/A'}`);
        console.log(`👤 Автор: ${reactPackage.author || 'N/A'}`);
        console.log(`📄 Лицензия: ${reactPackage.license || 'N/A'}`);
        
        // Статистика загрузок
        console.log('\n📊 СТАТИСТИКА ЗАГРУЗОК:');
        console.log(`   📈 Недельные: ${(reactPackage.weekly_downloads || 0).toLocaleString()}`);
        console.log(`   📈 Месячные: ${(reactPackage.monthly_downloads || 0).toLocaleString()}`);
        console.log(`   📈 Годовые: ${(reactPackage.yearly_downloads || 0).toLocaleString()}`);
        
        // GitHub метрики
        if (reactPackage.github_stars !== undefined) {
          console.log('\n🐙 GITHUB МЕТРИКИ:');
          console.log(`   ⭐ Звезды: ${reactPackage.github_stars.toLocaleString()}`);
          console.log(`   🍴 Форки: ${reactPackage.github_forks.toLocaleString()}`);
          console.log(`   🐛 Открытые issues: ${reactPackage.github_issues.toLocaleString()}`);
          console.log(`   👀 Наблюдатели: ${reactPackage.github_watchers.toLocaleString()}`);
        }
        
        // Качественные метрики
        console.log('\n🎯 КАЧЕСТВЕННЫЕ МЕТРИКИ:');
        console.log(`   📊 Оценка качества: ${((reactPackage.quality_score || 0) * 100).toFixed(1)}%`);
        console.log(`   📈 Популярность: ${((reactPackage.popularity_score || 0) * 100).toFixed(1)}%`);
        console.log(`   🔧 Поддержка: ${((reactPackage.maintenance_score || 0) * 100).toFixed(1)}%`);
        console.log(`   📦 TypeScript: ${reactPackage.has_typescript ? 'Да' : 'Нет'}`);
        console.log(`   🧪 Тесты: ${reactPackage.has_tests ? 'Да' : 'Нет'}`);
        
        // Зависимости
        console.log('\n🔗 ЗАВИСИМОСТИ:');
        console.log(`   📦 Основные: ${reactPackage.dependency_count || 0}`);
        console.log(`   🔧 Dev: ${reactPackage.dev_dependency_count || 0}`);
        console.log(`   🤝 Peer: ${reactPackage.peer_dependency_count || 0}`);
        console.log(`   📎 Optional: ${reactPackage.optional_dependency_count || 0}`);
        
        // Версии
        console.log('\n📋 ВЕРСИИ:');
        console.log(`   📊 Всего версий: ${reactPackage.version_count || 0}`);
        console.log(`   📅 Первый релиз: ${reactPackage.first_release_date ? new Date(reactPackage.first_release_date).toLocaleDateString() : 'N/A'}`);
        console.log(`   📅 Последний релиз: ${reactPackage.last_release_date ? new Date(reactPackage.last_release_date).toLocaleDateString() : 'N/A'}`);
        console.log(`   🔄 Частота релизов: ${(reactPackage.release_frequency || 0).toFixed(2)} в месяц`);
        
        console.log('\n✅ Полный сбор пакета работает корректно!');
      } else {
        console.log('❌ Пакет не найден в базе данных');
      }
    }
    
    // 2. Тестируем анализ совместимости с npm audit
    console.log('\n🔍 Шаг 2: Тестирование анализа совместимости с npm audit...');
    
    const testPackageJson = {
      "name": "test-audit-project",
      "version": "1.0.0",
      "dependencies": {
        "react": "^18.0.0",
        "lodash": "^4.17.21",
        "express": "^4.18.0",
        "moment": "^2.29.0"  // Пакет с известными уязвимостями
      },
      "devDependencies": {
        "jest": "^29.0.0",
        "typescript": "^4.8.0"
      }
    };
    
    console.log('📊 Запускаем анализ совместимости с npm audit...');
    
    const analysisResponse = await axios.post(`${baseUrl}/api/analyze`, {
      filePath: 'test-package.json',
      content: JSON.stringify(testPackageJson, null, 2)
    });
    
    if (analysisResponse.data) {
      const result = analysisResponse.data;
      
      console.log('\n📋 РЕЗУЛЬТАТЫ АНАЛИЗА С NPM AUDIT:');
      console.log('=' .repeat(60));
      
      // Основная информация
      console.log(`📦 Проект: ${result.name || 'test-audit-project'}`);
      
      // Статистика зависимостей
      console.log('\n📊 СТАТИСТИКА ЗАВИСИМОСТЕЙ:');
      console.log(`   📦 Всего зависимостей: ${result.dependencies?.total || 0}`);
      console.log(`   ✅ Совместимых: ${result.dependencies?.compatible || 0}`);
      console.log(`   ❌ Несовместимых: ${result.dependencies?.incompatible || 0}`);
      console.log(`   ❓ Неизвестных: ${result.dependencies?.unknown || 0}`);
      
      // Результаты npm audit
      if (result.compatibility?.summary?.totalVulnerabilities !== undefined) {
        console.log('\n🛡️ РЕЗУЛЬТАТЫ NPM AUDIT:');
        console.log(`   🚨 Всего уязвимостей: ${result.compatibility.summary.totalVulnerabilities}`);
        console.log(`   🔴 Критических: ${result.compatibility.summary.criticalVulnerabilities || 0}`);
        console.log(`   🟠 Высоких: ${result.compatibility.summary.highVulnerabilities || 0}`);
        console.log(`   🟡 Средних: ${result.compatibility.summary.moderateVulnerabilities || 0}`);
        console.log(`   🟢 Низких: ${result.compatibility.summary.lowVulnerabilities || 0}`);
      }
      
      // Уязвимости
      if (result.vulnerabilities && result.vulnerabilities.length > 0) {
        console.log('\n⚠️ НАЙДЕННЫЕ УЯЗВИМОСТИ:');
        result.vulnerabilities.slice(0, 5).forEach((vuln, index) => {
          console.log(`   ${index + 1}. ${vuln.package} (${vuln.severity})`);
          console.log(`      📝 ${vuln.title || 'Unknown vulnerability'}`);
          console.log(`      💡 ${vuln.recommendation || 'Update package'}`);
        });
        
        if (result.vulnerabilities.length > 5) {
          console.log(`   ... и еще ${result.vulnerabilities.length - 5} уязвимостей`);
        }
      }
      
      // Конфликты зависимостей
      if (result.conflicts && result.conflicts.length > 0) {
        console.log('\n⚠️ КОНФЛИКТЫ ЗАВИСИМОСТЕЙ:');
        result.conflicts.slice(0, 3).forEach((conflict, index) => {
          console.log(`   ${index + 1}. ${conflict.type}: ${conflict.message}`);
          console.log(`      💡 ${conflict.recommendation}`);
        });
      }
      
      // Рекомендации
      if (result.recommendations && result.recommendations.length > 0) {
        console.log('\n💡 РЕКОМЕНДАЦИИ:');
        result.recommendations.slice(0, 5).forEach((rec, index) => {
          const priority = rec.priority === 'high' ? '🔴' : rec.priority === 'medium' ? '🟡' : '🟢';
          console.log(`   ${priority} ${rec.type}: ${rec.message}`);
          console.log(`      💡 ${rec.recommendation}`);
        });
      }
      
      // Общая оценка
      console.log('\n🎯 ОБЩАЯ ОЦЕНКА:');
      console.log(`⚠️ Уровень риска: ${result.compatibility?.riskLevel?.toUpperCase() || 'НЕИЗВЕСТНО'}`);
      console.log(`📊 Совместимость: ${result.compatibility?.compatibilityScore?.toFixed(1) || 0}%`);
      
      const riskColor = result.compatibility?.riskLevel === 'low' ? '🟢' : 
                       result.compatibility?.riskLevel === 'medium' ? '🟡' : '🔴';
      console.log(`${riskColor} Статус: ${getProjectStatus(result.compatibility?.riskLevel, result.compatibility?.compatibilityScore)}`);
      
      console.log('\n✅ Анализ с npm audit работает корректно!');
    }
    
    // 3. Проверяем статистику системы
    console.log('\n📊 Шаг 3: Проверка статистики системы...');
    
    const statsResponse = await axios.get(`${baseUrl}/api/stats`);
    
    if (statsResponse.data) {
      console.log('\n📈 СТАТИСТИКА СИСТЕМЫ:');
      console.log(`   📦 Всего пакетов: ${statsResponse.data.totalPackages}`);
      console.log(`   🔍 Анализов сегодня: ${statsResponse.data.todayAnalyses}`);
      console.log(`   ⚡ Скорость анализа: ${statsResponse.data.analysisSpeed.toFixed(2)} пак/сек`);
      console.log(`   🎮 GPU ускорение: ${statsResponse.data.gpuAccelerated ? 'активно' : 'отключено'}`);
    }
    
    console.log('\n🎉 Тестирование улучшенной системы завершено успешно!');
    console.log('✅ Все функции работают корректно:');
    console.log('   📦 Полный сбор пакетов с npm audit');
    console.log('   🔍 Анализ совместимости с уязвимостями');
    console.log('   📊 Детальная информация о зависимостях');
    console.log('   💡 Рекомендации по безопасности');
    
  } catch (error) {
    console.error('❌ Ошибка тестирования:', error.message);
    
    if (error.response) {
      console.error('📄 Детали ошибки:', error.response.data);
    }
  }
}

function getProjectStatus(riskLevel, compatibilityScore) {
  if (riskLevel === 'low' && compatibilityScore >= 90) {
    return 'ОТЛИЧНО - проект готов к production';
  } else if (riskLevel === 'low' && compatibilityScore >= 70) {
    return 'ХОРОШО - незначительные проблемы';
  } else if (riskLevel === 'medium') {
    return 'ТРЕБУЕТ ВНИМАНИЯ - есть проблемы совместимости или уязвимости';
  } else {
    return 'КРИТИЧНО - серьезные проблемы безопасности или совместимости';
  }
}

// Запускаем тест
if (require.main === module) {
  testEnhancedSystem().then(() => {
    console.log('\n🏁 Тестирование завершено успешно!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
}

module.exports = { testEnhancedSystem };
