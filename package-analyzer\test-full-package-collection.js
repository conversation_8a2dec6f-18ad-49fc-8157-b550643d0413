// Тест полного сбора пакета с расширенной информацией
const axios = require('axios');

async function testFullPackageCollection() {
  console.log('🎯 Тестирование полного сбора пакета с расширенной информацией...');
  
  const packageName = 'react';
  const baseUrl = 'http://localhost:3000';
  
  try {
    console.log(`🚀 Собираем пакет ${packageName} с полной информацией...`);
    
    const startTime = Date.now();
    
    // Запускаем сбор пакета
    const response = await axios.post(`${baseUrl}/api/collect-package`, {
      packageName: packageName
    });
    
    if (response.data.success) {
      console.log(`✅ Пакет ${packageName} добавлен в очередь (Task ID: ${response.data.taskId})`);
      
      // Ждем завершения обработки
      console.log('⏳ Ожидаем завершения обработки...');
      await new Promise(resolve => setTimeout(resolve, 10000));
      
      // Получаем детальную информацию о пакете
      console.log('📊 Получаем детальную информацию...');
      const detailsResponse = await axios.get(`${baseUrl}/api/packages/${packageName}`);
      
      if (detailsResponse.data) {
        const pkg = detailsResponse.data;
        
        console.log('\n📋 ПОЛНАЯ ИНФОРМАЦИЯ О ПАКЕТЕ:');
        console.log('=' .repeat(50));
        
        // Основная информация
        console.log('\n📦 ОСНОВНАЯ ИНФОРМАЦИЯ:');
        console.log(`   📝 Название: ${pkg.name}`);
        console.log(`   📄 Описание: ${pkg.description || 'N/A'}`);
        console.log(`   📦 Последняя версия: ${pkg.latest_version || 'N/A'}`);
        console.log(`   👤 Автор: ${pkg.author || 'N/A'}`);
        console.log(`   ⚖️ Лицензия: ${pkg.license || 'N/A'}`);
        
        // Ссылки
        console.log('\n🔗 ССЫЛКИ:');
        console.log(`   🌐 NPM: ${pkg.npm_url || 'N/A'}`);
        console.log(`   🐙 Репозиторий: ${pkg.repository || 'N/A'}`);
        console.log(`   🏠 Домашняя страница: ${pkg.homepage || 'N/A'}`);
        console.log(`   🐛 Баги: ${pkg.bugs_url || 'N/A'}`);
        
        // Команда разработки
        console.log('\n👥 КОМАНДА РАЗРАБОТКИ:');
        if (pkg.maintainers) {
          try {
            const maintainers = JSON.parse(pkg.maintainers);
            console.log(`   👨‍💻 Maintainers: ${maintainers.length} человек`);
            maintainers.slice(0, 3).forEach((m, i) => {
              const name = typeof m === 'string' ? m : m.name;
              console.log(`     ${i + 1}. ${name}`);
            });
          } catch (e) {
            console.log(`   👨‍💻 Maintainers: ${pkg.maintainers ? 'есть' : 'нет'}`);
          }
        } else {
          console.log(`   👨‍💻 Maintainers: нет данных`);
        }
        
        // Статистика загрузок
        console.log('\n📊 СТАТИСТИКА ЗАГРУЗОК:');
        console.log(`   📅 Неделя: ${(pkg.weekly_downloads || 0).toLocaleString()}`);
        console.log(`   📅 Месяц: ${(pkg.monthly_downloads || 0).toLocaleString()}`);
        console.log(`   📅 Год: ${(pkg.yearly_downloads || 0).toLocaleString()}`);
        
        // GitHub метрики
        console.log('\n🐙 GITHUB МЕТРИКИ:');
        console.log(`   ⭐ Звезды: ${(pkg.github_stars || 0).toLocaleString()}`);
        console.log(`   🍴 Форки: ${(pkg.github_forks || 0).toLocaleString()}`);
        console.log(`   🐛 Issues: ${(pkg.github_issues || 0).toLocaleString()}`);
        console.log(`   👀 Watchers: ${(pkg.github_watchers || 0).toLocaleString()}`);
        
        // Оценки качества
        console.log('\n🎯 ОЦЕНКИ КАЧЕСТВА:');
        console.log(`   🏆 Качество: ${((pkg.quality_score || 0) * 100).toFixed(1)}%`);
        console.log(`   📈 Популярность: ${((pkg.popularity_score || 0) * 100).toFixed(1)}%`);
        console.log(`   🔧 Поддержка: ${((pkg.maintenance_score || 0) * 100).toFixed(1)}%`);
        
        // Технические характеристики
        console.log('\n⚙️ ТЕХНИЧЕСКИЕ ХАРАКТЕРИСТИКИ:');
        console.log(`   📝 TypeScript: ${pkg.has_typescript ? '✅ Да' : '❌ Нет'}`);
        console.log(`   🧪 Тесты: ${pkg.has_tests ? '✅ Да' : '❌ Нет'}`);
        console.log(`   📊 Покрытие тестами: ${((pkg.test_coverage || 0) * 100).toFixed(1)}%`);
        console.log(`   📦 Версий: ${pkg.version_count || 0}`);
        console.log(`   🔗 Зависимостей: ${pkg.dependency_count || 0}`);
        console.log(`   📁 Файлов: ${pkg.file_count || 0}`);
        console.log(`   📦 Размер (unpacked): ${pkg.unpacked_size || 0} байт`);
        
        // Даты
        console.log('\n📅 ВРЕМЕННЫЕ МЕТКИ:');
        console.log(`   🎂 Первый релиз: ${pkg.first_release_date ? new Date(pkg.first_release_date).toLocaleDateString() : 'N/A'}`);
        console.log(`   📅 Последний релиз: ${pkg.last_release_date ? new Date(pkg.last_release_date).toLocaleDateString() : 'N/A'}`);
        console.log(`   💻 Последний коммит: ${pkg.last_commit_date ? new Date(pkg.last_commit_date).toLocaleDateString() : 'N/A'}`);
        console.log(`   🔄 Частота релизов: ${(pkg.release_frequency || 0).toFixed(2)} релизов/месяц`);
        
        // Совместимость
        console.log('\n🔧 СОВМЕСТИМОСТЬ:');
        if (pkg.engines) {
          try {
            const engines = JSON.parse(pkg.engines);
            console.log(`   🚀 Engines: ${JSON.stringify(engines)}`);
          } catch (e) {
            console.log(`   🚀 Engines: ${pkg.engines}`);
          }
        } else {
          console.log(`   🚀 Engines: не указано`);
        }
        console.log(`   💻 OS: ${pkg.os_compatibility || 'любая'}`);
        console.log(`   🖥️ CPU: ${pkg.cpu_compatibility || 'любая'}`);
        
        // Финансирование
        console.log('\n💰 ФИНАНСИРОВАНИЕ:');
        if (pkg.funding) {
          try {
            const funding = JSON.parse(pkg.funding);
            console.log(`   💳 Funding: ${JSON.stringify(funding)}`);
          } catch (e) {
            console.log(`   💳 Funding: ${pkg.funding}`);
          }
        } else {
          console.log(`   💳 Funding: не указано`);
        }
        
        // Версии
        if (pkg.versions && pkg.versions.length > 0) {
          console.log('\n📦 ПОСЛЕДНИЕ ВЕРСИИ:');
          pkg.versions.slice(0, 5).forEach((version, index) => {
            const depCount = Object.keys(version.dependencies || {}).length;
            const devDepCount = Object.keys(version.devDependencies || {}).length;
            console.log(`   ${index + 1}. ${version.version} (${new Date(version.published_at).toLocaleDateString()})`);
            console.log(`      🔗 ${depCount} зависимостей, ${devDepCount} dev зависимостей`);
          });
        }
        
        // Ключевые слова
        if (pkg.keywords && pkg.keywords.length > 0) {
          console.log('\n🏷️ КЛЮЧЕВЫЕ СЛОВА:');
          console.log(`   ${pkg.keywords.slice(0, 10).join(', ')}`);
          if (pkg.keywords.length > 10) {
            console.log(`   ... и еще ${pkg.keywords.length - 10} ключевых слов`);
          }
        }
        
        const totalTime = Date.now() - startTime;
        
        console.log('\n🎯 РЕЗУЛЬТАТЫ СБОРА:');
        console.log('=' .repeat(50));
        console.log(`⏱️  Общее время: ${totalTime}мс`);
        console.log(`🎮 GPU ускорение: активно`);
        console.log(`📊 Полнота данных: ${calculateCompleteness(pkg).toFixed(1)}%`);
        console.log(`✅ Статус: успешно собрано`);
        
      } else {
        console.log('❌ Не удалось получить детальную информацию о пакете');
      }
    } else {
      console.log('❌ Ошибка добавления пакета в очередь');
    }
    
  } catch (error) {
    console.error('❌ Ошибка тестирования:', error.message);
  }
}

function calculateCompleteness(pkg) {
  const fields = [
    'description', 'repository', 'homepage', 'license', 'author',
    'weekly_downloads', 'github_stars', 'quality_score',
    'has_typescript', 'has_tests', 'maintainers', 'keywords'
  ];
  
  let filledFields = 0;
  fields.forEach(field => {
    if (pkg[field] && pkg[field] !== 0 && pkg[field] !== '0' && pkg[field] !== 'null') {
      filledFields++;
    }
  });
  
  return (filledFields / fields.length) * 100;
}

// Запускаем тест
if (require.main === module) {
  testFullPackageCollection().then(() => {
    console.log('\n🎉 Тестирование полного сбора завершено!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
}

module.exports = { testFullPackageCollection };
