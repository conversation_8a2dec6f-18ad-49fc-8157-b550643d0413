// Тест GPU ускорения для сбора пакетов
const axios = require('axios');

async function testGPUCollection() {
  console.log('🎮 Тестирование GPU ускорения сбора пакетов...');
  
  const packagesToTest = [
    'typescript',
    'webpack',
    'babel-core',
    'eslint',
    'prettier',
    'jest',
    'mocha',
    'chai',
    'sinon',
    'nodemon'
  ];
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    console.log(`📦 Добавляем ${packagesToTest.length} пакетов с GPU ускорением...`);
    
    const startTime = Date.now();
    
    // Добавляем пакеты параллельно для демонстрации GPU ускорения
    const promises = packagesToTest.map(async (packageName, index) => {
      try {
        console.log(`🚀 [${index + 1}/${packagesToTest.length}] Добавляем пакет: ${packageName}`);
        
        const response = await axios.post(`${baseUrl}/api/collect-package`, {
          packageName: packageName
        });
        
        console.log(`✅ [${index + 1}/${packagesToTest.length}] Пакет ${packageName} добавлен успешно`);
        return { packageName, success: true, data: response.data };
      } catch (error) {
        console.error(`❌ [${index + 1}/${packagesToTest.length}] Ошибка добавления ${packageName}:`, error.message);
        return { packageName, success: false, error: error.message };
      }
    });
    
    const results = await Promise.all(promises);
    const totalTime = Date.now() - startTime;
    
    const successful = results.filter(r => r.success).length;
    const failed = results.filter(r => !r.success).length;
    
    console.log('\n🎯 Результаты GPU тестирования:');
    console.log(`⏱️  Общее время: ${totalTime}мс`);
    console.log(`⚡ Среднее время на пакет: ${Math.round(totalTime / packagesToTest.length)}мс`);
    console.log(`✅ Успешно добавлено: ${successful} пакетов`);
    console.log(`❌ Ошибок: ${failed} пакетов`);
    console.log(`🎮 GPU ускорение: ${totalTime < packagesToTest.length * 2000 ? 'АКТИВНО' : 'НЕ АКТИВНО'}`);
    
    // Тестируем анализ package.json с GPU
    console.log('\n🔍 Тестирование GPU анализа package.json...');
    await testGPUAnalysis();
    
  } catch (error) {
    console.error('❌ Ошибка тестирования:', error.message);
  }
}

async function testGPUAnalysis() {
  const fs = require('fs');
  const path = require('path');
  
  try {
    // Читаем тестовый package.json
    const packageJsonPath = path.join(__dirname, 'test-package.json');
    const packageJsonContent = fs.readFileSync(packageJsonPath, 'utf8');
    
    console.log('📄 Анализируем test-package.json с GPU ускорением...');
    
    const startTime = Date.now();
    
    const response = await axios.post('http://localhost:3000/api/analyze', {
      filePath: 'test-package.json',
      content: packageJsonContent
    });
    
    const analysisTime = Date.now() - startTime;
    const result = response.data;
    
    console.log('\n🎯 Результаты GPU анализа:');
    console.log(`⏱️  Время анализа: ${analysisTime}мс`);
    console.log(`🎮 GPU ускорение: ${result.analysis?.gpuAccelerated ? 'АКТИВНО' : 'НЕ АКТИВНО'}`);
    
    if (result.analysis?.processingMetrics) {
      const metrics = result.analysis.processingMetrics;
      console.log(`🔍 GPU анализ зависимостей: ${metrics.gpuAnalysisTime || 0}мс`);
      console.log(`🔄 GPU поиск циклов: ${metrics.gpuCycleTime || 0}мс`);
      console.log(`🔗 GPU совместимость: ${metrics.gpuCompatibilityTime || 0}мс`);
    }
    
    console.log(`📊 Всего зависимостей: ${result.dependencies?.total || 0}`);
    console.log(`✅ Известных пакетов: ${result.dependencies?.known || 0}`);
    console.log(`❓ Неизвестных пакетов: ${result.dependencies?.unknown || 0}`);
    console.log(`⚠️  Уровень риска: ${result.analysis?.riskLevel || 'unknown'}`);
    
    if (result.gpuAnalysis?.dependencies) {
      console.log(`🎮 GPU проанализировано зависимостей: ${result.gpuAnalysis.dependencies.length}`);
      
      // Показываем топ-3 самых рискованных пакета
      const riskySorted = result.gpuAnalysis.dependencies
        .sort((a, b) => b.riskScore - a.riskScore)
        .slice(0, 3);
      
      if (riskySorted.length > 0) {
        console.log('\n🚨 Топ-3 самых рискованных пакета:');
        riskySorted.forEach((dep, index) => {
          console.log(`${index + 1}. ${dep.name}: ${(dep.riskScore * 100).toFixed(1)}% риска`);
        });
      }
    }
    
    console.log('\n✅ GPU анализ завершен успешно!');
    
  } catch (error) {
    console.error('❌ Ошибка GPU анализа:', error.message);
  }
}

// Запускаем тест
if (require.main === module) {
  testGPUCollection().then(() => {
    console.log('\n🎉 Тестирование GPU ускорения завершено!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
}

module.exports = { testGPUCollection, testGPUAnalysis };
