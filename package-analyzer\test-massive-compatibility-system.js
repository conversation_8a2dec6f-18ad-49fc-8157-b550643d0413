// Тест массовой системы анализа совместимости версий
const axios = require('axios');
const fs = require('fs');
const path = require('path');

async function testMassiveCompatibilitySystem() {
  console.log('🎯 Тестирование массовой системы анализа совместимости...');
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    // 1. Тестируем сбор нескольких популярных пакетов
    console.log('\n📦 Шаг 1: Сбор популярных пакетов для тестирования...');
    
    const testPackages = ['react', 'lodash', 'express', 'axios', 'moment'];
    
    for (const packageName of testPackages) {
      console.log(`🚀 Собираем пакет: ${packageName}`);
      
      try {
        const response = await axios.post(`${baseUrl}/api/collect-package`, {
          packageName: packageName
        });
        
        if (response.data.success) {
          console.log(`✅ ${packageName} добавлен в очередь (Task ID: ${response.data.taskId})`);
        }
      } catch (error) {
        console.log(`⚠️ Ошибка сбора ${packageName}: ${error.message}`);
      }
    }
    
    // Ждем завершения сбора
    console.log('⏳ Ожидаем завершения сбора пакетов...');
    await new Promise(resolve => setTimeout(resolve, 30000)); // 30 секунд
    
    // 2. Тестируем анализ совместимости с реальным package.json
    console.log('\n🔍 Шаг 2: Тестирование анализа совместимости...');
    
    const testPackageJson = {
      "name": "test-compatibility-project",
      "version": "1.0.0",
      "description": "Тестовый проект для анализа совместимости",
      "dependencies": {
        "react": "^18.0.0",
        "lodash": "^4.17.21",
        "express": "^4.18.0",
        "axios": "^1.0.0",
        "moment": "^2.29.0"
      },
      "devDependencies": {
        "jest": "^29.0.0",
        "typescript": "^4.8.0",
        "@types/react": "^18.0.0",
        "@types/lodash": "^4.14.0"
      },
      "peerDependencies": {
        "react-dom": "^18.0.0"
      },
      "optionalDependencies": {
        "fsevents": "^2.3.0"
      }
    };
    
    console.log('📊 Запускаем полный анализ совместимости...');
    
    const analysisResponse = await axios.post(`${baseUrl}/api/analyze`, {
      filePath: 'test-package.json',
      content: JSON.stringify(testPackageJson, null, 2)
    });
    
    if (analysisResponse.data) {
      const result = analysisResponse.data;
      
      console.log('\n📋 РЕЗУЛЬТАТЫ АНАЛИЗА СОВМЕСТИМОСТИ:');
      console.log('=' .repeat(60));
      
      // Основная информация
      console.log(`📦 Проект: ${result.name || 'test-compatibility-project'}`);
      console.log(`🔗 Хэш проекта: ${result.projectHash}`);
      
      // Статистика зависимостей
      console.log('\n📊 СТАТИСТИКА ЗАВИСИМОСТЕЙ:');
      console.log(`   📦 Всего зависимостей: ${result.dependencies.total}`);
      console.log(`   ✅ Совместимых: ${result.dependencies.compatible}`);
      console.log(`   ❌ Несовместимых: ${result.dependencies.incompatible}`);
      console.log(`   ❓ Неизвестных: ${result.dependencies.unknown}`);
      console.log(`   🏭 Production: ${result.dependencies.production}`);
      console.log(`   🔧 Development: ${result.dependencies.development}`);
      console.log(`   🤝 Peer: ${result.dependencies.peer}`);
      console.log(`   📎 Optional: ${result.dependencies.optional}`);
      
      // Результаты совместимости
      console.log('\n🎯 АНАЛИЗ СОВМЕСТИМОСТИ:');
      console.log(`   📊 Оценка совместимости: ${result.compatibility.compatibilityScore.toFixed(1)}%`);
      console.log(`   ⚠️ Уровень риска: ${result.compatibility.riskLevel.toUpperCase()}`);
      console.log(`   🔄 Найдено альтернатив: ${result.compatibility.alternativesFound}`);
      
      // Детальная информация о зависимостях
      if (result.detailedDependencies) {
        console.log('\n🔍 ДЕТАЛЬНЫЙ АНАЛИЗ ЗАВИСИМОСТЕЙ:');
        
        let compatibleCount = 0;
        let incompatibleCount = 0;
        let unknownCount = 0;
        
        for (const [depName, depAnalysis] of Object.entries(result.detailedDependencies)) {
          const status = depAnalysis.status;
          const statusIcon = status === 'compatible' ? '✅' : 
                           status === 'incompatible' ? '❌' : '❓';
          
          console.log(`   ${statusIcon} ${depName}@${depAnalysis.requestedVersion}`);
          console.log(`      📦 Последняя версия: ${depAnalysis.latestVersion || 'N/A'}`);
          console.log(`      🎯 Рекомендуемая: ${depAnalysis.recommendedVersion || 'N/A'}`);
          console.log(`      📊 Качество: ${((depAnalysis.metrics?.qualityScore || 0) * 100).toFixed(1)}%`);
          console.log(`      📈 Популярность: ${(depAnalysis.metrics?.weeklyDownloads || 0).toLocaleString()} загрузок/неделю`);
          
          if (depAnalysis.issues && depAnalysis.issues.length > 0) {
            console.log(`      ⚠️ Проблемы: ${depAnalysis.issues.length}`);
            depAnalysis.issues.slice(0, 2).forEach(issue => {
              console.log(`         - ${issue.message}`);
            });
          }
          
          if (depAnalysis.recommendations && depAnalysis.recommendations.length > 0) {
            console.log(`      💡 Рекомендации: ${depAnalysis.recommendations.length}`);
            depAnalysis.recommendations.slice(0, 1).forEach(rec => {
              console.log(`         - ${rec.message || rec.recommendation}`);
            });
          }
          
          // Подсчитываем статистику
          if (status === 'compatible') compatibleCount++;
          else if (status === 'incompatible') incompatibleCount++;
          else unknownCount++;
          
          console.log('');
        }
        
        console.log(`📊 Итого проанализировано: ${compatibleCount} совместимых, ${incompatibleCount} несовместимых, ${unknownCount} неизвестных`);
      }
      
      // Конфликты
      if (result.conflicts && result.conflicts.length > 0) {
        console.log('\n⚠️ КОНФЛИКТЫ ЗАВИСИМОСТЕЙ:');
        result.conflicts.forEach((conflict, index) => {
          console.log(`   ${index + 1}. ${conflict.type}: ${conflict.message}`);
          console.log(`      Серьезность: ${conflict.severity}`);
          console.log(`      Рекомендация: ${conflict.recommendation}`);
        });
      }
      
      // Альтернативы
      if (result.alternatives && Object.keys(result.alternatives).length > 0) {
        console.log('\n🔄 АЛЬТЕРНАТИВНЫЕ ПАКЕТЫ:');
        for (const [packageName, alternatives] of Object.entries(result.alternatives)) {
          console.log(`   📦 Для ${packageName}:`);
          alternatives.slice(0, 3).forEach((alt, index) => {
            console.log(`      ${index + 1}. ${alt.name} (совместимость: ${(alt.relevanceScore * 100).toFixed(1)}%)`);
          });
        }
      }
      
      // Проблемы безопасности
      if (result.securityIssues && result.securityIssues.length > 0) {
        console.log('\n🛡️ ПРОБЛЕМЫ БЕЗОПАСНОСТИ:');
        result.securityIssues.forEach((issue, index) => {
          console.log(`   ${index + 1}. ${issue.type}: ${issue.message}`);
          console.log(`      Серьезность: ${issue.severity}`);
          if (issue.cve) console.log(`      CVE: ${issue.cve}`);
        });
      }
      
      // GPU метрики
      if (result.analysis && result.analysis.processingMetrics) {
        console.log('\n🎮 GPU МЕТРИКИ ПРОИЗВОДИТЕЛЬНОСТИ:');
        const metrics = result.analysis.processingMetrics;
        console.log(`   ⚡ GPU анализ: ${metrics.gpuAnalysisTime || 0}мс`);
        console.log(`   🔄 GPU циклы: ${metrics.gpuCycleTime || 0}мс`);
        console.log(`   🎯 GPU совместимость: ${metrics.gpuCompatibilityTime || 0}мс`);
        console.log(`   📊 Анализ совместимости: ${metrics.compatibilityAnalysisTime || 0}мс`);
        console.log(`   🚀 GPU ускорение: ${result.analysis.gpuAccelerated ? 'АКТИВНО' : 'ОТКЛЮЧЕНО'}`);
      }
      
      // Общая оценка
      console.log('\n🎯 ОБЩАЯ ОЦЕНКА ПРОЕКТА:');
      console.log(`📊 Совместимость: ${result.compatibility.compatibilityScore.toFixed(1)}%`);
      console.log(`⚠️ Уровень риска: ${result.compatibility.riskLevel.toUpperCase()}`);
      console.log(`🔧 Всего проблем: ${result.analysis.totalIssues || 0}`);
      console.log(`💡 Рекомендаций: ${result.recommendations ? result.recommendations.length : 0}`);
      
      const riskColor = result.compatibility.riskLevel === 'low' ? '🟢' : 
                       result.compatibility.riskLevel === 'medium' ? '🟡' : '🔴';
      console.log(`${riskColor} Статус: ${getProjectStatus(result.compatibility.riskLevel, result.compatibility.compatibilityScore)}`);
      
    } else {
      console.log('❌ Не удалось получить результаты анализа');
    }
    
    // 3. Тестируем массовый сбор (опционально)
    console.log('\n🌍 Шаг 3: Тестирование возможности массового сбора...');
    console.log('ℹ️ Массовый сбор всех npm пакетов может занять несколько часов.');
    console.log('ℹ️ Для запуска используйте: POST /api/collect/massive');
    
    console.log('\n🎉 Тестирование массовой системы анализа совместимости завершено!');
    
  } catch (error) {
    console.error('❌ Ошибка тестирования:', error.message);
    if (error.response) {
      console.error('📄 Детали ошибки:', error.response.data);
    }
  }
}

function getProjectStatus(riskLevel, compatibilityScore) {
  if (riskLevel === 'low' && compatibilityScore >= 90) {
    return 'ОТЛИЧНО - проект готов к production';
  } else if (riskLevel === 'low' && compatibilityScore >= 70) {
    return 'ХОРОШО - незначительные проблемы';
  } else if (riskLevel === 'medium') {
    return 'ТРЕБУЕТ ВНИМАНИЯ - есть проблемы совместимости';
  } else {
    return 'КРИТИЧНО - серьезные проблемы совместимости';
  }
}

// Запускаем тест
if (require.main === module) {
  testMassiveCompatibilitySystem().then(() => {
    console.log('\n✅ Тестирование завершено успешно!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
}

module.exports = { testMassiveCompatibilitySystem };
