// Тест сбора нового пакета для проверки исправления ошибки
const axios = require('axios');

async function testNewPackage() {
  console.log('🧪 Тестирование сбора нового пакета...');
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    // Тестируем сбор нового пакета
    console.log('📦 Тестируем сбор пакета express...');
    
    const response = await axios.post(`${baseUrl}/api/collect-package`, {
      packageName: 'express'
    });
    
    if (response.data.success) {
      console.log('✅ Пакет express успешно добавлен в очередь');
      console.log(`📋 Task ID: ${response.data.taskId}`);
      console.log(`🎮 GPU ускорение: ${response.data.gpuAccelerated ? 'активно' : 'отключено'}`);
      
      // Ждем обработки
      console.log('⏳ Ожидаем обработки...');
      await new Promise(resolve => setTimeout(resolve, 10000));
      
      // Проверяем статистику
      console.log('📊 Проверяем статистику...');
      const statsResponse = await axios.get(`${baseUrl}/api/stats`);
      
      if (statsResponse.data) {
        console.log('📈 Статистика системы:');
        console.log(`   📦 Всего пакетов: ${statsResponse.data.totalPackages}`);
        console.log(`   🔍 Анализов сегодня: ${statsResponse.data.todayAnalyses}`);
        console.log(`   ⚡ Скорость анализа: ${statsResponse.data.analysisSpeed.toFixed(2)} пак/сек`);
        console.log(`   🎮 GPU ускорение: ${statsResponse.data.gpuAccelerated ? 'активно' : 'отключено'}`);
      }
      
      // Проверяем список пакетов
      console.log('📋 Проверяем список пакетов...');
      const packagesResponse = await axios.get(`${baseUrl}/api/packages?limit=5`);
      
      if (packagesResponse.data && packagesResponse.data.packages) {
        console.log(`📦 Найдено пакетов: ${packagesResponse.data.packages.length}`);
        packagesResponse.data.packages.forEach((pkg, index) => {
          console.log(`   ${index + 1}. ${pkg.name} v${pkg.latest_version || 'N/A'}`);
          console.log(`      📝 ${pkg.description ? pkg.description.substring(0, 60) + '...' : 'Нет описания'}`);
          console.log(`      📊 Загрузок: ${(pkg.weekly_downloads || 0).toLocaleString()}/неделю`);
        });
      }
      
      console.log('\n✅ Тест сбора нового пакета прошел успешно!');
      console.log('🎉 Все ошибки с базой данных исправлены!');
      
    } else {
      console.log('❌ Ошибка сбора пакета:', response.data.error);
    }
    
  } catch (error) {
    console.error('❌ Ошибка тестирования:', error.message);
    
    if (error.response) {
      console.error('📄 Детали ошибки:', error.response.data);
    }
    
    // Проверяем, связана ли ошибка с базой данных
    if (error.message.includes('SQLITE_ERROR') || error.message.includes('no column named')) {
      console.error('🔧 Обнаружена ошибка базы данных. Требуется дополнительное исправление.');
    }
  }
}

// Запускаем тест
if (require.main === module) {
  testNewPackage().then(() => {
    console.log('\n🏁 Тестирование завершено');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
}

module.exports = { testNewPackage };
