// Тест сбора пакета для проверки исправления ошибки с колонкой author
const axios = require('axios');

async function testPackageCollection() {
  console.log('🧪 Тестирование сбора пакета...');
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    // Тестируем сбор популярного пакета
    console.log('📦 Тестируем сбор пакета lodash...');
    
    const response = await axios.post(`${baseUrl}/api/collect-package`, {
      packageName: 'lodash'
    });
    
    if (response.data.success) {
      console.log('✅ Пакет lodash успешно добавлен в очередь');
      console.log(`📋 Task ID: ${response.data.taskId}`);
      console.log(`🎮 GPU ускорение: ${response.data.gpuAccelerated ? 'активно' : 'отключено'}`);
      
      // Ждем немного для обработки
      console.log('⏳ Ожидаем обработки...');
      await new Promise(resolve => setTimeout(resolve, 5000));
      
      // Проверяем статистику
      console.log('📊 Проверяем статистику...');
      const statsResponse = await axios.get(`${baseUrl}/api/stats`);
      
      if (statsResponse.data) {
        console.log('📈 Статистика системы:');
        console.log(`   📦 Всего пакетов: ${statsResponse.data.totalPackages}`);
        console.log(`   🔍 Анализов сегодня: ${statsResponse.data.todayAnalyses}`);
        console.log(`   ⚡ Скорость анализа: ${statsResponse.data.analysisSpeed.toFixed(2)} пак/сек`);
        console.log(`   🎮 GPU ускорение: ${statsResponse.data.gpuAccelerated ? 'активно' : 'отключено'}`);
      }
      
      console.log('\n✅ Тест сбора пакета прошел успешно!');
      console.log('🎉 Ошибка с колонкой author исправлена!');
      
    } else {
      console.log('❌ Ошибка сбора пакета:', response.data.error);
    }
    
  } catch (error) {
    console.error('❌ Ошибка тестирования:', error.message);
    
    if (error.response) {
      console.error('📄 Детали ошибки:', error.response.data);
    }
    
    // Проверяем, связана ли ошибка с базой данных
    if (error.message.includes('SQLITE_ERROR') || error.message.includes('no column named')) {
      console.error('🔧 Обнаружена ошибка базы данных. Требуется исправление схемы.');
    }
  }
}

// Запускаем тест
if (require.main === module) {
  testPackageCollection().then(() => {
    console.log('\n🏁 Тестирование завершено');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
}

module.exports = { testPackageCollection };
