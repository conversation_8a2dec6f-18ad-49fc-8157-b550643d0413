{"name": "test-project", "version": "1.0.0", "description": "Тестовый проект для проверки анализатора пакетов", "main": "index.js", "scripts": {"start": "node index.js", "test": "jest", "build": "webpack --mode production", "dev": "webpack-dev-server --mode development"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "express": "^4.18.2", "lodash": "^4.17.21", "axios": "^1.6.2", "moment": "^2.29.4", "uuid": "^9.0.1", "chalk": "^5.4.1"}, "devDependencies": {"webpack": "^5.89.0", "webpack-cli": "^5.1.4", "webpack-dev-server": "^4.15.1", "babel-loader": "^9.1.3", "@babel/core": "^7.23.6", "@babel/preset-env": "^7.23.6", "@babel/preset-react": "^7.23.3", "jest": "^29.7.0", "eslint": "^8.56.0", "prettier": "^3.1.1"}, "keywords": ["test", "package", "analyzer", "dependencies"], "author": "Package Analyzer Team", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/test/test-project.git"}, "bugs": {"url": "https://github.com/test/test-project/issues"}, "homepage": "https://github.com/test/test-project#readme"}