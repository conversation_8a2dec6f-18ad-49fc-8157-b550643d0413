// Простой тест анализа через сервер
const axios = require('axios');

async function testServerAnalyze() {
  console.log('🧪 Тестирование анализа через сервер...');
  
  const testPackageJson = {
    "name": "test-security-project",
    "version": "1.0.0",
    "dependencies": {
      "moment": "^2.24.0",     // Устаревшая версия с уязвимостями
      "lodash": "^4.17.15",    // Старая версия с уязвимостями
      "express": "^4.16.0",    // Устаревшая версия
      "axios": "^0.19.0",      // Старая версия
      "react": "^16.8.0"       // Устаревшая версия
    },
    "devDependencies": {
      "jest": "^24.0.0",       // Устаревшая версия
      "eslint": "^6.0.0"       // Устаревшая версия
    }
  };
  
  try {
    console.log('📊 Отправляем запрос на анализ...');
    
    const response = await axios.post('http://localhost:3000/api/analyze', {
      filePath: 'test-package.json',
      content: JSON.stringify(testPackageJson, null, 2)
    }, {
      timeout: 60000, // 60 секунд
      headers: {
        'Content-Type': 'application/json'
      }
    });
    
    console.log('✅ Получен ответ от сервера');
    console.log('📊 Статус:', response.status);
    console.log('📋 Размер ответа:', JSON.stringify(response.data).length, 'символов');
    
    const result = response.data;
    
    if (result.fixedCode && result.originalCode) {
      console.log('\n🛠️ ИСПРАВЛЕННЫЙ КОД СГЕНЕРИРОВАН:');
      console.log('📝 Оригинальный код:', result.originalCode.length, 'символов');
      console.log('🔧 Исправленный код:', result.fixedCode.length, 'символов');
      
      console.log('\n📋 ОРИГИНАЛЬНЫЙ КОД:');
      console.log(result.originalCode);
      
      console.log('\n🛠️ ИСПРАВЛЕННЫЙ КОД:');
      console.log(result.fixedCode);
      
      if (result.fixStats) {
        console.log('\n📊 СТАТИСТИКА ИСПРАВЛЕНИЙ:');
        console.log('🔧 Всего исправлений:', result.fixStats.totalFixes || 0);
        console.log('🛡️ Безопасности:', result.fixStats.securityFixes || 0);
        console.log('🔄 Совместимости:', result.fixStats.compatibilityFixes || 0);
        console.log('📈 Обновлений:', result.fixStats.updateFixes || 0);
        console.log('🔄 Альтернатив:', result.fixStats.alternativeFixes || 0);
      }
      
      if (result.fixReport && result.fixReport.fixes) {
        console.log('\n📋 ОТЧЕТ ОБ ИСПРАВЛЕНИЯХ:');
        console.log('📝 Детальных исправлений:', result.fixReport.fixes.length);
        
        result.fixReport.fixes.slice(0, 3).forEach((fix, index) => {
          console.log(`\n${index + 1}. ${fix.package} (${fix.category.toUpperCase()})`);
          console.log(`   📝 ${fix.description}`);
          console.log(`   🔧 ${fix.reason}`);
          if (fix.originalVersion && fix.newVersion) {
            console.log(`   📦 ${fix.originalVersion} → ${fix.newVersion}`);
          }
          console.log(`   ⚠️ Влияние: ${fix.impact?.toUpperCase() || 'LOW'}`);
        });
        
        if (result.fixReport.fixes.length > 3) {
          console.log(`\n... и еще ${result.fixReport.fixes.length - 3} исправлений`);
        }
      }
      
      if (result.vulnerabilities && result.vulnerabilities.length > 0) {
        console.log('\n🛡️ УЯЗВИМОСТИ:');
        console.log('📝 Найдено уязвимостей:', result.vulnerabilities.length);
        
        result.vulnerabilities.slice(0, 3).forEach((vuln, index) => {
          console.log(`\n${index + 1}. ${vuln.package || vuln.name} (${vuln.severity?.toUpperCase() || 'UNKNOWN'})`);
          console.log(`   📝 ${vuln.title || 'Уязвимость'}`);
          console.log(`   🔧 Уязвимые версии: ${vuln.vulnerable_versions || 'N/A'}`);
          console.log(`   ✅ Исправлено в: ${vuln.patched_versions || vuln.fixedVersion || 'Не исправлено'}`);
        });
        
        if (result.vulnerabilities.length > 3) {
          console.log(`\n... и еще ${result.vulnerabilities.length - 3} уязвимостей`);
        }
      }
      
      if (result.enhancedRecommendations) {
        console.log('\n💡 УЛУЧШЕННЫЕ РЕКОМЕНДАЦИИ:');
        console.log('📋 Приоритетных рекомендаций:', result.enhancedRecommendations.length);
        
        result.enhancedRecommendations.forEach((rec, index) => {
          const priority = rec.priority === 'critical' ? '🔴' : 
                         rec.priority === 'high' ? '🟠' : 
                         rec.priority === 'medium' ? '🟡' : '🟢';
          console.log(`\n${priority} ${rec.title} (${rec.priority.toUpperCase()})`);
          console.log(`   📝 ${rec.description}`);
          console.log(`   🎯 ${rec.action}`);
        });
      }
      
      console.log('\n✅ ТЕСТ УСПЕШНО ПРОЙДЕН!');
      console.log('🎉 Сервер корректно генерирует исправленный код и отчеты');
      
    } else {
      console.log('❌ Исправленный код НЕ сгенерирован');
      console.log('📋 Полный ответ сервера:');
      console.log(JSON.stringify(result, null, 2));
    }
    
  } catch (error) {
    console.error('❌ Ошибка тестирования:', error.message);
    
    if (error.response) {
      console.error('📄 Статус ошибки:', error.response.status);
      console.error('📄 Данные ошибки:', error.response.data);
    }
    
    if (error.code === 'ECONNREFUSED') {
      console.error('🔌 Сервер не запущен на порту 3000');
    }
  }
}

// Запускаем тест
if (require.main === module) {
  testServerAnalyze().then(() => {
    console.log('\n🏁 Тестирование завершено!');
    process.exit(0);
  }).catch(error => {
    console.error('💥 Критическая ошибка:', error);
    process.exit(1);
  });
}

module.exports = { testServerAnalyze };
