// Простой тест анализа совместимости
const axios = require('axios');

async function testSimpleAnalysis() {
  console.log('🎯 Простой тест анализа совместимости...');
  
  const baseUrl = 'http://localhost:3000';
  
  try {
    const testPackageJson = {
      "name": "simple-test-project",
      "version": "1.0.0",
      "dependencies": {
        "lodash": "^4.17.21",
        "express": "^4.18.0"
      },
      "devDependencies": {
        "jest": "^29.0.0"
      }
    };
    
    console.log('📊 Запускаем анализ...');
    
    const response = await axios.post(`${baseUrl}/api/analyze`, {
      filePath: 'test-package.json',
      content: JSON.stringify(testPackageJson, null, 2)
    });
    
    console.log('✅ Анализ завершен');
    console.log('📋 Результат:', JSON.stringify(response.data, null, 2));
    
  } catch (error) {
    console.error('❌ Ошибка:', error.message);
    if (error.response) {
      console.error('📄 Детали:', error.response.data);
    }
  }
}

testSimpleAnalysis();
